## 现在有个API能获取某个IP或者CIDR的信息，详细示例如下：
请求API：
<api>
https://ipapi.co/{ip}/json
</api>
得到返回结果：
<response>
{
    "ip": "*************",
    "network": "***********/24",
    "version": "IPv4",
    "city": "Serpong",
    "region": "West Java",
    "region_code": "JB",
    "country": "ID",
    "country_name": "Indonesia",
    "country_code": "ID",
    "country_code_iso3": "IDN",
    "country_capital": "Jakarta",
    "country_tld": ".id",
    "continent_code": "AS",
    "in_eu": false,
    "postal": null,
    "latitude": -6.31694,
    "longitude": 106.66417,
    "timezone": "Asia/Jakarta",
    "utc_offset": "+0700",
    "country_calling_code": "+62",
    "currency": "IDR",
    "currency_name": "Rupiah",
    "languages": "id,en,nl,jv",
    "country_area": 1919440.0,
    "country_population": 267663435,
    "asn": "AS137342",
    "org": "DISKOMINFO TANGERANG SELATAN"
}
</response>
返回结果如果包含network CIDR范围，那么这个范围的IP应该都可以通用这个response信息。
response中字段可能和本项目定义的model不一致，请仔细检查对应字段。
当前项目数据库中，应该有很多IP信息缺失的行，请帮我实现功能：
1. 通过调用API，补全这些IP或者CIDR的各种信息。
2. 提供代理功能，假设代理信息在 /proxy/proxy.txt 内容例子如下：
```
http://***********:59771
https://*************:80
socks4://*************:8008
socks5://*************:3128
```
3. API调用可能会有类似429之类的报错，如果报错了，那么请轮换代理，直到所有代理都不可用则报错退出程序。
4. 注意合理处理CIDR，IP有交叉的情况。
5. 用你认为合理，最佳的方式实现，可以是通过xxxx 命令，或者是通过API端口触发。
6. 你实现的逻辑应该尽可能独立。
7. 不应该影响现有代码逻辑。
8. 查看当前数据库表结构、字段、分析出最佳策略。
9. 服务已经通过nodemon --exec go run cmd/ipInsight/main.go --signal SIGTERM运行，会自动热更新

-----------------------------------------------------------------------------------------------
现在有2个API能获取某个IP或者CIDR的信息，详细示例如下：
```
1. ipapi.co
请求API：
<api>
https://ipapi.co/{ip}/json
</api>
得到返回结果：
<response>
{
    "ip": "*************",
    "network": "***********/24",
    "version": "IPv4",
    "city": "Serpong",
    "region": "West Java",
    "region_code": "JB",
    "country": "ID",
    "country_name": "Indonesia",
    "country_code": "ID",
    "country_code_iso3": "IDN",
    "country_capital": "Jakarta",
    "country_tld": ".id",
    "continent_code": "AS",
    "in_eu": false,
    "postal": null,
    "latitude": -6.31694,
    "longitude": 106.66417,
    "timezone": "Asia/Jakarta",
    "utc_offset": "+0700",
    "country_calling_code": "+62",
    "currency": "IDR",
    "currency_name": "Rupiah",
    "languages": "id,en,nl,jv",
    "country_area": 1919440.0,
    "country_population": 267663435,
    "asn": "AS137342",
    "org": "DISKOMINFO TANGERANG SELATAN"
}
</response>
如果直接请求url：
<api>
https://ipapi.co/json
</api>
则返回请求端的IP信息。
2. api.ipapi.is
请求API：
<api>
https://api.ipapi.is/?q=************
</api>
得到返回结果：
<response>
{
  "ip": "************",
  "rir": "ARIN",
  "is_bogon": false,
  "is_mobile": false,
  "is_satellite": false,
  "is_crawler": false,
  "is_datacenter": true,
  "is_tor": false,
  "is_proxy": false,
  "is_vpn": true,
  "is_abuser": false,
  "datacenter": {
    "datacenter": "amazonaws.com",
    "network": "**********/19",
    "country": "FR",
    "region": "FR-75C",
    "city": "Paris"
  },
  "company": {
    "name": "Amazon Technologies Inc.",
    "abuser_score": "0.0005 (Very Low)",
    "domain": "aws.amazon.com",
    "type": "hosting",
    "network": "******** - *************",
    "whois": "https://api.ipapi.is/?whois=********"
  },
  "abuse": {
    "name": "Amazon Technologies Inc.",
    "address": "410 Terry Ave N., Seattle, WA, 98109, US",
    "email": "<EMAIL>",
    "phone": "******-555-0000"
  },
  "asn": {
    "asn": 16509,
    "abuser_score": "0.0001 (Very Low)",
    "route": "**********/20",
    "descr": "AMAZON-02, US",
    "country": "us",
    "active": true,
    "org": "Amazon.com, Inc.",
    "domain": "amazon.com",
    "abuse": "<EMAIL>",
    "type": "hosting",
    "created": "2000-05-04",
    "updated": "2012-03-02",
    "rir": "ARIN",
    "whois": "https://api.ipapi.is/?whois=AS16509"
  },
  "location": {
    "is_eu_member": true,
    "calling_code": "33",
    "currency_code": "EUR",
    "continent": "EU",
    "country": "France",
    "country_code": "FR",
    "state": "Île-de-France",
    "city": "Paris",
    "latitude": 48.85341,
    "longitude": 2.3488,
    "zip": "75998 CEDEX 14",
    "timezone": "Europe/Paris",
    "local_time": "2025-07-29T14:15:00+02:00",
    "local_time_unix": 1753791300,
    "is_dst": true
  },
  "elapsed_ms": 0.25
}
</response>
如果直接请求url：
<api>
https://api.ipapi.is
</api>
则返回请求端的IP信息。
```
请根据两个示例，帮我改造 @internal/ipcompletion 服务。
目前ipcompletion 服务仅支持ipapi.co服务，增加对api.ipapi.is的支持。
注意对应字段的解析与赋值。如果其中一个请求成功了，就不再请求另一个API，也不需要替换代理。
尽可能不影响原有功能的情况下，简洁的实现修改。
-----------------------------------------------------------------------------------------------
请帮我优化当前项目的前端 @dashboard
1. 左侧菜单栏支持折叠/展开
2. 右侧内容区域的 header 高度应该和左侧菜单栏 header 高度一致
3. 帮我为左侧菜单栏设计个优美的 LOGO
4. 左侧菜单栏是否可以增加一个菜单：让用户可以调用当前项目的 API，并且美化/人类易读方式展示返回结果
5. 检查前端项目每个页面的风格，边距，padding，字体，等等样式是否统一，合理。如果没有，请优化

-----------------------------------------------------------------------------------------------
当前项目为一个基于 golang 编写的 IP 数据库项目，当前目录即为工程目录。项目提供了 RESTful API 服务，用于查询 IP 地理位置信息。项目程序入口位于：@cmd/ipInsight/main.go，提供 HTTP 服务的核心代码位于：@internal/api/api.go。
请阅读项目源码，然后帮我做以下事情：
1. 仔细分析源码，然后提供优化建议。
2. 遵循行业最佳实践，但不需要过度涉及。
3. 增加代码可读性，可扩展性，但不要过度设计，增加心智负担。
4. 按照以上建议，优化项目代码。


-----------------------------------------------------------------------------------------------
当前项目为一个基于 golang 编写的 IP 数据库项目，当前目录即为工程目录。项目提供了 RESTful API 服务，用于查询 IP 地理位置信息。项目程序入口位于：@cmd/ipInsight/main.go，提供 HTTP 服务的核心代码位于：@internal/api/api.go。
请阅读项目源码，特别是 @internal/api/api.go 源码，然后帮我输出一份 API 接口文档到 docs/api.md。
**核心目的**：
- 这份文档请尽可能详细，包含所有接口的详细信息，包括请求参数、返回值、错误处理等。
- 通过这份详细文档，能够构建一个完整的 IP 数据库看板 web 应用程序。



-----------------------------------------------------------------------------------------------
帮我优化一下 
程序入口文件 ：@cmd/ipInsight/main.go 和
API 服务文件：@internal/api/api.go
目前这两个文件代码内容有点冗杂，看起来不够美观、简洁。

注意 ：不需要过度设计，优化尽量简洁点。



-----------------------------------------------------------------------------------------------
@dashboard 这是我的前端项目，基于 React + TypeScript + Vite 构建。
UI 组件库使用的是 Material-UI。
但是我在使用中发现Material-UI并不能满足我的需求，于是我准备改用： shadcn/ui + Tailwind CSS。
请帮我检查当前前端项目代码，将所有样式相关的代码都迁移到 shadcn/ui + Tailwind CSS 的设计系统上，然后帮我优化代码，提升代码质量，同时保持功能不变。
**重要**：不要遗漏，所有Material-UI样式相关的代码都要迁移到 shadcn/ui + Tailwind CSS 的设计系统上。


-----------------------------------------------------------------------------------------------
这是我的`IP数据库`项目的前端: @/Users/<USER>/Desktop/conan-work/ipInsight/dashboard/ 基于 vite+react+tailwindcss+@radix-ui 技术栈。
但是我不满意现在的 UI 表现。
请帮我重构当前 UI。
当前技术栈可以不变。

UI 设计要求：
- 美观：采用现代、简约风格，颜色方案以中性色为主（蓝灰调），字体清晰。
- 易用性：交互友好，支持键盘导航，表单验证即时反馈。
- 跨平台和响应式：适配桌面、平板、手机，使用 Tailwind 的响应式类，确保在不同设备上布局自适应。
- 交互：按钮有 hover 效果，加载时显示 spinner，结果动画淡入。



-----------------------------------------------------------------------------------------------
将 @/Users/<USER>/Desktop/conan-work/ipInsight/dashboard/src/components/ui/ 中所有组件删除（shadcn 没有的除外），改为通过 pnpm dlx shadcn@latest 方式添加


-----------------------------------------------------------------------------------------------
这是我的`IP数据库`项目的前端: @/Users/<USER>/Desktop/conan-work/ipInsight/dashboard/ 基于 vite+react+tailwindcss+@radix-ui 技术栈。
但是我不满意现在的 UI 表现。
请参考这个页面：`https://shadcn-admin.netlify.app/`. 帮我将前端UI按照这个页面实现。


-----------------------------------------------------------------------------------------------

我有一个基础前端模版：@ipinsight-admin，现在我需要基于这个模版，实现一个 IP 数据库管理看板。
样式，组件，布局等应该参考模版，有类似的可以复用，没有的请参考模版，然后实现。
这是实现看板的 README.md 文件：@draft/README.md。
这是实现看板的一些 API 接口文件：@draft/services/api.ts(可以参考，也可以更新优化)
这是实现看板的一些 API 类型定义：@draft/types/api.ts(可以参考，也可以更新优化)
实现后的项目应该只有@draft/README.md中提到的功能或者你认为应该添加的功能，而模板只带的众多功能，页面，路由等，用不上的内容可以直接删除。
请帮我实现这个 IP 数据库管理看板。




-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------




-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------




-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------





-----------------------------------------------------------------------------------------------




-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------




-----------------------------------------------------------------------------------------------




-----------------------------------------------------------------------------------------------



-----------------------------------------------------------------------------------------------

