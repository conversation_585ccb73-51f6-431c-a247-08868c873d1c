#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/knip@5.62.0_@types+node@24.3.0_typescript@5.9.2/node_modules/knip/bin/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/knip@5.62.0_@types+node@24.3.0_typescript@5.9.2/node_modules/knip/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/knip@5.62.0_@types+node@24.3.0_typescript@5.9.2/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/knip@5.62.0_@types+node@24.3.0_typescript@5.9.2/node_modules/knip/bin/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/knip@5.62.0_@types+node@24.3.0_typescript@5.9.2/node_modules/knip/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/knip@5.62.0_@types+node@24.3.0_typescript@5.9.2/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../knip/bin/knip.js" "$@"
else
  exec node  "$basedir/../knip/bin/knip.js" "$@"
fi
