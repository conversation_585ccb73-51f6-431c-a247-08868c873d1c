import {
  require_react
} from "./chunk-575JY5N6.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/.pnpm/react-top-loading-bar@3.0.2_react@19.1.1/node_modules/react-top-loading-bar/dist/index.mjs
var l = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);
var G = () => {
};
function Y(e, t, m) {
  let a = (0, import_react.useRef)(G);
  (0, import_react.useEffect)(() => {
    a.current = e;
  }), (0, import_react.useEffect)(() => {
  }, [m]), (0, import_react.useEffect)(() => {
    if (t === null || t === false) return;
    let b = setInterval(() => a.current(), t);
    return () => clearInterval(b);
  }, [t]);
}
function k(e, t) {
  return Math.random() * (t - e + 1) + e;
}
function h(e, t) {
  return Math.floor(k(e, t));
}
var Q = (0, import_react.forwardRef)(({ progress: e, height: t = 2, className: m = "", color: a = "red", background: d = "transparent", onLoaderFinished: b, transitionTime: n = 300, loaderSpeed: i = 500, waitingTime: N = 1e3, shadow: y = true, containerStyle: T = {}, style: E = {}, shadowStyle: O = {}, containerClassName: z = "" }, C) => {
  let S = (0, import_react.useRef)(false), [p, c] = (0, import_react.useState)(0), f = (0, import_react.useRef)({ active: false, refreshRate: 1e3 }), [R, I] = (0, import_react.useState)({ active: false, value: 60 }), F = { height: "100%", background: a, transition: `all ${i}ms ease`, width: "0%" }, H = { position: "fixed", top: 0, left: 0, height: t, background: d, zIndex: 99999999999, width: "100%" }, j = { boxShadow: `0 0 10px ${a}, 0 0 10px ${a}`, width: "5%", opacity: 1, position: "absolute", height: "100%", transition: `all ${i}ms ease`, transform: "rotate(2deg) translate(0px, -2px)", left: "-10rem" }, [g, P] = (0, import_react.useState)(F), [w, L] = (0, import_react.useState)(j);
  (0, import_react.useEffect)(() => (S.current = true, () => {
    S.current = false;
  }), []), (0, import_react.useImperativeHandle)(C, () => ({ continuousStart(r, o = 1e3) {
    if (R.active) return;
    if (e !== void 0) {
      console.warn("react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!");
      return;
    }
    let s = r || h(10, 20);
    f.current = { active: true, refreshRate: o }, c(s), u(s);
  }, staticStart(r) {
    if (f.current.active) return;
    if (e !== void 0) {
      console.warn("react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!");
      return;
    }
    let o = r || h(30, 60);
    I({ active: true, value: o }), c(o), u(o);
  }, start(r = "continuous", o, s) {
    if (e !== void 0) {
      console.warn("react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!");
      return;
    }
    r === "continuous" ? f.current = { active: true, refreshRate: s || 1e3 } : I({ active: true, value: o || 20 });
    let q = h(10, 20), A = h(30, 70), V = o || (r === "continuous" ? q : A);
    c(V), u(V);
  }, complete() {
    if (e !== void 0) {
      console.warn("react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!");
      return;
    }
    c(100), u(100);
  }, increase(r) {
    if (e !== void 0) {
      console.warn("react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!");
      return;
    }
    c((o) => {
      let s = o + r;
      return u(s), s;
    });
  }, decrease(r) {
    if (e !== void 0) {
      console.warn("react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!");
      return;
    }
    c((o) => {
      let s = o - r;
      return u(s), s;
    });
  }, getProgress() {
    return p;
  } })), (0, import_react.useEffect)(() => {
    P({ ...g, background: a }), L({ ...w, boxShadow: `0 0 10px ${a}, 0 0 5px ${a}` });
  }, [a]), (0, import_react.useEffect)(() => {
    if (C) {
      if (C && e !== void 0) {
        console.warn(`react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar! Please use only props or only ref methods! Ref methods will override props if "ref" property is available.`);
        return;
      }
      u(p);
    } else e && u(e);
  }, [e]);
  let u = (r) => {
    r >= 100 ? (P({ ...g, width: "100%" }), y && L({ ...w, left: r - 10 + "%" }), setTimeout(() => {
      S.current && (P({ ...g, opacity: 0, width: "100%", transition: `all ${n}ms ease-out`, color: a }), setTimeout(() => {
        S.current && (f.current.active && (f.current = { ...f.current, active: false }, c(0), u(0)), R.active && (I({ ...R, active: false }), c(0), u(0)), b && b(), c(0), u(0));
      }, n));
    }, N)) : (P((o) => ({ ...o, width: r + "%", opacity: 1, transition: r > 0 ? `all ${i}ms ease` : "" })), y && L({ ...w, left: r - 5.5 + "%", transition: r > 0 ? `all ${i}ms ease` : "" }));
  };
  return Y(() => {
    let r = Math.min(10, (100 - p) / 5), o = Math.min(20, (100 - p) / 3), s = k(r, o);
    p + s < 95 && (c(p + s), u(p + s));
  }, f.current.active ? f.current.refreshRate : null), l.createElement("div", { className: z, style: { ...H, ...T } }, l.createElement("div", { className: m, style: { ...g, ...E } }, y ? l.createElement("div", { style: { ...w, ...O } }) : null));
});
var M = l.createContext(void 0);
var re = ({ children: e, props: t }) => {
  let [m, a] = (0, import_react.useState)(t || {}), d = (0, import_react.useRef)(null), b = (n = "continuous") => {
    var i;
    return (i = d.current) == null ? void 0 : i.start(n);
  };
  return l.createElement(M.Provider, { value: { start: b, complete: () => {
    var n;
    return (n = d.current) == null ? void 0 : n.complete();
  }, getProgress: () => {
    var n;
    return ((n = d.current) == null ? void 0 : n.getProgress()) || 0;
  }, increase: (n) => {
    var i;
    return (i = d.current) == null ? void 0 : i.increase(n);
  }, decrease: (n) => {
    var i;
    return (i = d.current) == null ? void 0 : i.decrease(n);
  }, setProps: (n) => a({ ...n, ...m }) } }, l.createElement(Q, { ref: d, ...m }), e);
};
var ne = (e) => {
  let t = l.useContext(M);
  if (!t) throw new Error("[react-top-loading-bar] useLoadingBar hook must be used within a LoadingBarContainer. Try wrapping parent component in <LoadingBarContainer>.");
  return (0, import_react.useEffect)(() => {
    e && t.setProps(e);
  }, []), { start: t.start, complete: t.complete, increase: t.increase, decrease: t.decrease, getProgress: t.getProgress };
};
export {
  re as LoadingBarContainer,
  Q as default,
  ne as useLoadingBar
};
//# sourceMappingURL=react-top-loading-bar.js.map
