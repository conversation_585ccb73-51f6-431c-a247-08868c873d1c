{"version": 3, "sources": ["../../.pnpm/input-otp@1.4.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/input-otp/src/input.tsx", "../../.pnpm/input-otp@1.4.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/input-otp/src/sync-timeouts.ts", "../../.pnpm/input-otp@1.4.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/input-otp/src/use-previous.ts", "../../.pnpm/input-otp@1.4.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/input-otp/src/use-pwm-badge.tsx", "../../.pnpm/input-otp@1.4.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/input-otp/src/regexp.ts"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\n\nimport { syncTimeouts } from './sync-timeouts'\nimport { OTPInputProps, RenderProps } from './types'\nimport { usePrevious } from './use-previous'\nimport { usePasswordManagerBadge } from './use-pwm-badge'\n\nexport const OTPInputContext = React.createContext<RenderProps>(\n  {} as RenderProps,\n)\n\nexport const OTPInput = React.forwardRef<HTMLInputElement, OTPInputProps>(\n  (\n    {\n      value: uncheckedValue,\n      onChange: uncheckedOnChange,\n      maxLength,\n      textAlign = 'left',\n      pattern,\n      placeholder,\n      inputMode = 'numeric',\n      onComplete,\n      pushPasswordManagerStrategy = 'increase-width',\n      pasteTransformer,\n      containerClassName,\n      noScriptCSSFallback = NOSCRIPT_CSS_FALLBACK,\n\n      render,\n      children,\n\n      ...props\n    },\n    ref,\n  ) => {\n    // Only used when `value` state is not provided\n    const [internalValue, setInternalValue] = React.useState(\n      typeof props.defaultValue === 'string' ? props.defaultValue : '',\n    )\n\n    // Definitions\n    const value = uncheckedValue ?? internalValue\n    const previousValue = usePrevious(value)\n    const onChange = React.useCallback(\n      (newValue: string) => {\n        uncheckedOnChange?.(newValue)\n        setInternalValue(newValue)\n      },\n      [uncheckedOnChange],\n    )\n    const regexp = React.useMemo(\n      () =>\n        pattern\n          ? typeof pattern === 'string'\n            ? new RegExp(pattern)\n            : pattern\n          : null,\n      [pattern],\n    )\n\n    /** useRef */\n    const inputRef = React.useRef<HTMLInputElement>(null)\n    const containerRef = React.useRef<HTMLDivElement>(null)\n    const initialLoadRef = React.useRef({\n      value,\n      onChange,\n      isIOS:\n        typeof window !== 'undefined' &&\n        window?.CSS?.supports?.('-webkit-touch-callout', 'none'),\n    })\n    const inputMetadataRef = React.useRef<{\n      prev: [number | null, number | null, 'none' | 'forward' | 'backward']\n    }>({\n      prev: [\n        inputRef.current?.selectionStart,\n        inputRef.current?.selectionEnd,\n        inputRef.current?.selectionDirection,\n      ],\n    })\n    React.useImperativeHandle(ref, () => inputRef.current, [])\n    React.useEffect(() => {\n      const input = inputRef.current\n      const container = containerRef.current\n\n      if (!input || !container) {\n        return\n      }\n\n      // Sync input value\n      if (initialLoadRef.current.value !== input.value) {\n        initialLoadRef.current.onChange(input.value)\n      }\n\n      // Previous selection\n      inputMetadataRef.current.prev = [\n        input.selectionStart,\n        input.selectionEnd,\n        input.selectionDirection,\n      ]\n      function onDocumentSelectionChange() {\n        if (document.activeElement !== input) {\n          setMirrorSelectionStart(null)\n          setMirrorSelectionEnd(null)\n          return\n        }\n\n        // Aliases\n        const _s = input.selectionStart\n        const _e = input.selectionEnd\n        const _dir = input.selectionDirection\n        const _ml = input.maxLength\n        const _val = input.value\n        const _prev = inputMetadataRef.current.prev\n\n        // Algorithm\n        let start = -1\n        let end = -1\n        let direction: 'forward' | 'backward' | 'none' = undefined\n        if (_val.length !== 0 && _s !== null && _e !== null) {\n          const isSingleCaret = _s === _e\n          const isInsertMode = _s === _val.length && _val.length < _ml\n\n          if (isSingleCaret && !isInsertMode) {\n            const c = _s\n            if (c === 0) {\n              start = 0\n              end = 1\n              direction = 'forward'\n            } else if (c === _ml) {\n              start = c - 1\n              end = c\n              direction = 'backward'\n            } else if (_ml > 1 && _val.length > 1) {\n              let offset = 0\n              if (_prev[0] !== null && _prev[1] !== null) {\n                direction = c < _prev[1] ? 'backward' : 'forward'\n                const wasPreviouslyInserting =\n                  _prev[0] === _prev[1] && _prev[0] < _ml\n                if (direction === 'backward' && !wasPreviouslyInserting) {\n                  offset = -1\n                }\n              }\n\n              start = offset + c\n              end = offset + c + 1\n            }\n          }\n\n          if (start !== -1 && end !== -1 && start !== end) {\n            inputRef.current.setSelectionRange(start, end, direction)\n          }\n        }\n\n        // Finally, update the state\n        const s = start !== -1 ? start : _s\n        const e = end !== -1 ? end : _e\n        const dir = direction ?? _dir\n        setMirrorSelectionStart(s)\n        setMirrorSelectionEnd(e)\n        // Store the previous selection value\n        inputMetadataRef.current.prev = [s, e, dir]\n      }\n      document.addEventListener('selectionchange', onDocumentSelectionChange, {\n        capture: true,\n      })\n\n      // Set initial mirror state\n      onDocumentSelectionChange()\n      document.activeElement === input && setIsFocused(true)\n\n      // Apply needed styles\n      if (!document.getElementById('input-otp-style')) {\n        const styleEl = document.createElement('style')\n        styleEl.id = 'input-otp-style'\n        document.head.appendChild(styleEl)\n\n        if (styleEl.sheet) {\n          const autofillStyles =\n            'background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;'\n\n          safeInsertRule(\n            styleEl.sheet,\n            '[data-input-otp]::selection { background: transparent !important; color: transparent !important; }',\n          )\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp]:autofill { ${autofillStyles} }`,\n          )\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp]:-webkit-autofill { ${autofillStyles} }`,\n          )\n          // iOS\n          safeInsertRule(\n            styleEl.sheet,\n            `@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }`,\n          )\n          // PWM badges\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp] + * { pointer-events: all !important; }`,\n          )\n        }\n      }\n      // Track root height\n      const updateRootHeight = () => {\n        if (container) {\n          container.style.setProperty(\n            '--root-height',\n            `${input.clientHeight}px`,\n          )\n        }\n      }\n      updateRootHeight()\n      const resizeObserver = new ResizeObserver(updateRootHeight)\n      resizeObserver.observe(input)\n\n      return () => {\n        document.removeEventListener(\n          'selectionchange',\n          onDocumentSelectionChange,\n          { capture: true },\n        )\n        resizeObserver.disconnect()\n      }\n    }, [])\n\n    /** Mirrors for UI rendering purpose only */\n    const [isHoveringInput, setIsHoveringInput] = React.useState(false)\n    const [isFocused, setIsFocused] = React.useState(false)\n    const [mirrorSelectionStart, setMirrorSelectionStart] = React.useState<\n      number | null\n    >(null)\n    const [mirrorSelectionEnd, setMirrorSelectionEnd] = React.useState<\n      number | null\n    >(null)\n\n    /** Effects */\n    React.useEffect(() => {\n      syncTimeouts(() => {\n        // Forcefully remove :autofill state\n        inputRef.current?.dispatchEvent(new Event('input'))\n\n        // Update the selection state\n        const s = inputRef.current?.selectionStart\n        const e = inputRef.current?.selectionEnd\n        const dir = inputRef.current?.selectionDirection\n        if (s !== null && e !== null) {\n          setMirrorSelectionStart(s)\n          setMirrorSelectionEnd(e)\n          inputMetadataRef.current.prev = [s, e, dir]\n        }\n      })\n    }, [value, isFocused])\n\n    React.useEffect(() => {\n      if (previousValue === undefined) {\n        return\n      }\n\n      if (\n        value !== previousValue &&\n        previousValue.length < maxLength &&\n        value.length === maxLength\n      ) {\n        onComplete?.(value)\n      }\n    }, [maxLength, onComplete, previousValue, value])\n\n    const pwmb = usePasswordManagerBadge({\n      containerRef,\n      inputRef,\n      pushPasswordManagerStrategy,\n      isFocused,\n    })\n\n    /** Event handlers */\n    const _changeListener = React.useCallback(\n      (e: React.ChangeEvent<HTMLInputElement>) => {\n        const newValue = e.currentTarget.value.slice(0, maxLength)\n        if (newValue.length > 0 && regexp && !regexp.test(newValue)) {\n          e.preventDefault()\n          return\n        }\n        const maybeHasDeleted =\n          typeof previousValue === 'string' &&\n          newValue.length < previousValue.length\n        if (maybeHasDeleted) {\n          // Since cutting/deleting text doesn't trigger\n          // selectionchange event, we'll have to dispatch it manually.\n          // NOTE: The following line also triggers when cmd+A then pasting\n          // a value with smaller length, which is not ideal for performance.\n          document.dispatchEvent(new Event('selectionchange'))\n        }\n        onChange(newValue)\n      },\n      [maxLength, onChange, previousValue, regexp],\n    )\n    const _focusListener = React.useCallback(() => {\n      if (inputRef.current) {\n        const start = Math.min(inputRef.current.value.length, maxLength - 1)\n        const end = inputRef.current.value.length\n        inputRef.current?.setSelectionRange(start, end)\n        setMirrorSelectionStart(start)\n        setMirrorSelectionEnd(end)\n      }\n      setIsFocused(true)\n    }, [maxLength])\n    // Fix iOS pasting\n    const _pasteListener = React.useCallback(\n      (e: React.ClipboardEvent<HTMLInputElement>) => {\n        const input = inputRef.current\n        if (!pasteTransformer && (!initialLoadRef.current.isIOS || !e.clipboardData || !input)) {\n          return\n        }\n        \n        const _content = e.clipboardData.getData('text/plain')\n        const content = pasteTransformer\n          ? pasteTransformer(_content)\n          : _content\n        e.preventDefault()\n\n        const start = inputRef.current?.selectionStart\n        const end = inputRef.current?.selectionEnd\n\n        const isReplacing = start !== end\n\n        const newValueUncapped = isReplacing\n          ? value.slice(0, start) + content + value.slice(end) // Replacing\n          : value.slice(0, start) + content + value.slice(start) // Inserting\n        const newValue = newValueUncapped.slice(0, maxLength)\n\n        if (newValue.length > 0 && regexp && !regexp.test(newValue)) {\n          return\n        }\n\n        input.value = newValue\n        onChange(newValue)\n\n        const _start = Math.min(newValue.length, maxLength - 1)\n        const _end = newValue.length\n\n        input.setSelectionRange(_start, _end)\n        setMirrorSelectionStart(_start)\n        setMirrorSelectionEnd(_end)\n      },\n      [maxLength, onChange, regexp, value],\n    )\n\n    /** Styles */\n    const rootStyle = React.useMemo<React.CSSProperties>(\n      () => ({\n        position: 'relative',\n        cursor: props.disabled ? 'default' : 'text',\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        pointerEvents: 'none',\n      }),\n      [props.disabled],\n    )\n\n    const inputStyle = React.useMemo<React.CSSProperties>(\n      () => ({\n        position: 'absolute',\n        inset: 0,\n        width: pwmb.willPushPWMBadge\n          ? `calc(100% + ${pwmb.PWM_BADGE_SPACE_WIDTH})`\n          : '100%',\n        clipPath: pwmb.willPushPWMBadge\n          ? `inset(0 ${pwmb.PWM_BADGE_SPACE_WIDTH} 0 0)`\n          : undefined,\n        height: '100%',\n        display: 'flex',\n        textAlign,\n        opacity: '1', // Mandatory for iOS hold-paste\n        color: 'transparent',\n        pointerEvents: 'all',\n        background: 'transparent',\n        caretColor: 'transparent',\n        border: '0 solid transparent',\n        outline: '0 solid transparent',\n        boxShadow: 'none',\n        lineHeight: '1',\n        letterSpacing: '-.5em',\n        fontSize: 'var(--root-height)',\n        fontFamily: 'monospace',\n        fontVariantNumeric: 'tabular-nums',\n        // letterSpacing: '-1em',\n        // transform: 'scale(1.5)',\n        // paddingRight: '100%',\n        // paddingBottom: '100%',\n        // debugging purposes\n        // inset: undefined,\n        // position: undefined,\n        // color: 'black',\n        // background: 'white',\n        // opacity: '1',\n        // caretColor: 'black',\n        // padding: '0',\n        // letterSpacing: 'unset',\n        // fontSize: 'unset',\n        // paddingInline: '.5rem',\n      }),\n      [pwmb.PWM_BADGE_SPACE_WIDTH, pwmb.willPushPWMBadge, textAlign],\n    )\n\n    /** Rendering */\n    const renderedInput = React.useMemo(\n      () => (\n        <input\n          autoComplete={props.autoComplete || 'one-time-code'}\n          {...props}\n          data-input-otp\n          data-input-otp-placeholder-shown={value.length === 0 || undefined}\n          data-input-otp-mss={mirrorSelectionStart}\n          data-input-otp-mse={mirrorSelectionEnd}\n          inputMode={inputMode}\n          pattern={regexp?.source}\n          aria-placeholder={placeholder}\n          style={inputStyle}\n          maxLength={maxLength}\n          value={value}\n          ref={inputRef}\n          onPaste={e => {\n            _pasteListener(e)\n            props.onPaste?.(e)\n          }}\n          onChange={_changeListener}\n          onMouseOver={e => {\n            setIsHoveringInput(true)\n            props.onMouseOver?.(e)\n          }}\n          onMouseLeave={e => {\n            setIsHoveringInput(false)\n            props.onMouseLeave?.(e)\n          }}\n          onFocus={e => {\n            _focusListener()\n            props.onFocus?.(e)\n          }}\n          onBlur={e => {\n            setIsFocused(false)\n            props.onBlur?.(e)\n          }}\n        />\n      ),\n      [\n        _changeListener,\n        _focusListener,\n        _pasteListener,\n        inputMode,\n        inputStyle,\n        maxLength,\n        mirrorSelectionEnd,\n        mirrorSelectionStart,\n        props,\n        regexp?.source,\n        value,\n      ],\n    )\n\n    const contextValue = React.useMemo<RenderProps>(() => {\n      return {\n        slots: Array.from({ length: maxLength }).map((_, slotIdx) => {\n          const isActive =\n            isFocused &&\n            mirrorSelectionStart !== null &&\n            mirrorSelectionEnd !== null &&\n            ((mirrorSelectionStart === mirrorSelectionEnd &&\n              slotIdx === mirrorSelectionStart) ||\n              (slotIdx >= mirrorSelectionStart && slotIdx < mirrorSelectionEnd))\n\n          const char = value[slotIdx] !== undefined ? value[slotIdx] : null\n          const placeholderChar = value[0] !== undefined ? null : placeholder?.[slotIdx] ?? null\n\n          return {\n            char,\n            placeholderChar,\n            isActive,\n            hasFakeCaret: isActive && char === null,\n          }\n        }),\n        isFocused,\n        isHovering: !props.disabled && isHoveringInput,\n      }\n    }, [\n      isFocused,\n      isHoveringInput,\n      maxLength,\n      mirrorSelectionEnd,\n      mirrorSelectionStart,\n      props.disabled,\n      value,\n    ])\n\n    const renderedChildren = React.useMemo(() => {\n      if (render) {\n        return render(contextValue)\n      }\n      return (\n        <OTPInputContext.Provider value={contextValue}>\n          {children}\n        </OTPInputContext.Provider>\n      )\n    }, [children, contextValue, render])\n\n    return (\n      <>\n        {noScriptCSSFallback !== null && (\n          <noscript>\n            <style>{noScriptCSSFallback}</style>\n          </noscript>\n        )}\n\n        <div\n          ref={containerRef}\n          data-input-otp-container\n          style={rootStyle}\n          className={containerClassName}\n        >\n          {renderedChildren}\n\n          <div\n            style={{\n              position: 'absolute',\n              inset: 0,\n              pointerEvents: 'none',\n            }}\n          >\n            {renderedInput}\n          </div>\n        </div>\n      </>\n    )\n  },\n)\nOTPInput.displayName = 'Input'\n\nfunction safeInsertRule(sheet: CSSStyleSheet, rule: string) {\n  try {\n    sheet.insertRule(rule)\n  } catch {\n    console.error('input-otp could not insert CSS rule:', rule)\n  }\n}\n\n// Decided to go with <noscript>\n// instead of `scripting` CSS media query\n// because it's a fallback for initial page load\n// and the <script> tag won't be loaded\n// unless the user has JS disabled.\nconst NOSCRIPT_CSS_FALLBACK = `\n[data-input-otp] {\n  --nojs-bg: white !important;\n  --nojs-fg: black !important;\n\n  background-color: var(--nojs-bg) !important;\n  color: var(--nojs-fg) !important;\n  caret-color: var(--nojs-fg) !important;\n  letter-spacing: .25em !important;\n  text-align: center !important;\n  border: 1px solid var(--nojs-fg) !important;\n  border-radius: 4px !important;\n  width: 100% !important;\n}\n@media (prefers-color-scheme: dark) {\n  [data-input-otp] {\n    --nojs-bg: black !important;\n    --nojs-fg: white !important;\n  }\n}`\n", "export function syncTimeouts(cb: (...args: any[]) => unknown): number[] {\n  const t1 = setTimeout(cb, 0) // For faster machines\n  const t2 = setTimeout(cb, 1_0)\n  const t3 = setTimeout(cb, 5_0)\n  return [t1, t2, t3]\n}\n", "import * as React from 'react'\n\nexport function usePrevious<T>(value: T) {\n  const ref = React.useRef<T>()\n  React.useEffect(() => {\n    ref.current = value\n  })\n  return ref.current\n}\n", "import * as React from 'react'\nimport { OTPInputProps } from './types'\n\nconst PWM_BADGE_MARGIN_RIGHT = 18\nconst PWM_BADGE_SPACE_WIDTH_PX = 40\nconst PWM_BADGE_SPACE_WIDTH = `${PWM_BADGE_SPACE_WIDTH_PX}px` as const\n\nconst PASSWORD_MANAGERS_SELECTORS = [\n  '[data-lastpass-icon-root]', // LastPass\n  'com-1password-button', // 1Password\n  '[data-dashlanecreated]', // Dashlane\n  '[style$=\"2147483647 !important;\"]', // Bitwarden\n].join(',')\n\nexport function usePasswordManagerBadge({\n  containerRef,\n  inputRef,\n  pushPasswordManagerStrategy,\n  isFocused,\n}: {\n  containerRef: React.RefObject<HTMLDivElement>\n  inputRef: React.RefObject<HTMLInputElement>\n  pushPasswordManagerStrategy: OTPInputProps['pushPasswordManagerStrategy']\n  isFocused: boolean\n}) {\n  /** Password managers have a badge\n   *  and I'll use this state to push them\n   *  outside the input */\n  const [hasPWMBadge, setHasPWMBadge] = React.useState(false)\n  const [hasPWMBadgeSpace, setHasPWMBadgeSpace] = React.useState(false)\n  const [done, setDone] = React.useState(false)\n\n  const willPushPWMBadge = React.useMemo(() => {\n    if (pushPasswordManagerStrategy === 'none') {\n      return false\n    }\n\n    const increaseWidthCase =\n      (pushPasswordManagerStrategy === 'increase-width' ||\n        // TODO: remove 'experimental-no-flickering' support in 2.0.0\n        pushPasswordManagerStrategy === 'experimental-no-flickering') &&\n      hasPWMBadge &&\n      hasPWMBadgeSpace\n\n    return increaseWidthCase\n  }, [hasPWMBadge, hasPWMBadgeSpace, pushPasswordManagerStrategy])\n\n  const trackPWMBadge = React.useCallback(() => {\n    const container = containerRef.current\n    const input = inputRef.current\n    if (\n      !container ||\n      !input ||\n      done ||\n      pushPasswordManagerStrategy === 'none'\n    ) {\n      return\n    }\n\n    const elementToCompare = container\n\n    // Get the top right-center point of the container.\n    // That is usually where most password managers place their badge.\n    const rightCornerX =\n      elementToCompare.getBoundingClientRect().left +\n      elementToCompare.offsetWidth\n    const centereredY =\n      elementToCompare.getBoundingClientRect().top +\n      elementToCompare.offsetHeight / 2\n    const x = rightCornerX - PWM_BADGE_MARGIN_RIGHT\n    const y = centereredY\n\n    // Do an extra search to check for famous password managers\n    const pmws = document.querySelectorAll(PASSWORD_MANAGERS_SELECTORS)\n\n    // If no password manager is automatically detect,\n    // we'll try to dispatch document.elementFromPoint\n    // to identify badges\n    if (pmws.length === 0) {\n      const maybeBadgeEl = document.elementFromPoint(x, y)\n\n      // If the found element is the input itself,\n      // then we assume it's not a password manager badge.\n      // We are not sure. Most times that means there isn't a badge.\n      if (maybeBadgeEl === container) {\n        return\n      }\n    }\n\n    setHasPWMBadge(true)\n    setDone(true)\n  }, [containerRef, inputRef, done, pushPasswordManagerStrategy])\n\n  React.useEffect(() => {\n    const container = containerRef.current\n    if (!container || pushPasswordManagerStrategy === 'none') {\n      return\n    }\n\n    // Check if the PWM area is 100% visible\n    function checkHasSpace() {\n      const viewportWidth = window.innerWidth\n      const distanceToRightEdge =\n        viewportWidth - container.getBoundingClientRect().right\n      setHasPWMBadgeSpace(distanceToRightEdge >= PWM_BADGE_SPACE_WIDTH_PX)\n    }\n\n    checkHasSpace()\n    const interval = setInterval(checkHasSpace, 1000)\n\n    return () => {\n      clearInterval(interval)\n    }\n  }, [containerRef, pushPasswordManagerStrategy])\n\n  React.useEffect(() => {\n    const _isFocused = isFocused || document.activeElement === inputRef.current\n\n    if (pushPasswordManagerStrategy === 'none' || !_isFocused) {\n      return\n    }\n    const t1 = setTimeout(trackPWMBadge, 0)\n    const t2 = setTimeout(trackPWMBadge, 2000)\n    const t3 = setTimeout(trackPWMBadge, 5000)\n    const t4 = setTimeout(() => {\n      setDone(true)\n    }, 6000)\n    return () => {\n      clearTimeout(t1)\n      clearTimeout(t2)\n      clearTimeout(t3)\n      clearTimeout(t4)\n    }\n  }, [inputRef, isFocused, pushPasswordManagerStrategy, trackPWMBadge])\n\n  return { hasPWMBadge, willPushPWMBadge, PWM_BADGE_SPACE_WIDTH }\n}\n", "export const REGEXP_ONLY_DIGITS = '^\\\\d+$'\nexport const REGEXP_ONLY_CHARS = '^[a-zA-Z]+$'\nexport const REGEXP_ONLY_DIGITS_AND_CHARS = '^[a-zA-Z0-9]+$'\n"], "mappings": ";;;;;;;;AAEA,QAAuB;AEFvB,QAAuB;ACAvB,QAAuB;;;;;;;;;;;;;;;;;;;;AFAhB,SAASA,GAAaC,GAA2C;AACtE,MAAMC,IAAK,WAAWD,GAAI,CAAC,GACrBE,IAAK,WAAWF,GAAI,EAAG,GACvBG,IAAK,WAAWH,GAAI,EAAG;AAC7B,SAAO,CAACC,GAAIC,GAAIC,CAAE;AACpB;ACHO,SAASC,GAAeC,GAAU;AACvC,MAAMC,IAAY,SAAU;AAC5B,SAAM,YAAU,MAAM;AACpBA,MAAI,UAAUD;EAChB,CAAC,GACMC,EAAI;AACb;ACLA,IAAMC,KAAyB;AAA/B,IACMC,KAA2B;AADjC,IAEMC,KAAwB,GAAGD,EAAwB;AAFzD,IAIME,KAA8B,CAClC,6BACA,wBACA,0BACA,mCACF,EAAE,KAAK,GAAG;AAEH,SAASC,GAAwB,EACtC,cAAAC,GACA,UAAAC,GACA,6BAAAC,GACA,WAAAC,EACF,GAKG;AAID,MAAM,CAACC,GAAaC,CAAc,IAAU,WAAS,KAAK,GACpD,CAACC,GAAkBC,CAAmB,IAAU,WAAS,KAAK,GAC9D,CAACC,GAAMC,CAAO,IAAU,WAAS,KAAK,GAEtCC,IAAyB,UAAQ,MACjCR,MAAgC,SAC3B,SAINA,MAAgC,oBAE/BA,MAAgC,iCAClCE,KACAE,GAGD,CAACF,GAAaE,GAAkBJ,CAA2B,CAAC,GAEzDS,IAAsB,cAAY,MAAM;AAC5C,QAAMC,IAAYZ,EAAa,SACzBa,IAAQZ,EAAS;AACvB,QACE,CAACW,KACD,CAACC,KACDL,KACAN,MAAgC,OAEhC;AAGF,QAAMY,IAAmBF,GAInBG,IACJD,EAAiB,sBAAsB,EAAE,OACzCA,EAAiB,aACbE,IACJF,EAAiB,sBAAsB,EAAE,MACzCA,EAAiB,eAAe,GAC5BG,IAAIF,IAAepB,IACnBuB,IAAIF;AAGG,aAAS,iBAAiBlB,EAA2B,EAKzD,WAAW,KACG,SAAS,iBAAiBmB,GAAGC,CAAC,MAK9BN,MAKvBP,EAAe,IAAI,GACnBI,EAAQ,IAAI;EACd,GAAG,CAACT,GAAcC,GAAUO,GAAMN,CAA2B,CAAC;AAE9D,SAAM,YAAU,MAAM;AACpB,QAAMU,IAAYZ,EAAa;AAC/B,QAAI,CAACY,KAAaV,MAAgC,OAChD;AAIF,aAASiB,IAAgB;AAEvB,UAAMC,IADgB,OAAO,aAEXR,EAAU,sBAAsB,EAAE;AACpDL,QAAoBa,KAAuBxB,EAAwB;IACrE;AAEAuB,MAAc;AACd,QAAME,IAAW,YAAYF,GAAe,GAAI;AAEhD,WAAO,MAAM;AACX,oBAAcE,CAAQ;IACxB;EACF,GAAG,CAACrB,GAAcE,CAA2B,CAAC,GAExC,YAAU,MAAM;AACpB,QAAMoB,IAAanB,KAAa,SAAS,kBAAkBF,EAAS;AAEpE,QAAIC,MAAgC,UAAU,CAACoB,EAC7C;AAEF,QAAMjC,IAAK,WAAWsB,GAAe,CAAC,GAChCrB,IAAK,WAAWqB,GAAe,GAAI,GACnCpB,IAAK,WAAWoB,GAAe,GAAI,GACnCY,IAAK,WAAW,MAAM;AAC1Bd,QAAQ,IAAI;IACd,GAAG,GAAI;AACP,WAAO,MAAM;AACX,mBAAapB,CAAE,GACf,aAAaC,CAAE,GACf,aAAaC,CAAE,GACf,aAAagC,CAAE;IACjB;EACF,GAAG,CAACtB,GAAUE,GAAWD,GAA6BS,CAAa,CAAC,GAE7D,EAAE,aAAAP,GAAa,kBAAAM,GAAkB,uBAAAb,GAAsB;AAChE;AH/HO,IAAM2B,KAAwB,gBACnC,CAAC,CACH;AAFO,IAIMC,KAAiB,aAC5B,CACEC,GAmBAhC,MACG;AApBH,MAAAiC,IAAAD,GACE,EAAA,OAAOE,GACP,UAAUC,GACV,WAAAC,GACA,WAAAC,IAAY,QACZ,SAAAC,GACA,aAAAC,GACA,WAAAC,IAAY,WACZ,YAAAC,GACA,6BAAAjC,IAA8B,kBAC9B,kBAAAkC,GACA,oBAAAC,GACA,qBAAAC,IAAsBC,IAEtB,QAAAC,GACA,UAAAC,EA9BN,IAeId,GAiBKe,IAAAC,GAjBLhB,GAiBK,CAhBH,SACA,YACA,aACA,aACA,WACA,eACA,aACA,cACA,+BACA,oBACA,sBACA,uBAEA,UACA,UAAA,CAAA;AA9BN,MAAAD,GAAAC,IAAAiB,IAAAC,IAAAC;AAqCI,MAAM,CAACC,GAAeC,EAAgB,IAAU,WAC9C,OAAON,EAAM,gBAAiB,WAAWA,EAAM,eAAe,EAChE,GAGMjD,IAAQmC,KAAA,OAAAA,IAAkBmB,GAC1BE,IAAgBzD,GAAYC,CAAK,GACjCyD,IAAiB,cACpBC,OAAqB;AACpBtB,SAAA,QAAAA,EAAoBsB,CAAAA,GACpBH,GAAiBG,CAAQ;EAC3B,GACA,CAACtB,CAAiB,CACpB,GACMuB,IAAe,UACnB,MACEpB,IACI,OAAOA,KAAY,WACjB,IAAI,OAAOA,CAAO,IAClBA,IACF,MACN,CAACA,CAAO,CACV,GAGM/B,IAAiB,SAAyB,IAAI,GAC9CD,IAAqB,SAAuB,IAAI,GAChDqD,IAAuB,SAAO,EAClC,OAAA5D,GACA,UAAAyD,GACA,OACE,OAAO,UAAW,iBAClBvB,MAAAD,IAAA,UAAA,OAAA,SAAA,OAAQ,QAAR,OAAA,SAAAA,EAAa,aAAb,OAAA,SAAAC,GAAA,KAAAD,GAAwB,yBAAyB,MAAA,GACrD,CAAC,GACK4B,IAAyB,SAE5B,EACD,MAAM,EACJV,KAAA3C,EAAS,YAAT,OAAA,SAAA2C,GAAkB,iBAClBC,KAAA5C,EAAS,YAAT,OAAA,SAAA4C,GAAkB,eAClBC,KAAA7C,EAAS,YAAT,OAAA,SAAA6C,GAAkB,kBACpB,EACF,CAAC;AACK,EAAA,sBAAoBpD,GAAK,MAAMO,EAAS,SAAS,CAAC,CAAC,GACnD,YAAU,MAAM;AACpB,QAAMY,IAAQZ,EAAS,SACjBW,IAAYZ,EAAa;AAE/B,QAAI,CAACa,KAAS,CAACD,EACb;AAIEyC,MAAe,QAAQ,UAAUxC,EAAM,SACzCwC,EAAe,QAAQ,SAASxC,EAAM,KAAK,GAI7CyC,EAAiB,QAAQ,OAAO,CAC9BzC,EAAM,gBACNA,EAAM,cACNA,EAAM,kBACR;AACA,aAAS0C,IAA4B;AACnC,UAAI,SAAS,kBAAkB1C,GAAO;AACpC2C,UAAwB,IAAI,GAC5BC,EAAsB,IAAI;AAC1B;MACF;AAGA,UAAMC,IAAK7C,EAAM,gBACXiC,IAAKjC,EAAM,cACX8C,KAAO9C,EAAM,oBACb+C,IAAM/C,EAAM,WACZgD,IAAOhD,EAAM,OACbiD,IAAQR,EAAiB,QAAQ,MAGnCS,IAAQ,IACRC,IAAM,IACNC;AACJ,UAAIJ,EAAK,WAAW,KAAKH,MAAO,QAAQZ,MAAO,MAAM;AACnD,YAAMoB,KAAgBR,MAAOZ,GACvBqB,KAAeT,MAAOG,EAAK,UAAUA,EAAK,SAASD;AAEzD,YAAIM,MAAiB,CAACC,IAAc;AAClC,cAAMC,IAAIV;AACV,cAAIU,MAAM,EACRL,KAAQ,GACRC,IAAM,GACNC,IAAY;mBACHG,MAAMR,EACfG,KAAQK,IAAI,GACZJ,IAAMI,GACNH,IAAY;mBACHL,IAAM,KAAKC,EAAK,SAAS,GAAG;AACrC,gBAAIQ,KAAS;AACb,gBAAIP,EAAM,CAAC,MAAM,QAAQA,EAAM,CAAC,MAAM,MAAM;AAC1CG,kBAAYG,IAAIN,EAAM,CAAC,IAAI,aAAa;AACxC,kBAAMQ,KACJR,EAAM,CAAC,MAAMA,EAAM,CAAC,KAAKA,EAAM,CAAC,IAAIF;AAClCK,oBAAc,cAAc,CAACK,OAC/BD,KAAS;YAEb;AAEAN,gBAAQM,KAASD,GACjBJ,IAAMK,KAASD,IAAI;UACrB;QACF;AAEIL,cAAU,MAAMC,MAAQ,MAAMD,MAAUC,KAC1C/D,EAAS,QAAQ,kBAAkB8D,GAAOC,GAAKC,CAAS;MAE5D;AAGA,UAAMM,KAAIR,MAAU,KAAKA,IAAQL,GAC3Bc,KAAIR,MAAQ,KAAKA,IAAMlB,GACvB2B,KAAMR,KAAA,OAAAA,IAAaN;AACzBH,QAAwBe,EAAC,GACzBd,EAAsBe,EAAC,GAEvBlB,EAAiB,QAAQ,OAAO,CAACiB,IAAGC,IAAGC,EAAG;IAC5C;AAUA,QATA,SAAS,iBAAiB,mBAAmBlB,GAA2B,EACtE,SAAS,KACX,CAAC,GAGDA,EAA0B,GAC1B,SAAS,kBAAkB1C,KAAS6D,EAAa,IAAI,GAGjD,CAAC,SAAS,eAAe,iBAAiB,GAAG;AAC/C,UAAMC,IAAU,SAAS,cAAc,OAAO;AAI9C,UAHAA,EAAQ,KAAK,mBACb,SAAS,KAAK,YAAYA,CAAO,GAE7BA,EAAQ,OAAO;AACjB,YAAMC,IACJ;AAEFC,UACEF,EAAQ,OACR,oGACF,GACAE,EACEF,EAAQ,OACR,+BAA+BC,CAAc,IAC/C,GACAC,EACEF,EAAQ,OACR,uCAAuCC,CAAc,IACvD,GAEAC,EACEF,EAAQ,OACR,oPACF,GAEAE,EACEF,EAAQ,OACR,0DACF;MACF;IACF;AAEA,QAAMG,IAAmB,MAAM;AACzBlE,WACFA,EAAU,MAAM,YACd,iBACA,GAAGC,EAAM,YAAY,IACvB;IAEJ;AACAiE,MAAiB;AACjB,QAAMC,IAAiB,IAAI,eAAeD,CAAgB;AAC1D,WAAAC,EAAe,QAAQlE,CAAK,GAErB,MAAM;AACX,eAAS,oBACP,mBACA0C,GACA,EAAE,SAAS,KAAK,CAClB,GACAwB,EAAe,WAAW;IAC5B;EACF,GAAG,CAAC,CAAC;AAGL,MAAM,CAACC,IAAiBC,EAAkB,IAAU,WAAS,KAAK,GAC5D,CAAC9E,GAAWuE,CAAY,IAAU,WAAS,KAAK,GAChD,CAACQ,GAAsB1B,CAAuB,IAAU,WAE5D,IAAI,GACA,CAAC2B,GAAoB1B,CAAqB,IAAU,WAExD,IAAI;AAGA,EAAA,YAAU,MAAM;AACpBtE,OAAa,MAAM;AAhPzB,UAAAuC,GAAAC,GAAAiB,GAAAC;AAAAA,OAkPQnB,IAAAzB,EAAS,YAAT,QAAAyB,EAAkB,cAAc,IAAI,MAAM,OAAO,CAAA;AAGjD,UAAM6C,KAAI5C,IAAA1B,EAAS,YAAT,OAAA,SAAA0B,EAAkB,gBACtB6C,KAAI5B,IAAA3C,EAAS,YAAT,OAAA,SAAA2C,EAAkB,cACtB6B,KAAM5B,IAAA5C,EAAS,YAAT,OAAA,SAAA4C,EAAkB;AAC1B0B,YAAM,QAAQC,MAAM,SACtBhB,EAAwBe,CAAC,GACzBd,EAAsBe,CAAC,GACvBlB,EAAiB,QAAQ,OAAO,CAACiB,GAAGC,GAAGC,CAAG;IAE9C,CAAC;EACH,GAAG,CAAChF,GAAOU,CAAS,CAAC,GAEf,YAAU,MAAM;AAChB8C,UAAkB,UAKpBxD,MAAUwD,KACVA,EAAc,SAASnB,KACvBrC,EAAM,WAAWqC,MAEjBK,KAAA,QAAAA,EAAa1C,CAAAA;EAEjB,GAAG,CAACqC,GAAWK,GAAYc,GAAexD,CAAK,CAAC;AAEhD,MAAM2F,IAAOrF,GAAwB,EACnC,cAAAC,GACA,UAAAC,GACA,6BAAAC,GACA,WAAAC,EACF,CAAC,GAGKkF,KAAwB,cAC3Bb,OAA2C;AAC1C,QAAMrB,IAAWqB,EAAE,cAAc,MAAM,MAAM,GAAG1C,CAAS;AACzD,QAAIqB,EAAS,SAAS,KAAKC,KAAU,CAACA,EAAO,KAAKD,CAAQ,GAAG;AAC3DqB,QAAE,eAAe;AACjB;IACF;AAEE,WAAOvB,KAAkB,YACzBE,EAAS,SAASF,EAAc,UAMhC,SAAS,cAAc,IAAI,MAAM,iBAAiB,CAAC,GAErDC,EAASC,CAAQ;EACnB,GACA,CAACrB,GAAWoB,GAAUD,GAAeG,CAAM,CAC7C,GACMkC,KAAuB,cAAY,MAAM;AA3SnD,QAAA5D;AA4SM,QAAIzB,EAAS,SAAS;AACpB,UAAM8D,IAAQ,KAAK,IAAI9D,EAAS,QAAQ,MAAM,QAAQ6B,IAAY,CAAC,GAC7DkC,IAAM/D,EAAS,QAAQ,MAAM;AAAA,OACnCyB,IAAAzB,EAAS,YAAT,QAAAyB,EAAkB,kBAAkBqC,GAAOC,CAAAA,GAC3CR,EAAwBO,CAAK,GAC7BN,EAAsBO,CAAG;IAC3B;AACAU,MAAa,IAAI;EACnB,GAAG,CAAC5C,CAAS,CAAC,GAERyD,KAAuB,cAC1Bf,OAA8C;AAvTrD,QAAA9C,GAAAC;AAwTQ,QAAMd,IAAQZ,EAAS;AACvB,QAAI,CAACmC,MAAqB,CAACiB,EAAe,QAAQ,SAAS,CAACmB,EAAE,iBAAiB,CAAC3D,GAC9E;AAGF,QAAM2E,IAAWhB,EAAE,cAAc,QAAQ,YAAY,GAC/CiB,IAAUrD,IACZA,EAAiBoD,CAAQ,IACzBA;AACJhB,MAAE,eAAe;AAEjB,QAAMT,KAAQrC,IAAAzB,EAAS,YAAT,OAAA,SAAAyB,EAAkB,gBAC1BsC,KAAMrC,IAAA1B,EAAS,YAAT,OAAA,SAAA0B,EAAkB,cAOxBwB,KALcY,MAAUC,IAG1BvE,EAAM,MAAM,GAAGsE,CAAK,IAAI0B,IAAUhG,EAAM,MAAMuE,CAAG,IACjDvE,EAAM,MAAM,GAAGsE,CAAK,IAAI0B,IAAUhG,EAAM,MAAMsE,CAAK,GACrB,MAAM,GAAGjC,CAAS;AAEpD,QAAIqB,EAAS,SAAS,KAAKC,KAAU,CAACA,EAAO,KAAKD,CAAQ,EACxD;AAGFtC,MAAM,QAAQsC,GACdD,EAASC,CAAQ;AAEjB,QAAMuC,IAAS,KAAK,IAAIvC,EAAS,QAAQrB,IAAY,CAAC,GAChD6D,IAAOxC,EAAS;AAEtBtC,MAAM,kBAAkB6E,GAAQC,CAAI,GACpCnC,EAAwBkC,CAAM,GAC9BjC,EAAsBkC,CAAI;EAC5B,GACA,CAAC7D,GAAWoB,GAAUE,GAAQ3D,CAAK,CACrC,GAGMmG,KAAkB,UACtB,OAAO,EACL,UAAU,YACV,QAAQlD,EAAM,WAAW,YAAY,QACrC,YAAY,QACZ,kBAAkB,QAClB,eAAe,OACjB,IACA,CAACA,EAAM,QAAQ,CACjB,GAEMmD,KAAmB,UACvB,OAAO,EACL,UAAU,YACV,OAAO,GACP,OAAOT,EAAK,mBACR,eAAeA,EAAK,qBAAqB,MACzC,QACJ,UAAUA,EAAK,mBACX,WAAWA,EAAK,qBAAqB,UACrC,QACJ,QAAQ,QACR,SAAS,QACT,WAAArD,GACA,SAAS,KACT,OAAO,eACP,eAAe,OACf,YAAY,eACZ,YAAY,eACZ,QAAQ,uBACR,SAAS,uBACT,WAAW,QACX,YAAY,KACZ,eAAe,SACf,UAAU,sBACV,YAAY,aACZ,oBAAoB,eAgBtB,IACA,CAACqD,EAAK,uBAAuBA,EAAK,kBAAkBrD,CAAS,CAC/D,GAGM+D,KAAsB,UAC1B,MACE,gBAAC,SAAAC,GAAAC,GAAA,EACC,cAActD,EAAM,gBAAgB,gBAAA,GAChCA,CAAAA,GAFL,EAGC,kBAAc,MACd,oCAAkCjD,EAAM,WAAW,KAAK,QACxD,sBAAoByF,GACpB,sBAAoBC,GACpB,WAAWjD,GACX,SAASkB,KAAA,OAAA,SAAAA,EAAQ,QACjB,oBAAkBnB,GAClB,OAAO4D,IACP,WAAW/D,GACX,OAAOrC,GACP,KAAKQ,GACL,SAASuE,OAAK;AAxaxB,QAAA9C;AAyaY6D,OAAef,CAAC,IAChB9C,IAAAgB,EAAM,YAAN,QAAAhB,EAAA,KAAAgB,GAAgB8B,CAAAA;EAClB,GACA,UAAUa,IACV,aAAab,OAAK;AA7a5B,QAAA9C;AA8aYuD,OAAmB,IAAI,IACvBvD,IAAAgB,EAAM,gBAAN,QAAAhB,EAAA,KAAAgB,GAAoB8B,CAAAA;EACtB,GACA,cAAcA,OAAK;AAjb7B,QAAA9C;AAkbYuD,OAAmB,KAAK,IACxBvD,IAAAgB,EAAM,iBAAN,QAAAhB,EAAA,KAAAgB,GAAqB8B,CAAAA;EACvB,GACA,SAASA,OAAK;AArbxB,QAAA9C;AAsbY4D,OAAe,IACf5D,IAAAgB,EAAM,YAAN,QAAAhB,EAAA,KAAAgB,GAAgB8B,CAAAA;EAClB,GACA,QAAQA,OAAK;AAzbvB,QAAA9C;AA0bYgD,MAAa,KAAK,IAClBhD,IAAAgB,EAAM,WAAN,QAAAhB,EAAA,KAAAgB,GAAe8B,CAAAA;EACjB,EAAA,CAAA,CACF,GAEF,CACEa,IACAC,IACAC,IACArD,GACA2D,IACA/D,GACAqD,GACAD,GACAxC,GACAU,KAAA,OAAA,SAAAA,EAAQ,QACR3D,CACF,CACF,GAEMwG,KAAqB,UAAqB,OACvC,EACL,OAAO,MAAM,KAAK,EAAE,QAAQnE,EAAU,CAAC,EAAE,IAAI,CAACoE,GAAGC,MAAY;AAhdrE,QAAAzE;AAidU,QAAM0E,IACJjG,KACA+E,MAAyB,QACzBC,MAAuB,SACrBD,MAAyBC,KACzBgB,MAAYjB,KACXiB,KAAWjB,KAAwBiB,IAAUhB,IAE5CkB,IAAO5G,EAAM0G,CAAO,MAAM,SAAY1G,EAAM0G,CAAO,IAAI,MACvDG,IAAkB7G,EAAM,CAAC,MAAM,SAAY,QAAOiC,IAAAO,KAAA,OAAA,SAAAA,EAAckE,CAAAA,MAAd,OAAAzE,IAA0B;AAElF,WAAO,EACL,MAAA2E,GACA,iBAAAC,GACA,UAAAF,GACA,cAAcA,KAAYC,MAAS,KACrC;EACF,CAAC,GACD,WAAAlG,GACA,YAAY,CAACuC,EAAM,YAAYsC,GACjC,IACC,CACD7E,GACA6E,IACAlD,GACAqD,GACAD,GACAxC,EAAM,UACNjD,CACF,CAAC,GAEK8G,KAAyB,UAAQ,MACjC/D,IACKA,EAAOyD,EAAY,IAG1B,gBAACzE,GAAgB,UAAhB,EAAyB,OAAOyE,GAAAA,GAC9BxD,CACH,GAED,CAACA,GAAUwD,IAAczD,CAAM,CAAC;AAEnC,SACE,gBAAA,YAAA,MACGF,MAAwB,QACvB,gBAAC,YAAA,MACC,gBAAC,SAAA,MAAOA,CAAoB,CAC9B,GAGF,gBAAC,OAAA,EACC,KAAKtC,GACL,4BAAwB,MACxB,OAAO4F,IACP,WAAWvD,EAAAA,GAEVkE,IAED,gBAAC,OAAA,EACC,OAAO,EACL,UAAU,YACV,OAAO,GACP,eAAe,OACjB,EAAA,GAECT,EACH,CACF,CACF;AAEJ,CACF;AACArE,GAAS,cAAc;AAEvB,SAASoD,EAAe2B,GAAsBC,GAAc;AAC1D,MAAI;AACFD,MAAM,WAAWC,CAAI;EACvB,SAAQ,GAAA;AACN,YAAQ,MAAM,wCAAwCA,CAAI;EAC5D;AACF;AAOA,IAAMlE,KAAwB;;;;;;;;;;;;;;;;;;;;AIxiBvB,IAAMmE,KAAqB;AAA3B,IACMC,KAAoB;AAD1B,IAEMC,KAA+B;", "names": ["syncTimeouts", "cb", "t1", "t2", "t3", "usePrevious", "value", "ref", "PWM_BADGE_MARGIN_RIGHT", "PWM_BADGE_SPACE_WIDTH_PX", "PWM_BADGE_SPACE_WIDTH", "PASSWORD_MANAGERS_SELECTORS", "usePasswordManagerBadge", "containerRef", "inputRef", "pushPasswordManagerStrategy", "isFocused", "hasPWMBadge", "setHasPWMBadge", "hasPWMBadgeSpace", "setHasPWMBadgeSpace", "done", "setDone", "willPushPWMBadge", "trackPWMBadge", "container", "input", "elementToCompare", "rightCornerX", "centereredY", "x", "y", "checkHasSpace", "distanceToRightEdge", "interval", "_isFocused", "t4", "OTPInputContext", "OTPInput", "_a", "_b", "uncheckedValue", "uncheckedOnChange", "max<PERSON><PERSON><PERSON>", "textAlign", "pattern", "placeholder", "inputMode", "onComplete", "pasteTransformer", "containerClassName", "noScriptCSSFallback", "NOSCRIPT_CSS_FALLBACK", "render", "children", "props", "__objRest", "_c", "_d", "_e", "internalValue", "setInternalValue", "previousValue", "onChange", "newValue", "regexp", "initialLoadRef", "inputMetadataRef", "onDocumentSelectionChange", "setMirrorSelectionStart", "setMirrorSelectionEnd", "_s", "_dir", "_ml", "_val", "_prev", "start", "end", "direction", "isSingleCaret", "isInsertMode", "c", "offset", "wasPreviouslyInserting", "s", "e", "dir", "setIsFocused", "styleEl", "autofillStyles", "safeInsertRule", "updateRootHeight", "resizeObserver", "isHoveringInput", "setIsHoveringInput", "mirrorSelectionStart", "mirrorSelectionEnd", "pwmb", "_changeListener", "_focusListener", "_pasteListener", "_content", "content", "_start", "_end", "rootStyle", "inputStyle", "renderedInput", "__spreadProps", "__spreadValues", "contextValue", "_", "slotIdx", "isActive", "char", "placeholder<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sheet", "rule", "REGEXP_ONLY_DIGITS", "REGEXP_ONLY_CHARS", "REGEXP_ONLY_DIGITS_AND_CHARS"]}