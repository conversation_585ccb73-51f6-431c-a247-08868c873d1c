{"version": 3, "sources": ["../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/columnHelper.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/core/cell.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/core/column.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/core/headers.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/core/row.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnFaceting.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/filterFns.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnFiltering.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/aggregationFns.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnGrouping.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnOrdering.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnPinning.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/document.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnSizing.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/ColumnVisibility.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/GlobalFaceting.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/GlobalFiltering.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/RowExpanding.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/RowPagination.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/RowPinning.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/RowSelection.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/sortingFns.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/features/RowSorting.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/core/table.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getCoreRowModel.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getExpandedRowModel.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getFacetedMinMaxValues.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/filterRowsUtils.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getFacetedRowModel.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getFacetedUniqueValues.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getFilteredRowModel.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getGroupedRowModel.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getPaginationRowModel.ts", "../../.pnpm/@tanstack+table-core@8.21.3/node_modules/@tanstack/table-core/src/utils/getSortedRowModel.ts", "../../.pnpm/@tanstack+react-table@8.21.3_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@tanstack/react-table/src/index.tsx"], "sourcesContent": ["import {\n  AccessorFn,\n  AccessorFnColumnDef,\n  AccessorKeyColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => TAccessor extends AccessorFn<TData>\n    ? AccessorFnColumnDef<TData, TValue>\n    : AccessorKeyColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => DisplayColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => GroupColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column,\n    group: column => column,\n  }\n}\n", "import { TableOptionsResolved, TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : T extends readonly any[] & IsTuple<T>\n      ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n      : T extends any[]\n        ? DeepKeys<T[number], [...TDepth, any]>\n        : T extends Date\n          ? never\n          : T extends object\n            ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n            : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> =\n  T extends Record<string | number, any>\n    ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n      ? DeepValue<T[TBranch], TDeepProp>\n      : T[TProp & string]\n    : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TDepArgs, TResult>(\n  getDeps: (depArgs?: TDepArgs) => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): (depArgs?: TDepArgs) => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return depArgs => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps(depArgs)\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n\nexport function getMemoOptions(\n  tableOptions: Partial<TableOptionsResolved<any>>,\n  debugLevel:\n    | 'debugAll'\n    | 'debugCells'\n    | 'debugTable'\n    | 'debugColumns'\n    | 'debugRows'\n    | 'debugHeaders',\n  key: string,\n  onChange?: (result: any) => void\n) {\n  return {\n    debug: () => tableOptions?.debugAll ?? tableOptions[debugLevel],\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange,\n  }\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, getMemoOptions, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  cell: Cell<TData, TValue>\n  column: Column<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n  row: Row<TData>\n  table: Table<TData>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  /**\n   * The associated Column object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  column: Column<TData, TValue>\n  /**\n   * Returns the rendering context (or props) for cell-based components like cells and aggregated cells. Use these props with your framework's `flexRender` utility to render these using the template of your choice:\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getContext: () => CellContext<TData, TValue>\n  /**\n   * Returns the value for the cell, accessed via the associated column's accessor key or accessor function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getValue: CellContext<TData, TValue>['getValue']\n  /**\n   * The unique ID for the cell across the entire table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  id: string\n  /**\n   * Renders the value for a cell the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  renderValue: CellContext<TData, TValue>['renderValue']\n  /**\n   * The associated Row object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#row)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  row: Row<TData>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      getMemoOptions(table.options, 'debugCells', 'cell.getContext')\n    ),\n  }\n\n  table._features.forEach(feature => {\n    feature.createCell?.(\n      cell as Cell<TData, TValue>,\n      column,\n      row as Row<TData>,\n      table\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey\n      ? typeof String.prototype.replaceAll === 'function'\n        ? accessorKey.replaceAll('.', '_')\n        : accessorKey.replace(/\\./g, '_')\n      : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns')\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column as Column<TData, TValue>, table)\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  HeaderGroup,\n  Table,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nconst debug = 'debugHeaders'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header as Header<TData, TValue>, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      getMemoOptions(table.options, debug, 'getHeaderGroups')\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      getMemoOptions(table.options, debug, 'getCenterHeaderGroups')\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      getMemoOptions(table.options, debug, 'getLeftHeaderGroups')\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      getMemoOptions(table.options, debug, 'getRightHeaderGroups')\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getFooterGroups')\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFooterGroups')\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFooterGroups')\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getRightFooterGroups')\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getFlatHeaders')\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFlatHeaders')\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFlatHeaders')\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getRightFlatHeaders')\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getCenterLeafHeaders')\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getLeftLeafHeaders')\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getRightLeafHeaders')\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeafHeaders')\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCells')\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId')\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row as Row<TData>, table)\n  }\n\n  return row as Row<TData>\n}\n", "import { RowModel } from '..'\nimport { Column, RowData, Table, TableFeature } from '../types'\n\nexport interface FacetedColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n}\n\nexport interface FacetedOptions<TData extends RowData> {\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n}\n\n//\n\nexport const ColumnFaceting: TableFeature = {\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn } from './features/ColumnFiltering'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue?.toString()?.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  FilterFns,\n  FilterMeta,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface ColumnFiltersTableState {\n  columnFilters: ColumnFiltersState\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  filterFn: FilterFn<TData>\n  id: string\n  resolvedValue: unknown\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n  resolveFilterValue?: TransformFilterValueFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface ColumnFiltersColumnDef<TData extends RowData> {\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFn?: FilterFnOption<TData>\n}\n\nexport interface ColumnFiltersColumn<TData extends RowData> {\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface ColumnFiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface ColumnFiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  maxLeafRowFilterDepth?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface ColumnFiltersOptions<TData extends RowData>\n  extends ColumnFiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface ColumnFiltersInstance<TData extends RowData> {\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const ColumnFiltering: TableFeature = {\n  getDefaultColumnDef: <\n    TData extends RowData,\n  >(): ColumnFiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): ColumnFiltersTableState => {\n    return {\n      columnFilters: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnFiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n    } as ColumnFiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousFilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousFilter ? previousFilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousFilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    _table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/ColumnGrouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport {\n  AggregationFns,\n  Cell,\n  Column,\n  ColumnDefTemplate,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const ColumnGrouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        (column.columnDef.enableGrouping ?? true) &&\n        (table.options.enableGrouping ?? true) &&\n        (!!column.accessorFn || !!column.columnDef.getGroupingValue)\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nimport {\n  Column,\n  OnChangeFn,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\n\nimport { orderColumns } from './ColumnGrouping'\nimport { ColumnPinningPosition, _getVisibleLeafColumns } from '..'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderColumn {\n  /**\n   * Returns the index of the column in the order of the visible columns. Optionally pass a `position` parameter to get the index of the column in a sub-section of the table\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIndex: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns `true` if the column is the first column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the first in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getisfirstcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsFirstColumn: (position?: ColumnPinningPosition | 'center') => boolean\n  /**\n   * Returns `true` if the column is the last column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the last in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getislastcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsLastColumn: (position?: ColumnPinningPosition | 'center') => boolean\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const ColumnOrdering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getIndex = memo(\n      position => [_getVisibleLeafColumns(table, position)],\n      columns => columns.findIndex(d => d.id === column.id),\n      getMemoOptions(table.options, 'debugColumns', 'getIndex')\n    )\n    column.getIsFirstColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[0]?.id === column.id\n    }\n    column.getIsLastColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[columns.length - 1]?.id === column.id\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) =>\n        (columns: Column<TData, unknown>[]) => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n      getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * @deprecated Use `enableColumnPinning` or `enableRowPinning` instead.\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const ColumnPinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells')\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells')\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns')\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns')\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns')\n    )\n  },\n}\n", "export function safelyAccessDocument(_document?: Document): Document | null {\n  return _document || (typeof document !== 'undefined' ? document : null)\n}\n\nexport function safelyAccessDocumentEvent(event: Event): Document | null {\n  return !!event &&\n    !!event.target &&\n    typeof event.target === 'object' &&\n    'ownerDocument' in event.target\n    ? (event.target.ownerDocument as Document | null)\n    : null\n}\n", "import { _getVisibleLeafColumns } from '..'\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>umn,\n  <PERSON>er,\n  OnChangeFn,\n  Table,\n  Updater,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\nimport { ColumnPinningPosition } from './ColumnPinning'\nimport { safelyAccessDocument } from '../utils/document'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding (left) headers in relation to the current column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all succeeding (right) headers in relation to the current column.\n   */\n  getAfter: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n\n    column.getStart = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(0, column.getIndex(position))\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getStart')\n    )\n\n    column.getAfter = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(column.getIndex(position) + 1)\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getAfter')\n    )\n\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument = safelyAccessDocument(_contextDocument)\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { ColumnPinningPosition } from '..'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  /**\n   * Whether to enable column hiding. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#enablehiding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const ColumnVisibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      const childColumns = column.columns\n      return (\n        (childColumns.length\n          ? childColumns.some(c => c.getIsVisible())\n          : table.getState().columnVisibility?.[column.id]) ?? true\n      )\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells')\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      getMemoOptions(table.options, 'debugRows', 'getVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        getMemoOptions(table.options, 'debugColumns', key)\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nexport function _getVisibleLeafColumns<TData extends RowData>(\n  table: Table<TData>,\n  position?: ColumnPinningPosition | 'center'\n) {\n  return !position\n    ? table.getVisibleLeafColumns()\n    : position === 'center'\n      ? table.getCenterVisibleLeafColumns()\n      : position === 'left'\n        ? table.getLeftVisibleLeafColumns()\n        : table.getRightVisibleLeafColumns()\n}\n", "import { RowModel } from '..'\nimport { Table, RowData, TableFeature } from '../types'\n\nexport interface GlobalFacetingInstance<TData extends RowData> {\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n}\n\n//\n\nexport const GlobalFaceting: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn, FilterFnOption } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport interface GlobalFilterTableState {\n  globalFilter: any\n}\n\nexport interface GlobalFilterColumnDef {\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface GlobalFilterColumn {\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getCanGlobalFilter: () => boolean\n}\n\nexport interface GlobalFilterOptions<TData extends RowData> {\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n}\n\nexport interface GlobalFilterInstance<TData extends RowData> {\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const GlobalFiltering: TableFeature = {\n  getInitialState: (state): GlobalFilterTableState => {\n    return {\n      globalFilter: undefined,\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GlobalFilterOptions<TData> => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as GlobalFilterOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport {\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const RowExpanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  RowModel,\n  Updater,\n  <PERSON>Data,\n  TableFeature,\n} from '../types'\nimport {\n  functionalUpdate,\n  getMemoOptions,\n  makeStateUpdater,\n  memo,\n} from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you can supply a total `pageCount` value to the table if you know it (Or supply a `rowCount` and `pageCount` will be calculated). If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n  /**\n   * When manually controlling pagination, you can supply a total `rowCount` value to the table if you know it. The `pageCount` can be calculated from this value and the `pageSize`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#rowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  rowCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns the row count. If manually paginating or controlling the pagination state, this will come directly from the `options.rowCount` table option, otherwise it will be calculated from the table data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getrowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getRowCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Sets the page index to `0`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#firstpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  firstPage: () => void\n  /**\n   * Sets the page index to the last page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#lastpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  lastPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * @deprecated The page count no longer exists in the pagination state. Just pass as a table option instead.\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const RowPagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    //deprecated\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPageOptions')\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.firstPage = () => {\n      return table.setPageIndex(0)\n    }\n\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1)\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(table.getRowCount() / table.getState().pagination.pageSize)\n      )\n    }\n\n    table.getRowCount = () => {\n      return (\n        table.options.rowCount ?? table.getPrePaginationRowModel().rows.length\n      )\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (\n    visiblePinnedRows: Array<Row<TData>>,\n    pinnedRowIds: Array<string> | undefined,\n    position: 'top' | 'bottom'\n  ) => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const RowPinning: TableFeature = {\n  getInitialState: (state): RowPinningTableState => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowPinningDefaultOptions => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = (\n        position === 'top' ? table.getTopRows() : table.getBottomRows()\n      )?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      const rows =\n        table.options.keepPinnedRows ?? true\n          ? //get all rows that are pinned even if they would not be otherwise visible\n            //account for expanded parent rows, but not pagination or filtering\n            (pinnedRowIds ?? []).map(rowId => {\n              const row = table.getRow(rowId, true)\n              return row.getIsAllParentsExpanded() ? row : null\n            })\n          : //else get only visible rows that are pinned\n            (pinnedRowIds ?? []).map(\n              rowId => visibleRows.find(row => row.id === rowId)!\n            )\n\n      return rows.filter(Boolean).map(d => ({ ...d, position })) as Row<TData>[]\n    }\n\n    table.getTopRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.top],\n      (allRows, topPinnedRowIds) =>\n        table._getPinnedRows(allRows, topPinnedRowIds, 'top'),\n      getMemoOptions(table.options, 'debugRows', 'getTopRows')\n    )\n\n    table.getBottomRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.bottom],\n      (allRows, bottomPinnedRowIds) =>\n        table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'),\n      getMemoOptions(table.options, 'debugRows', 'getBottomRows')\n    )\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterRows')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  Row,\n  <PERSON>Model,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel')\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel')\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel')\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n", "import { SortingFn } from './features/RowSorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n  TableFeature,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * The priority of undefined values when sorting this column.\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortUndefined?: false | -1 | 1 | 'first' | 'last'\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const RowSorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n", "import { functionalUpdate, getMemoOptions, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n  TableFeature,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnFaceting } from '../features/ColumnFaceting'\nimport { ColumnFiltering } from '../features/ColumnFiltering'\nimport { ColumnGrouping } from '../features/ColumnGrouping'\nimport { ColumnOrdering } from '../features/ColumnOrdering'\nimport { ColumnPinning } from '../features/ColumnPinning'\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { ColumnVisibility } from '../features/ColumnVisibility'\nimport { GlobalFaceting } from '../features/GlobalFaceting'\nimport { GlobalFiltering } from '../features/GlobalFiltering'\nimport { RowExpanding } from '../features/RowExpanding'\nimport { RowPagination } from '../features/RowPagination'\nimport { RowPinning } from '../features/RowPinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { RowSorting } from '../features/RowSorting'\n\nconst builtInFeatures = [\n  Headers,\n  ColumnVisibility,\n  ColumnOrdering,\n  ColumnPinning,\n  ColumnFaceting,\n  ColumnFiltering,\n  GlobalFaceting, //depends on ColumnFaceting\n  GlobalFiltering, //depends on ColumnFiltering\n  RowSorting,\n  ColumnGrouping, //depends on RowSorting\n  RowExpanding,\n  RowPagination,\n  RowPinning,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * An array of extra features that you can add to the table instance.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#_features)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  _features?: TableFeature[]\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output cell debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcells]\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugCells?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (options.debugAll || options.debugTable)\n  ) {\n    console.info('Creating Table Instance...')\n  }\n\n  const _features = [...builtInFeatures, ...(options._features ?? [])]\n\n  let table = { _features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = (feature.getInitialState?.(initialState) ??\n      initialState) as TableState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllColumns')\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel')\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const uniqueValues = facetedRowModel.flatRows\n          .flatMap(flatRow => flatRow.getUniqueValues(columnId) ?? [])\n          .map(Number)\n          .filter(value => !Number.isNaN(value))\n\n        if (!uniqueValues.length) return\n\n        let facetedMinValue = uniqueValues[0]!\n        let facetedMaxValue = uniqueValues[uniqueValues.length - 1]!\n\n        for (const value of uniqueValues) {\n          if (value < facetedMinValue) facetedMinValue = value\n          else if (value > facetedMaxValue) facetedMaxValue = value\n        }\n\n        return [facetedMinValue, facetedMaxValue]\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues')\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nfunction filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nfunction filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel')\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      getMemoOptions(\n        table.options,\n        'debugTable',\n        `getFacetedUniqueValues_${columnId}`\n      )\n    )\n}\n", "import { ResolvedColumnFilter } from '../features/ColumnFiltering'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = (columnFilters ?? []).map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowData, RowModel, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { GroupingState } from '../features/ColumnGrouping'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          rowModel.rows.forEach(row => {\n            row.depth = 0\n            row.parentId = undefined\n          })\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Perform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              subRows.forEach(subRow => {\n                subRow.parentId = id\n              })\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n        table._queue(() => {\n          table._autoResetExpanded()\n          table._autoResetPageIndex()\n        })\n      })\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel')\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/RowSorting'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(sort =>\n          table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1 | 'first' | 'last'\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const sortUndefined = columnInfo.sortUndefined\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  if (sortUndefined === 'first') return aUndefined ? -1 : 1\n                  if (sortUndefined === 'last') return aUndefined ? 1 : -1\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? sortUndefined\n                        : -sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import * as React from 'react'\nexport * from '@tanstack/table-core'\n\nimport {\n  TableOptions,\n  TableOptionsResolved,\n  RowData,\n  createTable,\n} from '@tanstack/table-core'\n\nexport type Renderable<TProps> = React.ReactNode | React.ComponentType<TProps>\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nexport function flexRender<TProps extends object>(\n  Comp: Renderable<TProps>,\n  props: TProps\n): React.ReactNode | React.JSX.Element {\n  return !Comp ? null : isReactComponent<TProps>(Comp) ? (\n    <Comp {...props} />\n  ) : (\n    Comp\n  )\n}\n\nfunction isReactComponent<TProps>(\n  component: unknown\n): component is React.ComponentType<TProps> {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component: any) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component: any) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n\nexport function useReactTable<TData extends RowData>(\n  options: TableOptions<TData>\n) {\n  // Compose in the generic options to the user options\n  const resolvedOptions: TableOptionsResolved<TData> = {\n    state: {}, // Dummy state\n    onStateChange: () => {}, // noop\n    renderFallbackValue: null,\n    ...options,\n  }\n\n  // Create a new table and store it in state\n  const [tableRef] = React.useState(() => ({\n    current: createTable<TData>(resolvedOptions),\n  }))\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = React.useState(() => tableRef.current.initialState)\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state,\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater)\n      options.onStateChange?.(updater)\n    },\n  }))\n\n  return tableRef.current\n}\n"], "mappings": ";;;;;;;;;;;AAwEO,SAASA,qBAES;AACvB,SAAO;IACLC,UAAUA,CAACA,UAAUC,WAAW;AAC9B,aAAO,OAAOD,aAAa,aACtB;QACC,GAAGC;QACHC,YAAYF;MACd,IACA;QACE,GAAGC;QACHE,aAAaH;;;IAGrBI,SAASH,YAAUA;IACnBI,OAAOJ,YAAUA;;AAErB;ACVO,SAASK,iBAAoBC,SAAqBC,OAAa;AACpE,SAAO,OAAOD,YAAY,aACrBA,QAA4BC,KAAK,IAClCD;AACN;AAEO,SAASE,OAAO;AACrB;AAGK,SAASC,iBACdC,KACAC,UACA;AACA,SAAQL,aAAoC;AACxCK,aAAiBC,SAAuBC,SAAqB;AAC7D,aAAO;QACL,GAAGA;QACH,CAACH,GAAG,GAAGL,iBAAiBC,SAAUO,IAAYH,GAAG,CAAC;;IAEtD,CAAC;;AAEL;AAIO,SAASI,WAAkCC,GAAgB;AAChE,SAAOA,aAAaC;AACtB;AAEO,SAASC,cAAcF,GAAuB;AACnD,SAAOG,MAAMC,QAAQJ,CAAC,KAAKA,EAAEK,MAAMC,SAAO,OAAOA,QAAQ,QAAQ;AACnE;AAEO,SAASC,UACdC,KACAC,aACA;AACA,QAAMC,OAAgB,CAAA;AAEtB,QAAMC,UAAWC,YAAoB;AACnCA,WAAOC,QAAQC,UAAQ;AACrBJ,WAAKK,KAAKD,IAAI;AACd,YAAME,WAAWP,YAAYK,IAAI;AACjC,UAAIE,YAAQ,QAARA,SAAUC,QAAQ;AACpBN,gBAAQK,QAAQ;MAClB;IACF,CAAC;;AAGHL,UAAQH,GAAG;AAEX,SAAOE;AACT;AAEO,SAASQ,KACdC,SACAC,IACAC,MAKiC;AACjC,MAAIC,OAAc,CAAA;AAClB,MAAIC;AAEJ,SAAOC,aAAW;AAChB,QAAIC;AACJ,QAAIJ,KAAK1B,OAAO0B,KAAKK,MAAOD,WAAUE,KAAKC,IAAG;AAE9C,UAAMC,UAAUV,QAAQK,OAAO;AAE/B,UAAMM,cACJD,QAAQZ,WAAWK,KAAKL,UACxBY,QAAQE,KAAK,CAACC,KAAUC,UAAkBX,KAAKW,KAAK,MAAMD,GAAG;AAE/D,QAAI,CAACF,aAAa;AAChB,aAAOP;IACT;AAEAD,WAAOO;AAEP,QAAIK;AACJ,QAAIb,KAAK1B,OAAO0B,KAAKK,MAAOQ,cAAaP,KAAKC,IAAG;AAEjDL,aAASH,GAAG,GAAGS,OAAO;AACtBR,YAAI,QAAJA,KAAMc,YAAQ,QAAdd,KAAMc,SAAWZ,MAAM;AAEvB,QAAIF,KAAK1B,OAAO0B,KAAKK,OAAO;AAC1B,UAAIL,QAAAA,QAAAA,KAAMK,MAAK,GAAI;AACjB,cAAMU,aAAaC,KAAKC,OAAOX,KAAKC,IAAG,IAAKH,WAAY,GAAG,IAAI;AAC/D,cAAMc,gBAAgBF,KAAKC,OAAOX,KAAKC,IAAG,IAAKM,cAAe,GAAG,IAAI;AACrE,cAAMM,sBAAsBD,gBAAgB;AAE5C,cAAME,MAAMA,CAACC,KAAsBC,QAAgB;AACjDD,gBAAME,OAAOF,GAAG;AAChB,iBAAOA,IAAIzB,SAAS0B,KAAK;AACvBD,kBAAM,MAAMA;UACd;AACA,iBAAOA;;AAGTG,gBAAQC,KACN,OAAOL,IAAIF,eAAe,CAAC,CAAC,KAAKE,IAAIL,YAAY,CAAC,CAAC,OACnD;;;yBAGeC,KAAKU,IAChB,GACAV,KAAKW,IAAI,MAAM,MAAMR,qBAAqB,GAAG,CAC/C,CAAC,kBACHnB,QAAAA,OAAAA,SAAAA,KAAM1B,GACR;MACF;IACF;AAEA,WAAO4B;;AAEX;AAEO,SAAS0B,eACdC,cACAC,YAOAxD,KACAwC,UACA;AACA,SAAO;IACLT,OAAOA,MAAA;AAAA,UAAA0B;AAAA,cAAAA,wBAAMF,gBAAY,OAAA,SAAZA,aAAcG,aAAQ,OAAAD,wBAAIF,aAAaC,UAAU;IAAC;IAC/DxD;IACAwC;;AAEJ;ACvKO,SAASmB,WACdC,OACAC,KACAvE,QACAwE,UACqB;AACrB,QAAMC,iBAAiBA,MAAA;AAAA,QAAAC;AAAA,YAAAA,iBACrBC,KAAKC,SAAQ,MAAEF,OAAAA,iBAAIJ,MAAMO,QAAQC;EAAmB;AAEtD,QAAMH,OAAgC;IACpCI,IAAI,GAAGR,IAAIQ,EAAE,IAAI/E,OAAO+E,EAAE;IAC1BR;IACAvE;IACA4E,UAAUA,MAAML,IAAIK,SAASJ,QAAQ;IACrCQ,aAAaP;IACbQ,YAAYhD,KACV,MAAM,CAACqC,OAAOtE,QAAQuE,KAAKI,IAAI,GAC/B,CAACL,QAAOtE,SAAQuE,MAAKI,WAAU;MAC7BL,OAAAA;MACAtE,QAAAA;MACAuE,KAAAA;MACAI,MAAMA;MACNC,UAAUD,MAAKC;MACfI,aAAaL,MAAKK;QAEpBhB,eAAeM,MAAMO,SAAS,cAAc,iBAAiB,CAC/D;;AAGFP,QAAMY,UAAUtD,QAAQuD,aAAW;AACjCA,YAAQd,cAARc,QAAAA,QAAQd,WACNM,MACA3E,QACAuE,KACAD,KACF;KACC,CAAA,CAAE;AAEL,SAAOK;AACT;AC1BO,SAASS,aACdd,OACAe,WACAC,OACAC,QACuB;AAAA,MAAAC,MAAAC;AACvB,QAAMC,gBAAgBpB,MAAMqB,qBAAoB;AAEhD,QAAMC,oBAAoB;IACxB,GAAGF;IACH,GAAGL;;AAGL,QAAMnF,cAAc0F,kBAAkB1F;AAEtC,MAAI6E,MAAES,QAAAC,wBACJG,kBAAkBb,OAAEU,OAAAA,wBACnBvF,cACG,OAAOyD,OAAOkC,UAAUC,eAAe,aACrC5F,YAAY4F,WAAW,KAAK,GAAG,IAC/B5F,YAAY6F,QAAQ,OAAO,GAAG,IAChCC,WAAS,OAAAR,OACZ,OAAOI,kBAAkBK,WAAW,WACjCL,kBAAkBK,SAClBD;AAEN,MAAI/F;AAEJ,MAAI2F,kBAAkB3F,YAAY;AAChCA,iBAAa2F,kBAAkB3F;aACtBC,aAAa;AAEtB,QAAIA,YAAYgG,SAAS,GAAG,GAAG;AAC7BjG,mBAAckG,iBAAuB;AACnC,YAAI7D,SAAS6D;AAEb,mBAAWzF,OAAOR,YAAYkG,MAAM,GAAG,GAAG;AAAA,cAAAC;AACxC/D,oBAAM+D,UAAG/D,WAAM,OAAA,SAAN+D,QAAS3F,GAAG;AACrB,cAA6C4B,WAAW0D,QAAW;AACjEpC,oBAAQ0C,KACN,IAAI5F,GAAG,2BAA2BR,WAAW,uBAC/C;UACF;QACF;AAEA,eAAOoC;;IAEX,OAAO;AACLrC,mBAAckG,iBACXA,YAAoBP,kBAAkB1F,WAAW;IACtD;EACF;AAEA,MAAI,CAAC6E,IAAI;AACP,QAAIwB,MAAuC;AACzC,YAAM,IAAIC,MACRZ,kBAAkB3F,aACd,mDACA,sDACN;IACF;AACA,UAAM,IAAIuG,MAAK;EACjB;AAEA,MAAIxG,SAAiC;IACnC+E,IAAI,GAAGpB,OAAOoB,EAAE,CAAC;IACjB9E;IACAsF;IACAD;IACAD,WAAWO;IACXa,SAAS,CAAA;IACTC,gBAAgBzE,KACd,MAAM,CAAC,IAAI,GACX,MAAM;AAAA,UAAA0E;AACJ,aAAO,CACL3G,QACA,IAAA2G,kBAAG3G,OAAOyG,YAAPE,OAAAA,SAAAA,gBAAgBC,QAAQ7F,OAAKA,EAAE2F,eAAc,CAAE,CAAC;OAGvD1C,eAAeM,MAAMO,SAAS,gBAAgB,uBAAuB,CACvE;IACAgC,gBAAgB5E,KACd,MAAM,CAACqC,MAAMwC,mBAAkB,CAAE,GACjCC,CAAAA,kBAAgB;AAAA,UAAAC;AACd,WAAAA,mBAAIhH,OAAOyG,YAAPO,QAAAA,iBAAgBhF,QAAQ;AAC1B,YAAIiF,cAAcjH,OAAOyG,QAAQG,QAAQ5G,CAAAA,YACvCA,QAAO6G,eAAc,CACvB;AAEA,eAAOE,cAAaE,WAAW;MACjC;AAEA,aAAO,CAACjH,MAAM;OAEhBgE,eAAeM,MAAMO,SAAS,gBAAgB,uBAAuB,CACvE;;AAGF,aAAWM,WAAWb,MAAMY,WAAW;AACrCC,YAAQC,gBAAY,QAApBD,QAAQC,aAAepF,QAAiCsE,KAAK;EAC/D;AAGA,SAAOtE;AACT;AC9JA,IAAMyC,QAAQ;AA0Md,SAASyE,aACP5C,OACAtE,QACA6E,SAOuB;AAAA,MAAAsC;AACvB,QAAMpC,MAAEoC,cAAGtC,QAAQE,OAAE,OAAAoC,cAAInH,OAAO+E;AAEhC,MAAIkB,SAAoC;IACtClB;IACA/E;IACAgD,OAAO6B,QAAQ7B;IACfoE,eAAe,CAAC,CAACvC,QAAQuC;IACzBC,eAAexC,QAAQwC;IACvB/B,OAAOT,QAAQS;IACfgC,YAAY,CAAA;IACZC,SAAS;IACTC,SAAS;IACTC,aAAa;IACbC,gBAAgBA,MAAgC;AAC9C,YAAMC,cAAwC,CAAA;AAE9C,YAAMC,gBAAiBC,OAA8B;AACnD,YAAIA,EAAEP,cAAcO,EAAEP,WAAWtF,QAAQ;AACvC6F,YAAEP,WAAWQ,IAAIF,aAAa;QAChC;AACAD,oBAAY7F,KAAK+F,CAA2B;;AAG9CD,oBAAc3B,MAAM;AAEpB,aAAO0B;;IAET1C,YAAYA,OAAO;MACjBX;MACA2B;MACAjG;;;AAIJsE,QAAMY,UAAUtD,QAAQuD,aAAW;AACjCA,YAAQ+B,gBAAY,QAApB/B,QAAQ+B,aAAejB,QAAiC3B,KAAK;EAC/D,CAAC;AAED,SAAO2B;AACT;AAEO,IAAM8B,UAAwB;EACnCC,aAAqC1D,WAA8B;AAGjEA,UAAM2D,kBAAkBhG,KACtB,MAAM,CACJqC,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAYvB,aAAaqB,MAAMC,UAAU;AAAA,UAAAE,kBAAAC;AACxC,YAAMC,eAAWF,mBACfH,QAAAA,OAAAA,SAAAA,KACIR,IAAItD,cAAYyC,YAAY2B,KAAK7H,OAAKA,EAAEgE,OAAOP,QAAQ,CAAE,EAC1DqE,OAAOC,OAAO,MAACL,OAAAA,mBAAI,CAAA;AAExB,YAAMM,gBAAYL,oBAChBH,SAAAA,OAAAA,SAAAA,MACIT,IAAItD,cAAYyC,YAAY2B,KAAK7H,OAAKA,EAAEgE,OAAOP,QAAQ,CAAE,EAC1DqE,OAAOC,OAAO,MAACJ,OAAAA,oBAAI,CAAA;AAExB,YAAMM,gBAAgB/B,YAAY4B,OAChC7I,YAAU,EAACsI,QAAI,QAAJA,KAAMpC,SAASlG,OAAO+E,EAAE,MAAK,EAACwD,SAAK,QAALA,MAAOrC,SAASlG,OAAO+E,EAAE,EACpE;AAEA,YAAMkE,eAAeC,kBACnBV,YACA,CAAC,GAAGG,aAAa,GAAGK,eAAe,GAAGD,YAAY,GAClDzE,KACF;AAEA,aAAO2E;OAETjF,eAAeM,MAAMO,SAASpC,OAAO,iBAAiB,CACxD;AAEA6B,UAAM6E,wBAAwBlH,KAC5B,MAAM,CACJqC,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAYvB,aAAaqB,MAAMC,UAAU;AACxCtB,oBAAcA,YAAY4B,OACxB7I,YAAU,EAACsI,QAAI,QAAJA,KAAMpC,SAASlG,OAAO+E,EAAE,MAAK,EAACwD,SAAK,QAALA,MAAOrC,SAASlG,OAAO+E,EAAE,EACpE;AACA,aAAOmE,kBAAkBV,YAAYvB,aAAa3C,OAAO,QAAQ;OAEnEN,eAAeM,MAAMO,SAASpC,OAAO,uBAAuB,CAC9D;AAEA6B,UAAM8E,sBAAsBnH,KAC1B,MAAM,CACJqC,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcC,IAAI,GAErC,CAACE,YAAYvB,aAAaqB,SAAS;AAAA,UAAAe;AACjC,YAAMC,sBAAkBD,oBACtBf,QAAAA,OAAAA,SAAAA,KACIR,IAAItD,cAAYyC,YAAY2B,KAAK7H,OAAKA,EAAEgE,OAAOP,QAAQ,CAAE,EAC1DqE,OAAOC,OAAO,MAACO,OAAAA,oBAAI,CAAA;AAExB,aAAOH,kBAAkBV,YAAYc,oBAAoBhF,OAAO,MAAM;OAExEN,eAAeM,MAAMO,SAASpC,OAAO,qBAAqB,CAC5D;AAEA6B,UAAMiF,uBAAuBtH,KAC3B,MAAM,CACJqC,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAYvB,aAAasB,UAAU;AAAA,UAAAiB;AAClC,YAAMF,sBAAkBE,qBACtBjB,SAAAA,OAAAA,SAAAA,MACIT,IAAItD,cAAYyC,YAAY2B,KAAK7H,OAAKA,EAAEgE,OAAOP,QAAQ,CAAE,EAC1DqE,OAAOC,OAAO,MAACU,OAAAA,qBAAI,CAAA;AAExB,aAAON,kBAAkBV,YAAYc,oBAAoBhF,OAAO,OAAO;OAEzEN,eAAeM,MAAMO,SAASpC,OAAO,sBAAsB,CAC7D;AAIA6B,UAAMmF,kBAAkBxH,KACtB,MAAM,CAACqC,MAAM2D,gBAAe,CAAE,GAC9BgB,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAES,QAAO;OAElC1F,eAAeM,MAAMO,SAASpC,OAAO,iBAAiB,CACxD;AAEA6B,UAAMqF,sBAAsB1H,KAC1B,MAAM,CAACqC,MAAM8E,oBAAmB,CAAE,GAClCH,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAES,QAAO;OAElC1F,eAAeM,MAAMO,SAASpC,OAAO,qBAAqB,CAC5D;AAEA6B,UAAMsF,wBAAwB3H,KAC5B,MAAM,CAACqC,MAAM6E,sBAAqB,CAAE,GACpCF,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAES,QAAO;OAElC1F,eAAeM,MAAMO,SAASpC,OAAO,uBAAuB,CAC9D;AAEA6B,UAAMuF,uBAAuB5H,KAC3B,MAAM,CAACqC,MAAMiF,qBAAoB,CAAE,GACnCN,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAES,QAAO;OAElC1F,eAAeM,MAAMO,SAASpC,OAAO,sBAAsB,CAC7D;AAIA6B,UAAMwF,iBAAiB7H,KACrB,MAAM,CAACqC,MAAM2D,gBAAe,CAAE,GAC9BgB,kBAAgB;AACd,aAAOA,aACJnB,IAAIL,iBAAe;AAClB,eAAOA,YAAYsC;MACrB,CAAC,EACAtI,KAAI;OAETuC,eAAeM,MAAMO,SAASpC,OAAO,gBAAgB,CACvD;AAEA6B,UAAM0F,qBAAqB/H,KACzB,MAAM,CAACqC,MAAM8E,oBAAmB,CAAE,GAClCd,UAAQ;AACN,aAAOA,KACJR,IAAIL,iBAAe;AAClB,eAAOA,YAAYsC;MACrB,CAAC,EACAtI,KAAI;OAETuC,eAAeM,MAAMO,SAASpC,OAAO,oBAAoB,CAC3D;AAEA6B,UAAM2F,uBAAuBhI,KAC3B,MAAM,CAACqC,MAAM6E,sBAAqB,CAAE,GACpCb,UAAQ;AACN,aAAOA,KACJR,IAAIL,iBAAe;AAClB,eAAOA,YAAYsC;MACrB,CAAC,EACAtI,KAAI;OAETuC,eAAeM,MAAMO,SAASpC,OAAO,sBAAsB,CAC7D;AAEA6B,UAAM4F,sBAAsBjI,KAC1B,MAAM,CAACqC,MAAMiF,qBAAoB,CAAE,GACnCjB,UAAQ;AACN,aAAOA,KACJR,IAAIL,iBAAe;AAClB,eAAOA,YAAYsC;MACrB,CAAC,EACAtI,KAAI;OAETuC,eAAeM,MAAMO,SAASpC,OAAO,qBAAqB,CAC5D;AAIA6B,UAAM6F,uBAAuBlI,KAC3B,MAAM,CAACqC,MAAM2F,qBAAoB,CAAE,GACnCG,iBAAe;AACb,aAAOA,YAAYvB,OAAO5C,YAAM;AAAA,YAAAoE;AAAA,eAAI,GAAAA,qBAACpE,OAAOqB,eAAU,QAAjB+C,mBAAmBrI;OAAO;OAEjEgC,eAAeM,MAAMO,SAASpC,OAAO,sBAAsB,CAC7D;AAEA6B,UAAMgG,qBAAqBrI,KACzB,MAAM,CAACqC,MAAM0F,mBAAkB,CAAE,GACjCI,iBAAe;AACb,aAAOA,YAAYvB,OAAO5C,YAAM;AAAA,YAAAsE;AAAA,eAAI,GAAAA,sBAACtE,OAAOqB,eAAU,QAAjBiD,oBAAmBvI;OAAO;OAEjEgC,eAAeM,MAAMO,SAASpC,OAAO,oBAAoB,CAC3D;AAEA6B,UAAMkG,sBAAsBvI,KAC1B,MAAM,CAACqC,MAAM4F,oBAAmB,CAAE,GAClCE,iBAAe;AACb,aAAOA,YAAYvB,OAAO5C,YAAM;AAAA,YAAAwE;AAAA,eAAI,GAAAA,sBAACxE,OAAOqB,eAAU,QAAjBmD,oBAAmBzI;OAAO;OAEjEgC,eAAeM,MAAMO,SAASpC,OAAO,qBAAqB,CAC5D;AAEA6B,UAAMoD,iBAAiBzF,KACrB,MAAM,CACJqC,MAAM8E,oBAAmB,GACzB9E,MAAM6E,sBAAqB,GAC3B7E,MAAMiF,qBAAoB,CAAE,GAE9B,CAACjB,MAAMoC,QAAQnC,UAAU;AAAA,UAAAoC,iBAAAC,QAAAC,mBAAAC,UAAAC,kBAAAC;AACvB,aAAO,CACL,IAAAL,mBAAAC,SAAItC,KAAK,CAAC,MAANsC,OAAAA,SAAAA,OAASb,YAAOY,OAAAA,kBAAI,CAAA,GACxB,IAAAE,qBAAAC,WAAIJ,OAAO,CAAC,MAARI,OAAAA,SAAAA,SAAWf,YAAOc,OAAAA,oBAAI,CAAA,GAC1B,IAAAE,oBAAAC,UAAIzC,MAAM,CAAC,MAAC,OAAA,SAARyC,QAAUjB,YAAO,OAAAgB,mBAAI,CAAA,CAAE,EAE1BjD,IAAI7B,YAAU;AACb,eAAOA,OAAOyB,eAAc;MAC9B,CAAC,EACAjG,KAAI;OAETuC,eAAeM,MAAMO,SAASpC,OAAO,gBAAgB,CACvD;EACF;AACF;AAEO,SAASyG,kBACdV,YACAyC,gBACA3G,OACA4G,cACA;AAAA,MAAAC,uBAAAC;AAOA,MAAIC,WAAW;AAEf,QAAMC,eAAe,SAAC7E,SAAmCnB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAChE+F,eAAWjI,KAAKU,IAAIuH,UAAU/F,KAAK;AAEnCmB,YACGoC,OAAO7I,YAAUA,OAAOuL,aAAY,CAAE,EACtC3J,QAAQ5B,YAAU;AAAA,UAAA2G;AACjB,WAAAA,kBAAI3G,OAAOyG,YAAPE,QAAAA,gBAAgB3E,QAAQ;AAC1BsJ,qBAAatL,OAAOyG,SAASnB,QAAQ,CAAC;MACxC;OACC,CAAC;;AAGRgG,eAAa9C,UAAU;AAEvB,MAAIS,eAAqC,CAAA;AAEzC,QAAMuC,oBAAoBA,CACxBC,gBACAnG,UACG;AAEH,UAAMmC,cAAkC;MACtCnC;MACAP,IAAI,CAACmG,cAAc,GAAG5F,KAAK,EAAE,EAAEuD,OAAOC,OAAO,EAAE4C,KAAK,GAAG;MACvD3B,SAAS,CAAA;;AAIX,UAAM4B,uBAAiD,CAAA;AAGvDF,mBAAe7J,QAAQgK,mBAAiB;AAGtC,YAAMC,4BAA4B,CAAC,GAAGF,oBAAoB,EAAEjC,QAAO,EAAG,CAAC;AAEvE,YAAMoC,eAAeF,cAAc5L,OAAOsF,UAAUmC,YAAYnC;AAEhE,UAAItF;AACJ,UAAIoH,gBAAgB;AAEpB,UAAI0E,gBAAgBF,cAAc5L,OAAOuF,QAAQ;AAE/CvF,iBAAS4L,cAAc5L,OAAOuF;MAChC,OAAO;AAELvF,iBAAS4L,cAAc5L;AACvBoH,wBAAgB;MAClB;AAEA,UACEyE,8BACAA,6BAAyB,OAAA,SAAzBA,0BAA2B7L,YAAWA,QACtC;AAEA6L,kCAA0BvE,WAAWxF,KAAK8J,aAAa;MACzD,OAAO;AAEL,cAAM3F,SAASiB,aAAa5C,OAAOtE,QAAQ;UACzC+E,IAAI,CAACmG,cAAc5F,OAAOtF,OAAO+E,IAAI6G,iBAAa,OAAA,SAAbA,cAAe7G,EAAE,EACnD8D,OAAOC,OAAO,EACd4C,KAAK,GAAG;UACXtE;UACAC,eAAeD,gBACX,GAAGuE,qBAAqB9C,OAAO9H,OAAKA,EAAEf,WAAWA,MAAM,EAAEgC,MAAM,KAC/DgE;UACJV;UACAtC,OAAO2I,qBAAqB3J;QAC9B,CAAC;AAGDiE,eAAOqB,WAAWxF,KAAK8J,aAAa;AAGpCD,6BAAqB7J,KAAKmE,MAAM;MAClC;AAEAwB,kBAAYsC,QAAQjI,KAAK8J,aAAa;AACtCA,oBAAcnE,cAAcA;IAC9B,CAAC;AAEDwB,iBAAanH,KAAK2F,WAAW;AAE7B,QAAInC,QAAQ,GAAG;AACbkG,wBAAkBG,sBAAsBrG,QAAQ,CAAC;IACnD;;AAGF,QAAMyG,gBAAgBd,eAAenD,IAAI,CAAC9H,QAAQgD,UAChDkE,aAAa5C,OAAOtE,QAAQ;IAC1BsF,OAAO+F;IACPrI;EACF,CAAC,CACH;AAEAwI,oBAAkBO,eAAeV,WAAW,CAAC;AAE7CpC,eAAaS,QAAO;AAMpB,QAAMsC,yBACJjC,aAC2C;AAC3C,UAAMkC,kBAAkBlC,QAAQlB,OAAO5C,YACrCA,OAAOjG,OAAOuL,aAAY,CAC5B;AAEA,WAAOU,gBAAgBnE,IAAI7B,YAAU;AACnC,UAAIsB,UAAU;AACd,UAAIC,UAAU;AACd,UAAI0E,gBAAgB,CAAC,CAAC;AAEtB,UAAIjG,OAAOqB,cAAcrB,OAAOqB,WAAWtF,QAAQ;AACjDkK,wBAAgB,CAAA;AAEhBF,+BAAuB/F,OAAOqB,UAAU,EAAE1F,QACxC4D,UAAsD;AAAA,cAArD;YAAE+B,SAAS4E;YAAc3E,SAAS4E;UAAa,IAAC5G;AAC/C+B,qBAAW4E;AACXD,wBAAcpK,KAAKsK,YAAY;QACjC,CACF;MACF,OAAO;AACL7E,kBAAU;MACZ;AAEA,YAAM8E,kBAAkBjJ,KAAKW,IAAI,GAAGmI,aAAa;AACjD1E,gBAAUA,UAAU6E;AAEpBpG,aAAOsB,UAAUA;AACjBtB,aAAOuB,UAAUA;AAEjB,aAAO;QAAED;QAASC;;IACpB,CAAC;;AAGHwE,0BAAsBb,yBAAAC,iBAACnC,aAAa,CAAC,MAAC,OAAA,SAAfmC,eAAiBrB,YAAO,OAAAoB,wBAAI,CAAA,CAAE;AAErD,SAAOlC;AACT;IChiBaqD,YAAYA,CACvBhI,OACAS,IACAwH,UACAC,UACAlH,OACAmH,SACAC,aACe;AACf,MAAInI,MAAsB;IACxBQ;IACA/B,OAAOwJ;IACPD;IACAjH;IACAoH;IACAC,cAAc,CAAA;IACdC,oBAAoB,CAAA;IACpBhI,UAAUJ,cAAY;AACpB,UAAID,IAAIoI,aAAaE,eAAerI,QAAQ,GAAG;AAC7C,eAAOD,IAAIoI,aAAanI,QAAQ;MAClC;AAEA,YAAMxE,SAASsE,MAAMwI,UAAUtI,QAAQ;AAEvC,UAAI,EAACxE,UAAM,QAANA,OAAQC,aAAY;AACvB,eAAO+F;MACT;AAEAzB,UAAIoI,aAAanI,QAAQ,IAAIxE,OAAOC,WAClCsE,IAAIgI,UACJC,QACF;AAEA,aAAOjI,IAAIoI,aAAanI,QAAQ;;IAElCuI,iBAAiBvI,cAAY;AAC3B,UAAID,IAAIqI,mBAAmBC,eAAerI,QAAQ,GAAG;AACnD,eAAOD,IAAIqI,mBAAmBpI,QAAQ;MACxC;AAEA,YAAMxE,SAASsE,MAAMwI,UAAUtI,QAAQ;AAEvC,UAAI,EAACxE,UAAM,QAANA,OAAQC,aAAY;AACvB,eAAO+F;MACT;AAEA,UAAI,CAAChG,OAAOqF,UAAU0H,iBAAiB;AACrCxI,YAAIqI,mBAAmBpI,QAAQ,IAAI,CAACD,IAAIK,SAASJ,QAAQ,CAAC;AAC1D,eAAOD,IAAIqI,mBAAmBpI,QAAQ;MACxC;AAEAD,UAAIqI,mBAAmBpI,QAAQ,IAAIxE,OAAOqF,UAAU0H,gBAClDxI,IAAIgI,UACJC,QACF;AAEA,aAAOjI,IAAIqI,mBAAmBpI,QAAQ;;IAExCQ,aAAaR,cAAQ;AAAA,UAAAwI;AAAA,cAAAA,gBACnBzI,IAAIK,SAASJ,QAAQ,MAAC,OAAAwI,gBAAI1I,MAAMO,QAAQC;IAAmB;IAC7D2H,SAASA,WAAAA,OAAAA,UAAW,CAAA;IACpBQ,aAAaA,MAAM3L,UAAUiD,IAAIkI,SAAS1L,OAAKA,EAAE0L,OAAO;IACxDS,cAAcA,MACZ3I,IAAImI,WAAWpI,MAAM6I,OAAO5I,IAAImI,UAAU,IAAI,IAAI1G;IACpDoH,eAAeA,MAAM;AACnB,UAAIC,aAA2B,CAAA;AAC/B,UAAIC,aAAa/I;AACjB,aAAO,MAAM;AACX,cAAMgJ,YAAYD,WAAWJ,aAAY;AACzC,YAAI,CAACK,UAAW;AAChBF,mBAAWvL,KAAKyL,SAAS;AACzBD,qBAAaC;MACf;AACA,aAAOF,WAAW3D,QAAO;;IAE3B8D,aAAavL,KACX,MAAM,CAACqC,MAAMmJ,kBAAiB,CAAE,GAChCxG,iBAAe;AACb,aAAOA,YAAYa,IAAI9H,YAAU;AAC/B,eAAOqE,WAAWC,OAAOC,KAAmBvE,QAAQA,OAAO+E,EAAE;MAC/D,CAAC;OAEHf,eAAeM,MAAMO,SAAS,aAAa,aAAa,CAC1D;IAEA6I,wBAAwBzL,KACtB,MAAM,CAACsC,IAAIiJ,YAAW,CAAE,GACxBG,cAAY;AACV,aAAOA,SAASC,OACd,CAACC,KAAKlJ,SAAS;AACbkJ,YAAIlJ,KAAK3E,OAAO+E,EAAE,IAAIJ;AACtB,eAAOkJ;SAET,CAAA,CACF;OAEF7J,eAAeM,MAAMO,SAAS,aAAa,uBAAuB,CACpE;;AAGF,WAASiJ,IAAI,GAAGA,IAAIxJ,MAAMY,UAAUlD,QAAQ8L,KAAK;AAC/C,UAAM3I,UAAUb,MAAMY,UAAU4I,CAAC;AACjC3I,eAAAA,QAAAA,QAASmH,aAATnH,QAAAA,QAASmH,UAAY/H,KAAmBD,KAAK;EAC/C;AAEA,SAAOC;AACT;ACzJO,IAAMwJ,iBAA+B;EAC1C3I,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAOgO,sBACL1J,MAAMO,QAAQoJ,sBACd3J,MAAMO,QAAQoJ,mBAAmB3J,OAAOtE,OAAO+E,EAAE;AACnD/E,WAAOiO,qBAAqB,MAAM;AAChC,UAAI,CAACjO,OAAOgO,qBAAqB;AAC/B,eAAO1J,MAAM4J,uBAAsB;MACrC;AAEA,aAAOlO,OAAOgO,oBAAmB;;AAEnChO,WAAOmO,0BACL7J,MAAMO,QAAQuJ,0BACd9J,MAAMO,QAAQuJ,uBAAuB9J,OAAOtE,OAAO+E,EAAE;AACvD/E,WAAOoO,yBAAyB,MAAM;AACpC,UAAI,CAACpO,OAAOmO,yBAAyB;AACnC,eAAO,oBAAIE,IAAG;MAChB;AAEA,aAAOrO,OAAOmO,wBAAuB;;AAEvCnO,WAAOsO,0BACLhK,MAAMO,QAAQ0J,0BACdjK,MAAMO,QAAQ0J,uBAAuBjK,OAAOtE,OAAO+E,EAAE;AACvD/E,WAAOuO,yBAAyB,MAAM;AACpC,UAAI,CAACvO,OAAOsO,yBAAyB;AACnC,eAAOtI;MACT;AAEA,aAAOhG,OAAOsO,wBAAuB;;EAEzC;AACF;ACjFA,IAAME,iBAAgCA,CACpCjK,KACAC,UACAiK,gBACG;AAAA,MAAAC,uBAAA1B;AACH,QAAM2B,SAASF,eAAWC,SAAAA,wBAAXD,YAAaG,SAAQ,MAArBF,OAAAA,SAAAA,sBAAyBG,YAAW;AACnD,SAAO/F,SAAOkE,gBACZzI,IACGK,SAAwBJ,QAAQ,MAAC,SAAAwI,gBADpCA,cAEI4B,SAAQ,MAAE5B,SAAAA,gBAFdA,cAGI6B,YAAW,MAAE,OAAA,SAHjB7B,cAII9G,SAASyI,MAAM,CACrB;AACF;AAEAH,eAAeM,aAAczN,SAAa0N,WAAW1N,GAAG;AAExD,IAAM2N,0BAAyCA,CAC7CzK,KACAC,UACAiK,gBACG;AAAA,MAAAQ;AACH,SAAOnG,SAAOmG,iBACZ1K,IAAIK,SAAwBJ,QAAQ,MAACyK,SAAAA,iBAArCA,eAAuCL,SAAQ,MAAE,OAAA,SAAjDK,eAAmD/I,SAASuI,WAAW,CACzE;AACF;AAEAO,wBAAwBF,aAAczN,SAAa0N,WAAW1N,GAAG;AAEjE,IAAM6N,eAA8BA,CAClC3K,KACAC,UACAiK,gBACG;AAAA,MAAAU;AACH,WACEA,iBAAA5K,IAAIK,SAAwBJ,QAAQ,MAAC,SAAA2K,iBAArCA,eAAuCP,SAAQ,MAA/CO,OAAAA,SAAAA,eAAmDN,YAAW,QAC9DJ,eAAAA,OAAAA,SAAAA,YAAaI,YAAW;AAE5B;AAEAK,aAAaJ,aAAczN,SAAa0N,WAAW1N,GAAG;AAEtD,IAAM+N,cAA6BA,CACjC7K,KACAC,UACAiK,gBACG;AAAA,MAAAY;AACH,UAAAA,iBAAO9K,IAAIK,SAAoBJ,QAAQ,MAAC,OAAA,SAAjC6K,eAAmCnJ,SAASuI,WAAW;AAChE;AAEAW,YAAYN,aAAczN,SAAa0N,WAAW1N,GAAG;AAErD,IAAMiO,iBAAgCA,CACpC/K,KACAC,UACAiK,gBACG;AACH,SAAO,CAACA,YAAY3L,KAClBzB,SAAG;AAAA,QAAAkO;AAAA,WAAI,GAAAA,iBAAChL,IAAIK,SAAoBJ,QAAQ,MAAC,QAAjC+K,eAAmCrJ,SAAS7E,GAAG;EAAC,CAC1D;AACF;AAEAiO,eAAeR,aAAczN,SAAa0N,WAAW1N,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEnE,IAAMwN,kBAAiCA,CACrCjL,KACAC,UACAiK,gBACG;AACH,SAAOA,YAAY3L,KAAKzB,SAAG;AAAA,QAAAoO;AAAA,YAAAA,iBACzBlL,IAAIK,SAAoBJ,QAAQ,MAAC,OAAA,SAAjCiL,eAAmCvJ,SAAS7E,GAAG;EAAC,CAClD;AACF;AAEAmO,gBAAgBV,aAAczN,SAAa0N,WAAW1N,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEpE,IAAM0N,SAAwBA,CAACnL,KAAKC,UAAkBiK,gBAAyB;AAC7E,SAAOlK,IAAIK,SAASJ,QAAQ,MAAMiK;AACpC;AAEAiB,OAAOZ,aAAczN,SAAa0N,WAAW1N,GAAG;AAEhD,IAAMsO,aAA4BA,CAChCpL,KACAC,UACAiK,gBACG;AACH,SAAOlK,IAAIK,SAASJ,QAAQ,KAAKiK;AACnC;AAEAkB,WAAWb,aAAczN,SAAa0N,WAAW1N,GAAG;AAEpD,IAAMuO,gBAA+BA,CACnCrL,KACAC,UACAiK,gBACG;AACH,MAAI,CAAC1K,MAAKD,IAAG,IAAI2K;AAEjB,QAAMoB,WAAWtL,IAAIK,SAAiBJ,QAAQ;AAC9C,SAAOqL,YAAY9L,QAAO8L,YAAY/L;AACxC;AAEA8L,cAAcE,qBAAsBzO,SAAoB;AACtD,MAAI,CAAC0O,WAAWC,SAAS,IAAI3O;AAE7B,MAAI4O,YACF,OAAOF,cAAc,WAAWG,WAAWH,SAAmB,IAAIA;AACpE,MAAII,YACF,OAAOH,cAAc,WAAWE,WAAWF,SAAmB,IAAIA;AAEpE,MAAIjM,OACFgM,cAAc,QAAQK,OAAOC,MAAMJ,SAAS,IAAI,YAAYA;AAC9D,MAAInM,OAAMkM,cAAc,QAAQI,OAAOC,MAAMF,SAAS,IAAIG,WAAWH;AAErE,MAAIpM,OAAMD,MAAK;AACb,UAAMyM,OAAOxM;AACbA,IAAAA,OAAMD;AACNA,IAAAA,OAAMyM;EACR;AAEA,SAAO,CAACxM,MAAKD,IAAG;AAClB;AAEA8L,cAAcd,aAAczN,SAC1B0N,WAAW1N,GAAG,KAAM0N,WAAW1N,IAAI,CAAC,CAAC,KAAK0N,WAAW1N,IAAI,CAAC,CAAC;AAItD,IAAMmP,YAAY;EACvBhC;EACAQ;EACAE;EACAE;EACAE;EACAE;EACAE;EACAC;EACAC;AACF;AAMA,SAASb,WAAW1N,KAAU;AAC5B,SAAOA,QAAQ2E,UAAa3E,QAAQ,QAAQA,QAAQ;AACtD;AC6FO,IAAMoP,kBAAgC;EAC3CC,qBAAqBA,MAEiB;AACpC,WAAO;MACLC,UAAU;;;EAIdC,iBAAkBC,WAAmC;AACnD,WAAO;MACLC,eAAe,CAAA;MACf,GAAGD;;;EAIPE,mBACEzM,WACgC;AAChC,WAAO;MACL0M,uBAAuBvQ,iBAAiB,iBAAiB6D,KAAK;MAC9D2M,oBAAoB;MACpBC,uBAAuB;;;EAI3B9L,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAOmR,kBAAkB,MAAM;AAC7B,YAAMC,WAAW9M,MAAM+M,gBAAe,EAAGC,SAAS,CAAC;AAEnD,YAAMC,QAAQH,YAAAA,OAAAA,SAAAA,SAAUxM,SAAS5E,OAAO+E,EAAE;AAE1C,UAAI,OAAOwM,UAAU,UAAU;AAC7B,eAAOf,UAAUhC;MACnB;AAEA,UAAI,OAAO+C,UAAU,UAAU;AAC7B,eAAOf,UAAUZ;MACnB;AAEA,UAAI,OAAO2B,UAAU,WAAW;AAC9B,eAAOf,UAAUd;MACnB;AAEA,UAAI6B,UAAU,QAAQ,OAAOA,UAAU,UAAU;AAC/C,eAAOf,UAAUd;MACnB;AAEA,UAAIxO,MAAMC,QAAQoQ,KAAK,GAAG;AACxB,eAAOf,UAAUpB;MACnB;AAEA,aAAOoB,UAAUb;;AAEnB3P,WAAOwR,cAAc,MAAM;AAAA,UAAAC,uBAAAC;AACzB,aAAO5Q,WAAWd,OAAOqF,UAAUsL,QAAQ,IACvC3Q,OAAOqF,UAAUsL,WACjB3Q,OAAOqF,UAAUsL,aAAa,SAC5B3Q,OAAOmR,gBAAe;;SACtBM,yBAAAC,yBACApN,MAAMO,QAAQ2L,cAAS,OAAA,SAAvBkB,uBAA0B1R,OAAOqF,UAAUsL,QAAQ,MAAWc,OAAAA,wBAC9DjB,UAAUxQ,OAAOqF,UAAUsL,QAAQ;;;AAE3C3Q,WAAO2R,eAAe,MAAM;AAAA,UAAAC,uBAAAC,uBAAAC;AAC1B,eACEF,wBAAC5R,OAAOqF,UAAU0M,uBAAkB,OAAAH,wBAAI,WAAIC,wBAC3CvN,MAAMO,QAAQmN,wBAAmB,OAAAH,wBAAI,WAAKC,yBAC1CxN,MAAMO,QAAQoN,kBAAa,OAAAH,yBAAI,SAChC,CAAC,CAAC9R,OAAOC;;AAIbD,WAAOkS,gBAAgB,MAAMlS,OAAOmS,eAAc,IAAK;AAEvDnS,WAAOoS,iBAAiB,MAAA;AAAA,UAAAC;AAAA,cAAAA,wBACtB/N,MAAM8D,SAAQ,EAAG0I,kBAAa,SAAAuB,wBAA9BA,sBAAgCzJ,KAAK7H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE,MAA5DsN,OAAAA,SAAAA,sBAA+Dd;IAAK;AAEtEvR,WAAOmS,iBAAiB,MAAA;AAAA,UAAAG,wBAAAC;AAAA,cAAAD,0BAAAC,yBACtBjO,MAAM8D,SAAQ,EAAG0I,kBAAa,OAAA,SAA9ByB,uBAAgCC,UAAUzR,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE,MAAC,OAAAuN,yBAAI;IAAE;AAE1EtS,WAAOyS,iBAAiBlB,WAAS;AAC/BjN,YAAMoO,iBAAiB7R,SAAO;AAC5B,cAAM8P,WAAW3Q,OAAOwR,YAAW;AACnC,cAAMmB,iBAAiB9R,OAAAA,OAAAA,SAAAA,IAAK+H,KAAK7H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE;AAExD,cAAM6N,YAAYvS,iBAChBkR,OACAoB,iBAAiBA,eAAepB,QAAQvL,MAC1C;AAGA,YACE6M,uBAAuBlC,UAA6BiC,WAAW5S,MAAM,GACrE;AAAA,cAAA8S;AACA,kBAAAA,cAAOjS,OAAG,OAAA,SAAHA,IAAKgI,OAAO9H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE,MAAC,OAAA+N,cAAI,CAAA;QACjD;AAEA,cAAMC,eAAe;UAAEhO,IAAI/E,OAAO+E;UAAIwM,OAAOqB;;AAE7C,YAAID,gBAAgB;AAAA,cAAAK;AAClB,kBAAAA,WACEnS,OAAG,OAAA,SAAHA,IAAKiH,IAAI/G,OAAK;AACZ,gBAAIA,EAAEgE,OAAO/E,OAAO+E,IAAI;AACtB,qBAAOgO;YACT;AACA,mBAAOhS;UACT,CAAC,MAAC,OAAAiS,WAAI,CAAA;QAEV;AAEA,YAAInS,OAAG,QAAHA,IAAKmB,QAAQ;AACf,iBAAO,CAAC,GAAGnB,KAAKkS,YAAY;QAC9B;AAEA,eAAO,CAACA,YAAY;MACtB,CAAC;;;EAILzG,WAAWA,CACT/H,KACA0O,WACS;AACT1O,QAAIuM,gBAAgB,CAAA;AACpBvM,QAAI2O,oBAAoB,CAAA;;EAG1BlL,aAAqC1D,WAA8B;AACjEA,UAAMoO,mBAAoBpS,aAAyC;AACjE,YAAM2G,cAAc3C,MAAMmJ,kBAAiB;AAE3C,YAAM0F,WAAYtS,SAA4B;AAAA,YAAAuS;AAC5C,gBAAAA,oBAAO/S,iBAAiBC,SAASO,GAAG,MAAC,OAAA,SAA9BuS,kBAAgCvK,OAAOA,YAAU;AACtD,gBAAM7I,SAASiH,YAAY2B,KAAK7H,OAAKA,EAAEgE,OAAO8D,OAAO9D,EAAE;AAEvD,cAAI/E,QAAQ;AACV,kBAAM2Q,WAAW3Q,OAAOwR,YAAW;AAEnC,gBAAIqB,uBAAuBlC,UAAU9H,OAAO0I,OAAOvR,MAAM,GAAG;AAC1D,qBAAO;YACT;UACF;AAEA,iBAAO;QACT,CAAC;;AAGHsE,YAAMO,QAAQmM,yBAAd1M,QAAAA,MAAMO,QAAQmM,sBAAwBmC,QAAQ;;AAGhD7O,UAAM+O,qBAAqBC,kBAAgB;AAAA,UAAAC,uBAAAC;AACzClP,YAAMoO,iBACJY,eAAe,CAAA,KAAEC,yBAAAC,sBAAGlP,MAAMmP,iBAAY,OAAA,SAAlBD,oBAAoB1C,kBAAayC,OAAAA,wBAAI,CAAA,CAC3D;;AAGFjP,UAAM4J,yBAAyB,MAAM5J,MAAM+M,gBAAe;AAC1D/M,UAAMoP,sBAAsB,MAAM;AAChC,UAAI,CAACpP,MAAMqP,wBAAwBrP,MAAMO,QAAQ6O,qBAAqB;AACpEpP,cAAMqP,uBAAuBrP,MAAMO,QAAQ6O,oBAAoBpP,KAAK;MACtE;AAEA,UAAIA,MAAMO,QAAQ+O,mBAAmB,CAACtP,MAAMqP,sBAAsB;AAChE,eAAOrP,MAAM4J,uBAAsB;MACrC;AAEA,aAAO5J,MAAMqP,qBAAoB;;EAErC;AACF;AAEO,SAASd,uBACdlC,UACAY,OACAvR,QACA;AACA,UACG2Q,YAAYA,SAAS7B,aAClB6B,SAAS7B,WAAWyC,OAAOvR,MAAM,IACjC,UACJ,OAAOuR,UAAU,eAChB,OAAOA,UAAU,YAAY,CAACA;AAEnC;ACzaA,IAAMsC,MAA0BA,CAACrP,UAAUsP,WAAWC,cAAc;AAGlE,SAAOA,UAAUnG,OAAO,CAACiG,MAAKG,SAAS;AACrC,UAAMC,YAAYD,KAAKpP,SAASJ,QAAQ;AACxC,WAAOqP,QAAO,OAAOI,cAAc,WAAWA,YAAY;KACzD,CAAC;AACN;AAEA,IAAMlQ,MAA0BA,CAACS,UAAUsP,WAAWC,cAAc;AAClE,MAAIhQ;AAEJgQ,YAAUnS,QAAQ2C,SAAO;AACvB,UAAMgN,QAAQhN,IAAIK,SAAiBJ,QAAQ;AAE3C,QACE+M,SAAS,SACRxN,OAAOwN,SAAUxN,SAAQiC,UAAauL,SAASA,QAChD;AACAxN,MAAAA,OAAMwN;IACR;EACF,CAAC;AAED,SAAOxN;AACT;AAEA,IAAMD,MAA0BA,CAACU,UAAUsP,WAAWC,cAAc;AAClE,MAAIjQ;AAEJiQ,YAAUnS,QAAQ2C,SAAO;AACvB,UAAMgN,QAAQhN,IAAIK,SAAiBJ,QAAQ;AAC3C,QACE+M,SAAS,SACRzN,OAAOyN,SAAUzN,SAAQkC,UAAauL,SAASA,QAChD;AACAzN,MAAAA,OAAMyN;IACR;EACF,CAAC;AAED,SAAOzN;AACT;AAEA,IAAMoQ,SAA6BA,CAAC1P,UAAUsP,WAAWC,cAAc;AACrE,MAAIhQ;AACJ,MAAID;AAEJiQ,YAAUnS,QAAQ2C,SAAO;AACvB,UAAMgN,QAAQhN,IAAIK,SAAiBJ,QAAQ;AAC3C,QAAI+M,SAAS,MAAM;AACjB,UAAIxN,SAAQiC,QAAW;AACrB,YAAIuL,SAASA,MAAOxN,CAAAA,OAAMD,OAAMyN;MAClC,OAAO;AACL,YAAIxN,OAAMwN,MAAOxN,CAAAA,OAAMwN;AACvB,YAAIzN,OAAOyN,MAAOzN,CAAAA,OAAMyN;MAC1B;IACF;EACF,CAAC;AAED,SAAO,CAACxN,MAAKD,IAAG;AAClB;AAEA,IAAMqQ,OAA2BA,CAAC3P,UAAU4P,aAAa;AACvD,MAAIC,SAAQ;AACZ,MAAIR,OAAM;AAEVO,WAASxS,QAAQ2C,SAAO;AACtB,QAAIgN,QAAQhN,IAAIK,SAAiBJ,QAAQ;AACzC,QAAI+M,SAAS,SAASA,QAAQ,CAACA,UAAUA,OAAO;AAC9C,QAAE8C,QAAQR,QAAOtC;IACnB;EACF,CAAC;AAED,MAAI8C,OAAO,QAAOR,OAAMQ;AAExB;AACF;AAEA,IAAMC,SAA6BA,CAAC9P,UAAU4P,aAAa;AACzD,MAAI,CAACA,SAASpS,QAAQ;AACpB;EACF;AAEA,QAAMuS,SAASH,SAAStM,IAAIvD,SAAOA,IAAIK,SAASJ,QAAQ,CAAC;AACzD,MAAI,CAACvD,cAAcsT,MAAM,GAAG;AAC1B;EACF;AACA,MAAIA,OAAOvS,WAAW,GAAG;AACvB,WAAOuS,OAAO,CAAC;EACjB;AAEA,QAAMC,MAAMpR,KAAKqR,MAAMF,OAAOvS,SAAS,CAAC;AACxC,QAAM0S,OAAOH,OAAOI,KAAK,CAACC,GAAGC,MAAMD,IAAIC,CAAC;AACxC,SAAON,OAAOvS,SAAS,MAAM,IAAI0S,KAAKF,GAAG,KAAKE,KAAKF,MAAM,CAAC,IAAKE,KAAKF,GAAG,KAAM;AAC/E;AAEA,IAAMM,SAA6BA,CAACtQ,UAAU4P,aAAa;AACzD,SAAOlT,MAAM6T,KAAK,IAAIC,IAAIZ,SAAStM,IAAI/G,OAAKA,EAAE6D,SAASJ,QAAQ,CAAC,CAAC,EAAE+P,OAAM,CAAE;AAC7E;AAEA,IAAMU,cAAkCA,CAACzQ,UAAU4P,aAAa;AAC9D,SAAO,IAAIY,IAAIZ,SAAStM,IAAI/G,OAAKA,EAAE6D,SAASJ,QAAQ,CAAC,CAAC,EAAE0Q;AAC1D;AAEA,IAAMb,QAA4BA,CAACc,WAAWf,aAAa;AACzD,SAAOA,SAASpS;AAClB;AAEO,IAAMoT,iBAAiB;EAC5BvB;EACA9P;EACAD;EACAoQ;EACAC;EACAG;EACAQ;EACAG;EACAZ;AACF;ACyHO,IAAMgB,iBAA+B;EAC1C3E,qBAAqBA,MAGhB;AACH,WAAO;MACL4E,gBAAgBC,WAAK;AAAA,YAAAC,WAAAC;AAAA,gBAAAD,aAAAC,kBAAKF,MAAM3Q,SAAQ,MAAf6Q,QAAAA,gBAA2B7G,YAAQ,OAAA,SAAnC6G,gBAA2B7G,SAAQ,MAAI,OAAA4G,YAAI;MAAI;MACxEE,eAAe;;;EAInB9E,iBAAkBC,WAA8B;AAC9C,WAAO;MACL8E,UAAU,CAAA;MACV,GAAG9E;;;EAIPE,mBACEzM,WACoB;AACpB,WAAO;MACLsR,kBAAkBnV,iBAAiB,YAAY6D,KAAK;MACpDuR,mBAAmB;;;EAIvBzQ,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAO8V,iBAAiB,MAAM;AAC5BxR,YAAMyR,YAAYlV,SAAO;AAEvB,YAAIA,OAAAA,QAAAA,IAAKqF,SAASlG,OAAO+E,EAAE,GAAG;AAC5B,iBAAOlE,IAAIgI,OAAO9H,OAAKA,MAAMf,OAAO+E,EAAE;QACxC;AAEA,eAAO,CAAC,GAAIlE,OAAG,OAAHA,MAAO,CAAA,GAAKb,OAAO+E,EAAE;MACnC,CAAC;;AAGH/E,WAAOgW,cAAc,MAAM;AAAA,UAAApE,uBAAAC;AACzB,eACED,wBAAC5R,OAAOqF,UAAU4Q,mBAAcrE,OAAAA,wBAAI,WAAIC,wBACvCvN,MAAMO,QAAQoR,mBAAc,OAAApE,wBAAI,UAChC,CAAC,CAAC7R,OAAOC,cAAc,CAAC,CAACD,OAAOqF,UAAU6Q;;AAI/ClW,WAAOmW,eAAe,MAAM;AAAA,UAAAC;AAC1B,cAAAA,wBAAO9R,MAAM8D,SAAQ,EAAGuN,aAAQ,OAAA,SAAzBS,sBAA2BlQ,SAASlG,OAAO+E,EAAE;;AAGtD/E,WAAOqW,kBAAkB,MAAA;AAAA,UAAAC;AAAA,cAAAA,yBAAMhS,MAAM8D,SAAQ,EAAGuN,aAAQ,OAAA,SAAzBW,uBAA2BC,QAAQvW,OAAO+E,EAAE;IAAC;AAE5E/E,WAAOwW,2BAA2B,MAAM;AACtC,YAAMC,WAAWzW,OAAOgW,YAAW;AAEnC,aAAO,MAAM;AACX,YAAI,CAACS,SAAU;AACfzW,eAAO8V,eAAc;;;AAGzB9V,WAAO0W,uBAAuB,MAAM;AAClC,YAAMtF,WAAW9M,MAAM+M,gBAAe,EAAGC,SAAS,CAAC;AAEnD,YAAMC,QAAQH,YAAAA,OAAAA,SAAAA,SAAUxM,SAAS5E,OAAO+E,EAAE;AAE1C,UAAI,OAAOwM,UAAU,UAAU;AAC7B,eAAO6D,eAAevB;MACxB;AAEA,UAAI8C,OAAO9Q,UAAU+I,SAASgI,KAAKrF,KAAK,MAAM,iBAAiB;AAC7D,eAAO6D,eAAelB;MACxB;;AAEFlU,WAAO6W,mBAAmB,MAAM;AAAA,UAAAC,uBAAAC;AAC9B,UAAI,CAAC/W,QAAQ;AACX,cAAM,IAAIwG,MAAK;MACjB;AAEA,aAAO1F,WAAWd,OAAOqF,UAAUqQ,aAAa,IAC5C1V,OAAOqF,UAAUqQ,gBACjB1V,OAAOqF,UAAUqQ,kBAAkB,SACjC1V,OAAO0W,qBAAoB,KAAEI,yBAAAC,yBAC7BzS,MAAMO,QAAQuQ,mBAAc,OAAA,SAA5B2B,uBACE/W,OAAOqF,UAAUqQ,aAAa,MAC/BoB,OAAAA,wBACD1B,eACEpV,OAAOqF,UAAUqQ,aAAa;;;EAK1C1N,aAAqC1D,WAA8B;AACjEA,UAAMyR,cAAczV,aAAWgE,MAAMO,QAAQ+Q,oBAAgB,OAAA,SAA9BtR,MAAMO,QAAQ+Q,iBAAmBtV,OAAO;AAEvEgE,UAAM0S,gBAAgB1D,kBAAgB;AAAA,UAAA2D,uBAAAzD;AACpClP,YAAMyR,YAAYzC,eAAe,CAAA,KAAE2D,yBAAAzD,sBAAGlP,MAAMmP,iBAAY,OAAA,SAAlBD,oBAAoBmC,aAAQsB,OAAAA,wBAAI,CAAA,CAAE;;AAG1E3S,UAAM4S,wBAAwB,MAAM5S,MAAMoP,oBAAmB;AAC7DpP,UAAM6S,qBAAqB,MAAM;AAC/B,UAAI,CAAC7S,MAAM8S,uBAAuB9S,MAAMO,QAAQsS,oBAAoB;AAClE7S,cAAM8S,sBAAsB9S,MAAMO,QAAQsS,mBAAmB7S,KAAK;MACpE;AAEA,UAAIA,MAAMO,QAAQwS,kBAAkB,CAAC/S,MAAM8S,qBAAqB;AAC9D,eAAO9S,MAAM4S,sBAAqB;MACpC;AAEA,aAAO5S,MAAM8S,oBAAmB;;;EAIpC9K,WAAWA,CACT/H,KACAD,UACS;AACTC,QAAI4R,eAAe,MAAM,CAAC,CAAC5R,IAAI+S;AAC/B/S,QAAI2R,mBAAmB1R,cAAY;AACjC,UAAID,IAAIgT,qBAAqB1K,eAAerI,QAAQ,GAAG;AACrD,eAAOD,IAAIgT,qBAAqB/S,QAAQ;MAC1C;AAEA,YAAMxE,SAASsE,MAAMwI,UAAUtI,QAAQ;AAEvC,UAAI,EAACxE,UAAAA,QAAAA,OAAQqF,UAAU6Q,mBAAkB;AACvC,eAAO3R,IAAIK,SAASJ,QAAQ;MAC9B;AAEAD,UAAIgT,qBAAqB/S,QAAQ,IAAIxE,OAAOqF,UAAU6Q,iBACpD3R,IAAIgI,QACN;AAEA,aAAOhI,IAAIgT,qBAAqB/S,QAAQ;;AAE1CD,QAAIgT,uBAAuB,CAAA;;EAG7BlT,YAAYA,CACVM,MACA3E,QACAuE,KACAD,UACS;AAITK,SAAKwR,eAAe,MAClBnW,OAAOmW,aAAY,KAAMnW,OAAO+E,OAAOR,IAAI+S;AAC7C3S,SAAK6S,mBAAmB,MAAM,CAAC7S,KAAKwR,aAAY,KAAMnW,OAAOmW,aAAY;AACzExR,SAAK8S,kBAAkB,MAAA;AAAA,UAAAC;AAAA,aACrB,CAAC/S,KAAKwR,aAAY,KAAM,CAACxR,KAAK6S,iBAAgB,KAAM,CAAC,GAAAE,eAACnT,IAAIkI,YAAO,QAAXiL,aAAa1V;IAAM;EAC7E;AACF;AAEO,SAAS+E,aACdE,aACA0O,UACAE,mBACA;AACA,MAAI,EAACF,YAAAA,QAAAA,SAAU3T,WAAU,CAAC6T,mBAAmB;AAC3C,WAAO5O;EACT;AAEA,QAAM0Q,qBAAqB1Q,YAAY4B,OACrC+O,SAAO,CAACjC,SAASzP,SAAS0R,IAAI7S,EAAE,CAClC;AAEA,MAAI8Q,sBAAsB,UAAU;AAClC,WAAO8B;EACT;AAEA,QAAME,kBAAkBlC,SACrB7N,IAAIgQ,OAAK7Q,YAAY2B,KAAKgP,SAAOA,IAAI7S,OAAO+S,CAAC,CAAE,EAC/CjP,OAAOC,OAAO;AAEjB,SAAO,CAAC,GAAG+O,iBAAiB,GAAGF,kBAAkB;AACnD;AC3VO,IAAMI,iBAA+B;EAC1CnH,iBAAkBC,WAAiC;AACjD,WAAO;MACLmH,aAAa,CAAA;MACb,GAAGnH;;;EAIPE,mBACEzM,WAC8B;AAC9B,WAAO;MACL2T,qBAAqBxX,iBAAiB,eAAe6D,KAAK;;;EAI9Dc,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAOkY,WAAWjW,KAChBkW,cAAY,CAACC,uBAAuB9T,OAAO6T,QAAQ,CAAC,GACpD1R,aAAWA,QAAQ+L,UAAUzR,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE,GACpDf,eAAeM,MAAMO,SAAS,gBAAgB,UAAU,CAC1D;AACA7E,WAAOqY,mBAAmBF,cAAY;AAAA,UAAAG;AACpC,YAAM7R,UAAU2R,uBAAuB9T,OAAO6T,QAAQ;AACtD,eAAOG,YAAA7R,QAAQ,CAAC,MAAT6R,OAAAA,SAAAA,UAAYvT,QAAO/E,OAAO+E;;AAEnC/E,WAAOuY,kBAAkBJ,cAAY;AAAA,UAAAK;AACnC,YAAM/R,UAAU2R,uBAAuB9T,OAAO6T,QAAQ;AACtD,eAAOK,WAAA/R,QAAQA,QAAQzE,SAAS,CAAC,MAAC,OAAA,SAA3BwW,SAA6BzT,QAAO/E,OAAO+E;;;EAItDiD,aAAqC1D,WAA8B;AACjEA,UAAMmU,iBAAiBnY,aACrBgE,MAAMO,QAAQoT,uBAAmB,OAAA,SAAjC3T,MAAMO,QAAQoT,oBAAsB3X,OAAO;AAC7CgE,UAAMoU,mBAAmBpF,kBAAgB;AAAA,UAAAC;AACvCjP,YAAMmU,eACJnF,eAAe,CAAA,KAAEC,wBAAGjP,MAAMmP,aAAauE,gBAAW,OAAAzE,wBAAI,CAAA,CACxD;;AAEFjP,UAAMwC,qBAAqB7E,KACzB,MAAM,CACJqC,MAAM8D,SAAQ,EAAG4P,aACjB1T,MAAM8D,SAAQ,EAAGuN,UACjBrR,MAAMO,QAAQgR,iBAAiB,GAEjC,CAACmC,aAAarC,UAAUE,sBACrBpP,aAAsC;AAGrC,UAAIkS,iBAA2C,CAAA;AAG/C,UAAI,EAACX,eAAW,QAAXA,YAAahW,SAAQ;AACxB2W,yBAAiBlS;MACnB,OAAO;AACL,cAAMmS,kBAAkB,CAAC,GAAGZ,WAAW;AAGvC,cAAMa,cAAc,CAAC,GAAGpS,OAAO;AAK/B,eAAOoS,YAAY7W,UAAU4W,gBAAgB5W,QAAQ;AACnD,gBAAM8W,iBAAiBF,gBAAgBG,MAAK;AAC5C,gBAAMC,aAAaH,YAAYrG,UAC7BzR,OAAKA,EAAEgE,OAAO+T,cAChB;AACA,cAAIE,aAAa,IAAI;AACnBL,2BAAe7W,KAAK+W,YAAYI,OAAOD,YAAY,CAAC,EAAE,CAAC,CAAE;UAC3D;QACF;AAGAL,yBAAiB,CAAC,GAAGA,gBAAgB,GAAGE,WAAW;MACrD;AAEA,aAAO9R,aAAa4R,gBAAgBhD,UAAUE,iBAAiB;OAEnE7R,eAAeM,MAAMO,SAAS,cAAc,oBAAoB,CAClE;EACF;AACF;ACbA,IAAMqU,+BAA+BA,OAA2B;EAC9D5Q,MAAM,CAAA;EACNC,OAAO,CAAA;AACT;AAEO,IAAM4Q,gBAA8B;EACzCvI,iBAAkBC,WAAmC;AACnD,WAAO;MACLxI,eAAe6Q,6BAA4B;MAC3C,GAAGrI;;;EAIPE,mBACEzM,WACgC;AAChC,WAAO;MACL8U,uBAAuB3Y,iBAAiB,iBAAiB6D,KAAK;;;EAIlEc,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAOqZ,MAAMlB,cAAY;AACvB,YAAMmB,YAAYtZ,OACf6G,eAAc,EACdiB,IAAI/G,OAAKA,EAAEgE,EAAE,EACb8D,OAAOC,OAAO;AAEjBxE,YAAMiV,iBAAiB1Y,SAAO;AAAA,YAAA2Y,YAAAC;AAC5B,YAAItB,aAAa,SAAS;AAAA,cAAAuB,WAAAC;AACxB,iBAAO;YACLrR,QAAMoR,YAAC7Y,OAAAA,OAAAA,SAAAA,IAAKyH,SAAIoR,OAAAA,YAAI,CAAA,GAAI7Q,OAAO9H,OAAK,EAACuY,aAAS,QAATA,UAAWpT,SAASnF,CAAC,EAAE;YAC5DwH,OAAO,CACL,KAAGoR,aAAC9Y,OAAAA,OAAAA,SAAAA,IAAK0H,UAAKoR,OAAAA,aAAI,CAAA,GAAI9Q,OAAO9H,OAAK,EAACuY,aAAS,QAATA,UAAWpT,SAASnF,CAAC,EAAE,GAC1D,GAAGuY,SAAS;;QAGlB;AAEA,YAAInB,aAAa,QAAQ;AAAA,cAAAyB,YAAAC;AACvB,iBAAO;YACLvR,MAAM,CACJ,KAAGsR,aAAC/Y,OAAAA,OAAAA,SAAAA,IAAKyH,SAAIsR,OAAAA,aAAI,CAAA,GAAI/Q,OAAO9H,OAAK,EAACuY,aAAS,QAATA,UAAWpT,SAASnF,CAAC,EAAC,GACxD,GAAGuY,SAAS;YAEd/Q,SAAOsR,cAAChZ,OAAAA,OAAAA,SAAAA,IAAK0H,UAAKsR,OAAAA,cAAI,CAAA,GAAIhR,OAAO9H,OAAK,EAACuY,aAAAA,QAAAA,UAAWpT,SAASnF,CAAC,EAAC;;QAEjE;AAEA,eAAO;UACLuH,QAAMkR,aAAC3Y,OAAAA,OAAAA,SAAAA,IAAKyH,SAAIkR,OAAAA,aAAI,CAAA,GAAI3Q,OAAO9H,OAAK,EAACuY,aAAS,QAATA,UAAWpT,SAASnF,CAAC,EAAE;UAC5DwH,SAAOkR,cAAC5Y,OAAAA,OAAAA,SAAAA,IAAK0H,UAAKkR,OAAAA,cAAI,CAAA,GAAI5Q,OAAO9H,OAAK,EAACuY,aAAAA,QAAAA,UAAWpT,SAASnF,CAAC,EAAC;;MAEjE,CAAC;;AAGHf,WAAO8Z,YAAY,MAAM;AACvB,YAAM7S,cAAcjH,OAAO6G,eAAc;AAEzC,aAAOI,YAAYnE,KACjB/B,OAAC;AAAA,YAAAgZ,uBAAAvU,MAAAqM;AAAA,iBACCkI,wBAAChZ,EAAEsE,UAAU2U,kBAAa,OAAAD,wBAAI,WAAIvU,QAAAqM,wBACjCvN,MAAMO,QAAQoV,wBAAmB,OAAApI,wBAChCvN,MAAMO,QAAQmV,kBAAa,OAAAxU,OAC3B;MAAK,CACX;;AAGFxF,WAAOka,cAAc,MAAM;AACzB,YAAMC,gBAAgBna,OAAO6G,eAAc,EAAGiB,IAAI/G,OAAKA,EAAEgE,EAAE;AAE3D,YAAM;QAAEuD;QAAMC;MAAM,IAAIjE,MAAM8D,SAAQ,EAAGC;AAEzC,YAAM+R,SAASD,cAAcrX,KAAK/B,OAAKuH,QAAI,OAAA,SAAJA,KAAMpC,SAASnF,CAAC,CAAC;AACxD,YAAMsZ,UAAUF,cAAcrX,KAAK/B,OAAKwH,SAAK,OAAA,SAALA,MAAOrC,SAASnF,CAAC,CAAC;AAE1D,aAAOqZ,SAAS,SAASC,UAAU,UAAU;;AAG/Cra,WAAOsa,iBAAiB,MAAM;AAAA,UAAAjI,uBAAAC;AAC5B,YAAM6F,WAAWnY,OAAOka,YAAW;AAEnC,aAAO/B,YAAQ9F,yBAAAC,yBACXhO,MAAM8D,SAAQ,EAAGC,kBAAa,SAAAiK,yBAA9BA,uBAAiC6F,QAAQ,MAAzC7F,OAAAA,SAAAA,uBAA4CiE,QAAQvW,OAAO+E,EAAE,MAAC,OAAAsN,wBAAI,KAClE;;;EAIR/F,WAAWA,CACT/H,KACAD,UACS;AACTC,QAAIgW,wBAAwBtY,KAC1B,MAAM,CACJsC,IAAIiW,oBAAmB,GACvBlW,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACoF,UAAUrF,MAAMC,UAAU;AACzB,YAAMkS,eAAyB,CAAC,GAAInS,QAAI,OAAJA,OAAQ,CAAA,GAAK,GAAIC,SAAK,OAALA,QAAS,CAAA,CAAE;AAEhE,aAAOoF,SAAS9E,OAAO9H,OAAK,CAAC0Z,aAAavU,SAASnF,EAAEf,OAAO+E,EAAE,CAAC;OAEjEf,eAAeM,MAAMO,SAAS,aAAa,uBAAuB,CACpE;AACAN,QAAImW,sBAAsBzY,KACxB,MAAM,CAACsC,IAAIiW,oBAAmB,GAAIlW,MAAM8D,SAAQ,EAAGC,cAAcC,IAAI,GACrE,CAACqF,UAAUrF,SAAS;AAClB,YAAMqS,SAASrS,QAAI,OAAJA,OAAQ,CAAA,GACpBR,IAAItD,cAAYmJ,SAAS/E,KAAKjE,UAAQA,KAAK3E,OAAO+E,OAAOP,QAAQ,CAAE,EACnEqE,OAAOC,OAAO,EACdhB,IAAI/G,QAAM;QAAE,GAAGA;QAAGoX,UAAU;MAAO,EAA0B;AAEhE,aAAOwC;OAET3W,eAAeM,MAAMO,SAAS,aAAa,qBAAqB,CAClE;AACAN,QAAIqW,uBAAuB3Y,KACzB,MAAM,CAACsC,IAAIiW,oBAAmB,GAAIlW,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GACtE,CAACoF,UAAUpF,UAAU;AACnB,YAAMoS,SAASpS,SAAK,OAALA,QAAS,CAAA,GACrBT,IAAItD,cAAYmJ,SAAS/E,KAAKjE,UAAQA,KAAK3E,OAAO+E,OAAOP,QAAQ,CAAE,EACnEqE,OAAOC,OAAO,EACdhB,IAAI/G,QAAM;QAAE,GAAGA;QAAGoX,UAAU;MAAQ,EAA0B;AAEjE,aAAOwC;OAET3W,eAAeM,MAAMO,SAAS,aAAa,sBAAsB,CACnE;;EAGFmD,aAAqC1D,WAA8B;AACjEA,UAAMiV,mBAAmBjZ,aACvBgE,MAAMO,QAAQuU,yBAAqB,OAAA,SAAnC9U,MAAMO,QAAQuU,sBAAwB9Y,OAAO;AAE/CgE,UAAMuW,qBAAqBvH,kBAAY;AAAA,UAAAC,uBAAAC;AAAA,aACrClP,MAAMiV,iBACJjG,eACI4F,6BAA4B,KAAE3F,yBAAAC,sBAC9BlP,MAAMmP,iBAAND,OAAAA,SAAAA,oBAAoBnL,kBAAakL,OAAAA,wBAAI2F,6BAA4B,CACvE;IAAC;AAEH5U,UAAMwW,yBAAyB3C,cAAY;AAAA,UAAA4C;AACzC,YAAMC,eAAe1W,MAAM8D,SAAQ,EAAGC;AAEtC,UAAI,CAAC8P,UAAU;AAAA,YAAA8C,oBAAAC;AACb,eAAOpS,UAAQmS,qBAAAD,aAAa1S,SAAI,OAAA,SAAjB2S,mBAAmBjZ,aAAMkZ,sBAAIF,aAAazS,UAAb2S,OAAAA,SAAAA,oBAAoBlZ,OAAO;MACzE;AACA,aAAO8G,SAAOiS,wBAACC,aAAa7C,QAAQ,MAArB4C,OAAAA,SAAAA,sBAAwB/Y,MAAM;;AAG/CsC,UAAM6W,qBAAqBlZ,KACzB,MAAM,CAACqC,MAAMmJ,kBAAiB,GAAInJ,MAAM8D,SAAQ,EAAGC,cAAcC,IAAI,GACrE,CAACE,YAAYF,SAAS;AACpB,cAAQA,QAAAA,OAAAA,OAAQ,CAAA,GACbR,IAAItD,cAAYgE,WAAWI,KAAK5I,YAAUA,OAAO+E,OAAOP,QAAQ,CAAE,EAClEqE,OAAOC,OAAO;OAEnB9E,eAAeM,MAAMO,SAAS,gBAAgB,oBAAoB,CACpE;AAEAP,UAAM8W,sBAAsBnZ,KAC1B,MAAM,CAACqC,MAAMmJ,kBAAiB,GAAInJ,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GACtE,CAACC,YAAYD,UAAU;AACrB,cAAQA,SAAAA,OAAAA,QAAS,CAAA,GACdT,IAAItD,cAAYgE,WAAWI,KAAK5I,YAAUA,OAAO+E,OAAOP,QAAQ,CAAE,EAClEqE,OAAOC,OAAO;OAEnB9E,eAAeM,MAAMO,SAAS,gBAAgB,qBAAqB,CACrE;AAEAP,UAAM+W,uBAAuBpZ,KAC3B,MAAM,CACJqC,MAAMmJ,kBAAiB,GACvBnJ,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAYF,MAAMC,UAAU;AAC3B,YAAMkS,eAAyB,CAAC,GAAInS,QAAI,OAAJA,OAAQ,CAAA,GAAK,GAAIC,SAAK,OAALA,QAAS,CAAA,CAAE;AAEhE,aAAOC,WAAWK,OAAO9H,OAAK,CAAC0Z,aAAavU,SAASnF,EAAEgE,EAAE,CAAC;OAE5Df,eAAeM,MAAMO,SAAS,gBAAgB,sBAAsB,CACtE;EACF;AACF;AC/UO,SAASyW,qBAAqBC,WAAuC;AAC1E,SAAOA,cAAc,OAAOC,aAAa,cAAcA,WAAW;AACpE;ACyNO,IAAMC,sBAAsB;EACjCvG,MAAM;EACNwG,SAAS;EACTC,SAASvL,OAAOwL;AAClB;AAEA,IAAMC,kCAAkCA,OAA8B;EACpEC,aAAa;EACbC,WAAW;EACXC,aAAa;EACbC,iBAAiB;EACjBC,kBAAkB;EAClBC,mBAAmB,CAAA;AACrB;AAEO,IAAMC,eAA6B;EACxC1L,qBAAqBA,MAA6B;AAChD,WAAO+K;;EAET7K,iBAAkBC,WAAkC;AAClD,WAAO;MACLwL,cAAc,CAAA;MACdC,kBAAkBT,gCAA+B;MACjD,GAAGhL;;;EAIPE,mBACEzM,WAC+B;AAC/B,WAAO;MACLiY,kBAAkB;MAClBC,uBAAuB;MACvBC,sBAAsBhc,iBAAiB,gBAAgB6D,KAAK;MAC5DoY,0BAA0Bjc,iBAAiB,oBAAoB6D,KAAK;;;EAIxEc,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAO2c,UAAU,MAAM;AAAA,UAAAC,uBAAApX,MAAAqX;AACrB,YAAMC,aAAaxY,MAAM8D,SAAQ,EAAGiU,aAAarc,OAAO+E,EAAE;AAE1D,aAAO3B,KAAKW,IACVX,KAAKU,KAAG8Y,wBACN5c,OAAOqF,UAAUqW,YAAOkB,OAAAA,wBAAInB,oBAAoBC,UAAOlW,OACvDsX,cAAAA,OAAAA,aAAc9c,OAAOqF,UAAU6P,SAAI,OAAA1P,OAAIiW,oBAAoBvG,IAC7D,IAAC2H,wBACD7c,OAAOqF,UAAUsW,YAAOkB,OAAAA,wBAAIpB,oBAAoBE,OAClD;;AAGF3b,WAAO+c,WAAW9a,KAChBkW,cAAY,CACVA,UACAC,uBAAuB9T,OAAO6T,QAAQ,GACtC7T,MAAM8D,SAAQ,EAAGiU,YAAY,GAE/B,CAAClE,UAAU1R,YACTA,QACGuW,MAAM,GAAGhd,OAAOkY,SAASC,QAAQ,CAAC,EAClCvK,OAAO,CAACiG,MAAK7T,YAAW6T,OAAM7T,QAAO2c,QAAO,GAAI,CAAC,GACtD3Y,eAAeM,MAAMO,SAAS,gBAAgB,UAAU,CAC1D;AAEA7E,WAAOid,WAAWhb,KAChBkW,cAAY,CACVA,UACAC,uBAAuB9T,OAAO6T,QAAQ,GACtC7T,MAAM8D,SAAQ,EAAGiU,YAAY,GAE/B,CAAClE,UAAU1R,YACTA,QACGuW,MAAMhd,OAAOkY,SAASC,QAAQ,IAAI,CAAC,EACnCvK,OAAO,CAACiG,MAAK7T,YAAW6T,OAAM7T,QAAO2c,QAAO,GAAI,CAAC,GACtD3Y,eAAeM,MAAMO,SAAS,gBAAgB,UAAU,CAC1D;AAEA7E,WAAOkd,YAAY,MAAM;AACvB5Y,YAAM6Y,gBAAgBC,WAAiC;AAAA,YAAhC;UAAE,CAACpd,OAAO+E,EAAE,GAAGsY;UAAG,GAAGC;QAAK,IAACF;AAChD,eAAOE;MACT,CAAC;;AAEHtd,WAAOud,eAAe,MAAM;AAAA,UAAA3L,uBAAAC;AAC1B,eACED,wBAAC5R,OAAOqF,UAAUmY,mBAAc5L,OAAAA,wBAAI,WAAIC,wBACvCvN,MAAMO,QAAQ4Y,yBAAoB5L,OAAAA,wBAAI;;AAG3C7R,WAAO0d,gBAAgB,MAAM;AAC3B,aAAOpZ,MAAM8D,SAAQ,EAAGkU,iBAAiBJ,qBAAqBlc,OAAO+E;;;EAIzEmC,cAAcA,CACZjB,QACA3B,UACS;AACT2B,WAAO0W,UAAU,MAAM;AACrB,UAAI9I,OAAM;AAEV,YAAMnS,UAAWuE,CAAAA,YAAkC;AACjD,YAAIA,QAAOqB,WAAWtF,QAAQ;AAC5BiE,UAAAA,QAAOqB,WAAW1F,QAAQF,OAAO;QACnC,OAAO;AAAA,cAAAic;AACL9J,UAAAA,SAAG8J,wBAAI1X,QAAOjG,OAAO2c,QAAO,MAAEgB,OAAAA,wBAAI;QACpC;;AAGFjc,cAAQuE,MAAM;AAEd,aAAO4N;;AAET5N,WAAO8W,WAAW,MAAM;AACtB,UAAI9W,OAAOjD,QAAQ,GAAG;AACpB,cAAM4a,oBAAoB3X,OAAOwB,YAAYsC,QAAQ9D,OAAOjD,QAAQ,CAAC;AACrE,eAAO4a,kBAAkBb,SAAQ,IAAKa,kBAAkBjB,QAAO;MACjE;AAEA,aAAO;;AAET1W,WAAO4X,mBAAmBC,sBAAoB;AAC5C,YAAM9d,SAASsE,MAAMwI,UAAU7G,OAAOjG,OAAO+E,EAAE;AAC/C,YAAMgZ,YAAY/d,UAAM,OAAA,SAANA,OAAQud,aAAY;AAEtC,aAAQS,OAAe;AACrB,YAAI,CAAChe,UAAU,CAAC+d,WAAW;AACzB;QACF;AAEEC,UAAUC,WAAO,QAAjBD,EAAUC,QAAO;AAEnB,YAAIC,kBAAkBF,CAAC,GAAG;AAExB,cAAIA,EAAEG,WAAWH,EAAEG,QAAQnc,SAAS,GAAG;AACrC;UACF;QACF;AAEA,cAAM+Z,YAAY9V,OAAO0W,QAAO;AAEhC,cAAMR,oBAAwClW,SAC1CA,OAAOyB,eAAc,EAAGI,IAAI/G,OAAK,CAACA,EAAEf,OAAO+E,IAAIhE,EAAEf,OAAO2c,QAAO,CAAE,CAAC,IAClE,CAAC,CAAC3c,OAAO+E,IAAI/E,OAAO2c,QAAO,CAAE,CAAC;AAElC,cAAMyB,UAAUF,kBAAkBF,CAAC,IAC/B5a,KAAKC,MAAM2a,EAAEG,QAAQ,CAAC,EAAGC,OAAO,IAC/BJ,EAAiBI;AAEtB,cAAMC,kBAAqC,CAAA;AAE3C,cAAMC,eAAeA,CACnBC,WACAC,eACG;AACH,cAAI,OAAOA,eAAe,UAAU;AAClC;UACF;AAEAla,gBAAMma,oBAAoB5d,SAAO;AAAA,gBAAA6d,kBAAAC;AAC/B,kBAAMC,iBACJta,MAAMO,QAAQ2X,0BAA0B,QAAQ,KAAK;AACvD,kBAAMR,eACHwC,eAAUE,mBAAI7d,OAAG,OAAA,SAAHA,IAAKib,gBAAW4C,OAAAA,mBAAI,MAAME;AAC3C,kBAAM3C,kBAAkB7Y,KAAKU,IAC3BkY,gBAAW2C,iBAAI9d,OAAG,OAAA,SAAHA,IAAKkb,cAAS,OAAA4C,iBAAI,IACjC,SACF;AAEA9d,gBAAIsb,kBAAkBva,QAAQid,WAA4B;AAAA,kBAA3B,CAACra,UAAUsa,UAAU,IAACD;AACnDR,8BAAgB7Z,QAAQ,IACtBpB,KAAKC,MACHD,KAAKU,IAAIgb,aAAaA,aAAa7C,iBAAiB,CAAC,IAAI,GAC3D,IAAI;YACR,CAAC;AAED,mBAAO;cACL,GAAGpb;cACHmb;cACAC;;UAEJ,CAAC;AAED,cACE3X,MAAMO,QAAQ0X,qBAAqB,cACnCgC,cAAc,OACd;AACAja,kBAAM6Y,gBAAgBtc,UAAQ;cAC5B,GAAGA;cACH,GAAGwd;YACL,EAAE;UACJ;;AAGF,cAAMU,SAAUP,gBAAwBF,aAAa,QAAQE,UAAU;AAEvE,cAAMQ,QAASR,gBAAwB;AACrCF,uBAAa,OAAOE,UAAU;AAE9Bla,gBAAMma,oBAAoB5d,UAAQ;YAChC,GAAGA;YACHqb,kBAAkB;YAClBJ,aAAa;YACbC,WAAW;YACXC,aAAa;YACbC,iBAAiB;YACjBE,mBAAmB,CAAA;UACrB,EAAE;;AAGJ,cAAM8C,kBAAkB3D,qBAAqBwC,gBAAgB;AAE7D,cAAMoB,cAAc;UAClBC,aAAcnB,CAAAA,OAAkBe,OAAOf,GAAEI,OAAO;UAChDgB,WAAYpB,CAAAA,OAAkB;AAC5BiB,+BAAe,QAAfA,gBAAiBI,oBACf,aACAH,YAAYC,WACd;AACAF,+BAAe,QAAfA,gBAAiBI,oBACf,WACAH,YAAYE,SACd;AACAJ,kBAAMhB,GAAEI,OAAO;UACjB;;AAGF,cAAMkB,cAAc;UAClBH,aAAcnB,CAAAA,OAAkB;AAC9B,gBAAIA,GAAEuB,YAAY;AAChBvB,cAAAA,GAAEwB,eAAc;AAChBxB,cAAAA,GAAEyB,gBAAe;YACnB;AACAV,mBAAOf,GAAEG,QAAQ,CAAC,EAAGC,OAAO;AAC5B,mBAAO;;UAETgB,WAAYpB,CAAAA,OAAkB;AAAA,gBAAA0B;AAC5BT,+BAAe,QAAfA,gBAAiBI,oBACf,aACAC,YAAYH,WACd;AACAF,+BAAe,QAAfA,gBAAiBI,oBACf,YACAC,YAAYF,SACd;AACA,gBAAIpB,GAAEuB,YAAY;AAChBvB,cAAAA,GAAEwB,eAAc;AAChBxB,cAAAA,GAAEyB,gBAAe;YACnB;AACAT,mBAAKU,cAAC1B,GAAEG,QAAQ,CAAC,MAAC,OAAA,SAAZuB,YAActB,OAAO;UAC7B;;AAGF,cAAMuB,qBAAqBC,sBAAqB,IAC5C;UAAEC,SAAS;QAAM,IACjB;AAEJ,YAAI3B,kBAAkBF,CAAC,GAAG;AACxBiB,6BAAAA,QAAAA,gBAAiBa,iBACf,aACAR,YAAYH,aACZQ,kBACF;AACAV,6BAAAA,QAAAA,gBAAiBa,iBACf,YACAR,YAAYF,WACZO,kBACF;QACF,OAAO;AACLV,6BAAAA,QAAAA,gBAAiBa,iBACf,aACAZ,YAAYC,aACZQ,kBACF;AACAV,6BAAAA,QAAAA,gBAAiBa,iBACf,WACAZ,YAAYE,WACZO,kBACF;QACF;AAEArb,cAAMma,oBAAoB5d,UAAQ;UAChC,GAAGA;UACHib,aAAasC;UACbrC;UACAC,aAAa;UACbC,iBAAiB;UACjBE;UACAD,kBAAkBlc,OAAO+E;QAC3B,EAAE;;;;EAKRiD,aAAqC1D,WAA8B;AACjEA,UAAM6Y,kBAAkB7c,aACtBgE,MAAMO,QAAQ4X,wBAAoB,OAAA,SAAlCnY,MAAMO,QAAQ4X,qBAAuBnc,OAAO;AAC9CgE,UAAMma,sBAAsBne,aAC1BgE,MAAMO,QAAQ6X,4BAAwB,OAAA,SAAtCpY,MAAMO,QAAQ6X,yBAA2Bpc,OAAO;AAClDgE,UAAMyb,oBAAoBzM,kBAAgB;AAAA,UAAAC;AACxCjP,YAAM6Y,gBACJ7J,eAAe,CAAA,KAAEC,wBAAGjP,MAAMmP,aAAa4I,iBAAY,OAAA9I,wBAAI,CAAA,CACzD;;AAEFjP,UAAM0b,sBAAsB1M,kBAAgB;AAAA,UAAA2M;AAC1C3b,YAAMma,oBACJnL,eACIuI,gCAA+B,KAAEoE,yBACjC3b,MAAMmP,aAAa6I,qBAAgB,OAAA2D,yBACjCpE,gCAA+B,CACvC;;AAEFvX,UAAM4b,eAAe,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACnB9b,MAAM2D,gBAAe,EAAG,CAAC,MAAzBmY,OAAAA,SAAAA,uBAA4BrW,QAAQ6D,OAAO,CAACiG,MAAK5N,WAAW;AAC1D,eAAO4N,OAAM5N,OAAO0W,QAAO;MAC7B,GAAG,CAAC,MAACwD,OAAAA,wBAAI;IAAC;AACZ7b,UAAM+b,mBAAmB,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACvBjc,MAAM8E,oBAAmB,EAAG,CAAC,MAA7BmX,OAAAA,SAAAA,uBAAgCxW,QAAQ6D,OAAO,CAACiG,MAAK5N,WAAW;AAC9D,eAAO4N,OAAM5N,OAAO0W,QAAO;MAC7B,GAAG,CAAC,MAAC2D,OAAAA,wBAAI;IAAC;AACZhc,UAAMkc,qBAAqB,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACzBpc,MAAM6E,sBAAqB,EAAG,CAAC,MAA/BuX,OAAAA,SAAAA,uBAAkC3W,QAAQ6D,OAAO,CAACiG,MAAK5N,WAAW;AAChE,eAAO4N,OAAM5N,OAAO0W,QAAO;MAC7B,GAAG,CAAC,MAAC8D,OAAAA,wBAAI;IAAC;AACZnc,UAAMqc,oBAAoB,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACxBvc,MAAMiF,qBAAoB,EAAG,CAAC,MAA9BsX,OAAAA,SAAAA,uBAAiC9W,QAAQ6D,OAAO,CAACiG,MAAK5N,WAAW;AAC/D,eAAO4N,OAAM5N,OAAO0W,QAAO;MAC7B,GAAG,CAAC,MAACiE,OAAAA,wBAAI;IAAC;EACd;AACF;AAEA,IAAIE,mBAAmC;AAChC,SAASlB,wBAAwB;AACtC,MAAI,OAAOkB,qBAAqB,UAAW,QAAOA;AAElD,MAAIC,YAAY;AAChB,MAAI;AACF,UAAMlc,UAAU;MACd,IAAIgb,UAAU;AACZkB,oBAAY;AACZ,eAAO;MACT;;AAGF,UAAMvgB,QAAOA,MAAM;IAAA;AAEnBwgB,WAAOlB,iBAAiB,QAAQtf,OAAMqE,OAAO;AAC7Cmc,WAAO3B,oBAAoB,QAAQ7e,KAAI;WAChCygB,KAAK;AACZF,gBAAY;EACd;AACAD,qBAAmBC;AACnB,SAAOD;AACT;AAEA,SAAS5C,kBAAkBF,GAA6B;AACtD,SAAQA,EAAiBkD,SAAS;AACpC;AC3aO,IAAMC,mBAAiC;EAC5CvQ,iBAAkBC,WAAgC;AAChD,WAAO;MACLuQ,kBAAkB,CAAA;MAClB,GAAGvQ;;;EAIPE,mBACEzM,WAC6B;AAC7B,WAAO;MACL+c,0BAA0B5gB,iBAAiB,oBAAoB6D,KAAK;;;EAIxEc,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAOshB,mBAAmB/P,WAAS;AACjC,UAAIvR,OAAOuhB,WAAU,GAAI;AACvBjd,cAAMkd,oBAAoB3gB,UAAQ;UAChC,GAAGA;UACH,CAACb,OAAO+E,EAAE,GAAGwM,SAAK,OAALA,QAAS,CAACvR,OAAOuL,aAAY;QAC5C,EAAE;MACJ;;AAEFvL,WAAOuL,eAAe,MAAM;AAAA,UAAA/F,MAAA6M;AAC1B,YAAMoP,eAAezhB,OAAOyG;AAC5B,cAAAjB,OACGic,aAAazf,SACVyf,aAAa3e,KAAK4e,OAAKA,EAAEnW,aAAY,CAAE,KAAC8G,wBACxC/N,MAAM8D,SAAQ,EAAGgZ,qBAAgB,OAAA,SAAjC/O,sBAAoCrS,OAAO+E,EAAE,MAACS,OAAAA,OAAK;;AAI3DxF,WAAOuhB,aAAa,MAAM;AAAA,UAAA3P,uBAAAC;AACxB,eACED,wBAAC5R,OAAOqF,UAAUsc,iBAAY/P,OAAAA,wBAAI,WAAIC,wBACrCvN,MAAMO,QAAQ8c,iBAAY9P,OAAAA,wBAAI;;AAGnC7R,WAAO4hB,6BAA6B,MAAM;AACxC,aAAQ5D,OAAe;AACrBhe,eAAOshB,oBAAPthB,QAAAA,OAAOshB,iBACHtD,EAAiB6D,OAA4BC,OACjD;;;;EAKNxV,WAAWA,CACT/H,KACAD,UACS;AACTC,QAAIiW,sBAAsBvY,KACxB,MAAM,CAACsC,IAAIiJ,YAAW,GAAIlJ,MAAM8D,SAAQ,EAAGgZ,gBAAgB,GAC3DzG,WAAS;AACP,aAAOA,MAAM9R,OAAOlE,UAAQA,KAAK3E,OAAOuL,aAAY,CAAE;OAExDvH,eAAeM,MAAMO,SAAS,aAAa,qBAAqB,CAClE;AACAN,QAAIwd,kBAAkB9f,KACpB,MAAM,CACJsC,IAAImW,oBAAmB,GACvBnW,IAAIgW,sBAAqB,GACzBhW,IAAIqW,qBAAoB,CAAE,GAE5B,CAACtS,MAAMoC,QAAQnC,UAAU,CAAC,GAAGD,MAAM,GAAGoC,QAAQ,GAAGnC,KAAK,GACtDvE,eAAeM,MAAMO,SAAS,aAAa,iBAAiB,CAC9D;;EAGFmD,aAAqC1D,WAA8B;AACjE,UAAM0d,2BAA2BA,CAC/BthB,KACAuhB,eACqC;AACrC,aAAOhgB,KACL,MAAM,CACJggB,WAAU,GACVA,WAAU,EACPpZ,OAAO9H,OAAKA,EAAEwK,aAAY,CAAE,EAC5BzD,IAAI/G,OAAKA,EAAEgE,EAAE,EACb2G,KAAK,GAAG,CAAC,GAEdjF,aAAW;AACT,eAAOA,QAAQoC,OAAO9H,OAAKA,EAAEwK,gBAAY,OAAA,SAAdxK,EAAEwK,aAAY,CAAI;SAE/CvH,eAAeM,MAAMO,SAAS,gBAAgBnE,GAAG,CACnD;;AAGF4D,UAAM4d,wBAAwBF,yBAC5B,yBACA,MAAM1d,MAAM6d,kBAAiB,CAC/B;AACA7d,UAAM6D,wBAAwB6Z,yBAC5B,yBACA,MAAM1d,MAAMmJ,kBAAiB,CAC/B;AACAnJ,UAAM8d,4BAA4BJ,yBAChC,6BACA,MAAM1d,MAAM6W,mBAAkB,CAChC;AACA7W,UAAM+d,6BAA6BL,yBACjC,8BACA,MAAM1d,MAAM8W,oBAAmB,CACjC;AACA9W,UAAMge,8BAA8BN,yBAClC,+BACA,MAAM1d,MAAM+W,qBAAoB,CAClC;AAEA/W,UAAMkd,sBAAsBlhB,aAC1BgE,MAAMO,QAAQwc,4BAAwB,OAAA,SAAtC/c,MAAMO,QAAQwc,yBAA2B/gB,OAAO;AAElDgE,UAAMie,wBAAwBjP,kBAAgB;AAAA,UAAAC;AAC5CjP,YAAMkd,oBACJlO,eAAe,CAAA,KAAEC,wBAAGjP,MAAMmP,aAAa2N,qBAAgB,OAAA7N,wBAAI,CAAA,CAC7D;;AAGFjP,UAAMke,0BAA0BjR,WAAS;AAAA,UAAAkR;AACvClR,eAAKkR,SAAGlR,UAAKkR,OAAAA,SAAI,CAACne,MAAMoe,uBAAsB;AAE9Cpe,YAAMkd,oBACJld,MAAMmJ,kBAAiB,EAAGG,OACxB,CAAC+U,KAAK3iB,YAAY;QAChB,GAAG2iB;QACH,CAAC3iB,OAAO+E,EAAE,GAAG,CAACwM,QAAQ,EAACvR,OAAOuhB,cAAPvhB,QAAAA,OAAOuhB,WAAU,KAAOhQ;MACjD,IACA,CAAA,CACF,CACF;;AAGFjN,UAAMoe,yBAAyB,MAC7B,CAACpe,MAAMmJ,kBAAiB,EAAG3K,KAAK9C,YAAU,EAACA,OAAOuL,gBAAPvL,QAAAA,OAAOuL,aAAY,EAAK;AAErEjH,UAAMse,0BAA0B,MAC9Bte,MAAMmJ,kBAAiB,EAAG3K,KAAK9C,YAAUA,OAAOuL,gBAAY,OAAA,SAAnBvL,OAAOuL,aAAY,CAAI;AAElEjH,UAAMue,uCAAuC,MAAM;AACjD,aAAQ7E,OAAe;AAAA,YAAA8E;AACrBxe,cAAMke,yBAAuBM,UACzB9E,EAAiB6D,WAAnBiB,OAAAA,SAAAA,QAAgDhB,OAClD;;;EAGN;AACF;AAEO,SAAS1J,uBACd9T,OACA6T,UACA;AACA,SAAO,CAACA,WACJ7T,MAAM6D,sBAAqB,IAC3BgQ,aAAa,WACX7T,MAAMge,4BAA2B,IACjCnK,aAAa,SACX7T,MAAM8d,0BAAyB,IAC/B9d,MAAM+d,2BAA0B;AAC1C;AC/RO,IAAMU,iBAA+B;EAC1C/a,aAAqC1D,WAA8B;AACjEA,UAAM0e,4BACJ1e,MAAMO,QAAQoJ,sBACd3J,MAAMO,QAAQoJ,mBAAmB3J,OAAO,YAAY;AAEtDA,UAAM2e,2BAA2B,MAAM;AACrC,UAAI3e,MAAMO,QAAQ+O,mBAAmB,CAACtP,MAAM0e,2BAA2B;AACrE,eAAO1e,MAAM4J,uBAAsB;MACrC;AAEA,aAAO5J,MAAM0e,0BAAyB;;AAGxC1e,UAAM4e,gCACJ5e,MAAMO,QAAQuJ,0BACd9J,MAAMO,QAAQuJ,uBAAuB9J,OAAO,YAAY;AAC1DA,UAAM6e,+BAA+B,MAAM;AACzC,UAAI,CAAC7e,MAAM4e,+BAA+B;AACxC,eAAO,oBAAI7U,IAAG;MAChB;AAEA,aAAO/J,MAAM4e,8BAA6B;;AAG5C5e,UAAM8e,gCACJ9e,MAAMO,QAAQ0J,0BACdjK,MAAMO,QAAQ0J,uBAAuBjK,OAAO,YAAY;AAC1DA,UAAM+e,+BAA+B,MAAM;AACzC,UAAI,CAAC/e,MAAM8e,+BAA+B;AACxC;MACF;AAEA,aAAO9e,MAAM8e,8BAA6B;;EAE9C;AACF;AC8BO,IAAME,kBAAgC;EAC3C1S,iBAAkBC,WAAkC;AAClD,WAAO;MACL0S,cAAcvd;MACd,GAAG6K;;;EAIPE,mBACEzM,WAC+B;AAC/B,WAAO;MACLkf,sBAAsB/iB,iBAAiB,gBAAgB6D,KAAK;MAC5Dmf,gBAAgB;MAChBC,0BAA0B1jB,YAAU;AAAA,YAAA2jB;AAClC,cAAMpS,SAAKoS,wBAAGrf,MACX+M,gBAAe,EACfC,SAAS,CAAC,MAACqS,SAAAA,wBAFAA,sBAEEjW,uBAAsB,EACnC1N,OAAO+E,EAAE,MAHE4e,OAAAA,SAAAA,sBAGC/e,SAAQ;AAEvB,eAAO,OAAO2M,UAAU,YAAY,OAAOA,UAAU;MACvD;;;EAIJnM,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAO4jB,qBAAqB,MAAM;AAAA,UAAAhS,uBAAAC,uBAAAC,wBAAA+R;AAChC,eACEjS,wBAAC5R,OAAOqF,UAAUye,uBAAkBlS,OAAAA,wBAAI,WAAIC,wBAC3CvN,MAAMO,QAAQif,uBAAkB,OAAAjS,wBAAI,WAAKC,yBACzCxN,MAAMO,QAAQoN,kBAAa,OAAAH,yBAAI,WAAK+R,wBACpCvf,MAAMO,QAAQ6e,4BAAwB,OAAA,SAAtCpf,MAAMO,QAAQ6e,yBAA2B1jB,MAAM,MAAC,OAAA6jB,wBAAI,SACrD,CAAC,CAAC7jB,OAAOC;;;EAKf+H,aAAqC1D,WAA8B;AACjEA,UAAMyf,wBAAwB,MAAM;AAClC,aAAOvT,UAAUhC;;AAGnBlK,UAAM0f,oBAAoB,MAAM;AAAA,UAAAvS,uBAAAC;AAC9B,YAAM;QAAE+R;UAAmCnf,MAAMO;AAEjD,aAAO/D,WAAW2iB,cAAc,IAC5BA,iBACAA,mBAAmB,SACjBnf,MAAMyf,sBAAqB,KAAEtS,yBAAAC,yBAC7BpN,MAAMO,QAAQ2L,cAAdkB,OAAAA,SAAAA,uBAA0B+R,cAAc,MAAWhS,OAAAA,wBACnDjB,UAAUiT,cAAc;;AAGhCnf,UAAM2f,kBAAkB3jB,aAAW;AACjCgE,YAAMO,QAAQ2e,wBAAdlf,QAAAA,MAAMO,QAAQ2e,qBAAuBljB,OAAO;;AAG9CgE,UAAM4f,oBAAoB5Q,kBAAgB;AACxChP,YAAM2f,gBACJ3Q,eAAetN,SAAY1B,MAAMmP,aAAa8P,YAChD;;EAEJ;AACF;ACOO,IAAMY,eAA6B;EACxCvT,iBAAkBC,WAA8B;AAC9C,WAAO;MACLuT,UAAU,CAAA;MACV,GAAGvT;;;EAIPE,mBACEzM,WAC2B;AAC3B,WAAO;MACL+f,kBAAkB5jB,iBAAiB,YAAY6D,KAAK;MACpDggB,sBAAsB;;;EAI1Btc,aAAqC1D,WAA8B;AACjE,QAAIigB,aAAa;AACjB,QAAIC,SAAS;AAEblgB,UAAMmgB,qBAAqB,MAAM;AAAA,UAAAjf,MAAAkf;AAC/B,UAAI,CAACH,YAAY;AACfjgB,cAAMqgB,OAAO,MAAM;AACjBJ,uBAAa;QACf,CAAC;AACD;MACF;AAEA,WAAA/e,QAAAkf,wBACEpgB,MAAMO,QAAQ+f,iBAAYF,OAAAA,wBAC1BpgB,MAAMO,QAAQggB,sBAAiB,OAAArf,OAC/B,CAAClB,MAAMO,QAAQigB,iBACf;AACA,YAAIN,OAAQ;AACZA,iBAAS;AACTlgB,cAAMqgB,OAAO,MAAM;AACjBrgB,gBAAMygB,cAAa;AACnBP,mBAAS;QACX,CAAC;MACH;;AAEFlgB,UAAM0gB,cAAc1kB,aAAWgE,MAAMO,QAAQwf,oBAAgB,OAAA,SAA9B/f,MAAMO,QAAQwf,iBAAmB/jB,OAAO;AACvEgE,UAAM2gB,wBAAwBb,cAAY;AACxC,UAAIA,YAAAA,OAAAA,WAAY,CAAC9f,MAAM4gB,qBAAoB,GAAI;AAC7C5gB,cAAM0gB,YAAY,IAAI;MACxB,OAAO;AACL1gB,cAAM0gB,YAAY,CAAA,CAAE;MACtB;;AAEF1gB,UAAMygB,gBAAgBzR,kBAAgB;AAAA,UAAA6R,uBAAA3R;AACpClP,YAAM0gB,YAAY1R,eAAe,CAAA,KAAE6R,yBAAA3R,sBAAGlP,MAAMmP,iBAAND,OAAAA,SAAAA,oBAAoB4Q,aAAQ,OAAAe,wBAAI,CAAA,CAAE;;AAE1E7gB,UAAM8gB,uBAAuB,MAAM;AACjC,aAAO9gB,MACJ+gB,yBAAwB,EACxB/T,SAASxO,KAAKyB,SAAOA,IAAI+gB,aAAY,CAAE;;AAE5ChhB,UAAMihB,kCAAkC,MAAM;AAC5C,aAAQvH,OAAe;AACnBA,UAAUC,WAAO,QAAjBD,EAAUC,QAAO;AACnB3Z,cAAM2gB,sBAAqB;;;AAG/B3gB,UAAMkhB,wBAAwB,MAAM;AAClC,YAAMpB,WAAW9f,MAAM8D,SAAQ,EAAGgc;AAClC,aAAOA,aAAa,QAAQzN,OAAOpC,OAAO6P,QAAQ,EAAEthB,KAAKgG,OAAO;;AAElExE,UAAM4gB,uBAAuB,MAAM;AACjC,YAAMd,WAAW9f,MAAM8D,SAAQ,EAAGgc;AAGlC,UAAI,OAAOA,aAAa,WAAW;AACjC,eAAOA,aAAa;MACtB;AAEA,UAAI,CAACzN,OAAO8O,KAAKrB,QAAQ,EAAEpiB,QAAQ;AACjC,eAAO;MACT;AAGA,UAAIsC,MAAMohB,YAAW,EAAGpU,SAASxO,KAAKyB,SAAO,CAACA,IAAIohB,cAAa,CAAE,GAAG;AAClE,eAAO;MACT;AAGA,aAAO;;AAETrhB,UAAMshB,mBAAmB,MAAM;AAC7B,UAAIva,WAAW;AAEf,YAAMwa,SACJvhB,MAAM8D,SAAQ,EAAGgc,aAAa,OAC1BzN,OAAO8O,KAAKnhB,MAAMohB,YAAW,EAAGI,QAAQ,IACxCnP,OAAO8O,KAAKnhB,MAAM8D,SAAQ,EAAGgc,QAAQ;AAE3CyB,aAAOjkB,QAAQmD,QAAM;AACnB,cAAMghB,UAAUhhB,GAAGqB,MAAM,GAAG;AAC5BiF,mBAAWjI,KAAKU,IAAIuH,UAAU0a,QAAQ/jB,MAAM;MAC9C,CAAC;AAED,aAAOqJ;;AAET/G,UAAM0hB,yBAAyB,MAAM1hB,MAAM2hB,kBAAiB;AAC5D3hB,UAAM4hB,sBAAsB,MAAM;AAChC,UAAI,CAAC5hB,MAAM6hB,wBAAwB7hB,MAAMO,QAAQqhB,qBAAqB;AACpE5hB,cAAM6hB,uBAAuB7hB,MAAMO,QAAQqhB,oBAAoB5hB,KAAK;MACtE;AAEA,UAAIA,MAAMO,QAAQigB,mBAAmB,CAACxgB,MAAM6hB,sBAAsB;AAChE,eAAO7hB,MAAM0hB,uBAAsB;MACrC;AAEA,aAAO1hB,MAAM6hB,qBAAoB;;;EAIrC7Z,WAAWA,CACT/H,KACAD,UACS;AACTC,QAAI6hB,iBAAiBhC,cAAY;AAC/B9f,YAAM0gB,YAAYnkB,SAAO;AAAA,YAAAwlB;AACvB,cAAMC,SAASzlB,QAAQ,OAAO,OAAO,CAAC,EAACA,OAAAA,QAAAA,IAAM0D,IAAIQ,EAAE;AAEnD,YAAIwhB,cAAiC,CAAA;AAErC,YAAI1lB,QAAQ,MAAM;AAChB8V,iBAAO8O,KAAKnhB,MAAMohB,YAAW,EAAGI,QAAQ,EAAElkB,QAAQ4kB,WAAS;AACzDD,wBAAYC,KAAK,IAAI;UACvB,CAAC;QACH,OAAO;AACLD,wBAAc1lB;QAChB;AAEAujB,oBAAQiC,YAAGjC,aAAQ,OAAAiC,YAAI,CAACC;AAExB,YAAI,CAACA,UAAUlC,UAAU;AACvB,iBAAO;YACL,GAAGmC;YACH,CAAChiB,IAAIQ,EAAE,GAAG;;QAEd;AAEA,YAAIuhB,UAAU,CAAClC,UAAU;AACvB,gBAAM;YAAE,CAAC7f,IAAIQ,EAAE,GAAGsY;YAAG,GAAGC;UAAK,IAAIiJ;AACjC,iBAAOjJ;QACT;AAEA,eAAOzc;MACT,CAAC;;AAEH0D,QAAIohB,gBAAgB,MAAM;AAAA,UAAAc;AACxB,YAAMrC,WAAW9f,MAAM8D,SAAQ,EAAGgc;AAElC,aAAO,CAAC,GAAAqC,wBACNniB,MAAMO,QAAQ6hB,oBAAdpiB,OAAAA,SAAAA,MAAMO,QAAQ6hB,iBAAmBniB,GAAG,MAACkiB,OAAAA,wBACpCrC,aAAa,SAAQA,YAAQ,OAAA,SAARA,SAAW7f,IAAIQ,EAAE;;AAG3CR,QAAI+gB,eAAe,MAAM;AAAA,UAAAqB,uBAAA9U,uBAAA6F;AACvB,cAAAiP,wBACEriB,MAAMO,QAAQ+hB,mBAAdtiB,OAAAA,SAAAA,MAAMO,QAAQ+hB,gBAAkBriB,GAAG,MAACoiB,OAAAA,0BACnC9U,wBAACvN,MAAMO,QAAQgiB,oBAAehV,OAAAA,wBAAI,SAAS,CAAC,GAAA6F,eAACnT,IAAIkI,YAAJiL,QAAAA,aAAa1V;;AAG/DuC,QAAIuiB,0BAA0B,MAAM;AAClC,UAAIC,kBAAkB;AACtB,UAAIzZ,aAAa/I;AAEjB,aAAOwiB,mBAAmBzZ,WAAWZ,UAAU;AAC7CY,qBAAahJ,MAAM6I,OAAOG,WAAWZ,UAAU,IAAI;AACnDqa,0BAAkBzZ,WAAWqY,cAAa;MAC5C;AAEA,aAAOoB;;AAETxiB,QAAIyiB,2BAA2B,MAAM;AACnC,YAAMC,YAAY1iB,IAAI+gB,aAAY;AAElC,aAAO,MAAM;AACX,YAAI,CAAC2B,UAAW;AAChB1iB,YAAI6hB,eAAc;;;EAGxB;AACF;ACxKA,IAAMc,mBAAmB;AACzB,IAAMC,kBAAkB;AAExB,IAAMC,4BAA4BA,OAAwB;EACxDC,WAAWH;EACXI,UAAUH;AACZ;AAEO,IAAMI,gBAA8B;EACzC3W,iBAAkBC,WAAgC;AAChD,WAAO;MACL,GAAGA;MACH2W,YAAY;QACV,GAAGJ,0BAAyB;QAC5B,GAAGvW,SAAK,OAAA,SAALA,MAAO2W;MACZ;;;EAIJzW,mBACEzM,WAC6B;AAC7B,WAAO;MACLmjB,oBAAoBhnB,iBAAiB,cAAc6D,KAAK;;;EAI5D0D,aAAqC1D,WAA8B;AACjE,QAAIigB,aAAa;AACjB,QAAIC,SAAS;AAEblgB,UAAMojB,sBAAsB,MAAM;AAAA,UAAAliB,MAAAkf;AAChC,UAAI,CAACH,YAAY;AACfjgB,cAAMqgB,OAAO,MAAM;AACjBJ,uBAAa;QACf,CAAC;AACD;MACF;AAEA,WAAA/e,QAAAkf,wBACEpgB,MAAMO,QAAQ+f,iBAAYF,OAAAA,wBAC1BpgB,MAAMO,QAAQ8iB,uBAAkB,OAAAniB,OAChC,CAAClB,MAAMO,QAAQ+iB,kBACf;AACA,YAAIpD,OAAQ;AACZA,iBAAS;AACTlgB,cAAMqgB,OAAO,MAAM;AACjBrgB,gBAAMujB,eAAc;AACpBrD,mBAAS;QACX,CAAC;MACH;;AAEFlgB,UAAMwjB,gBAAgBxnB,aAAW;AAC/B,YAAMynB,cAAwClnB,SAAO;AACnD,YAAImnB,WAAW3nB,iBAAiBC,SAASO,GAAG;AAE5C,eAAOmnB;;AAGT,aAAO1jB,MAAMO,QAAQ4iB,sBAAkB,OAAA,SAAhCnjB,MAAMO,QAAQ4iB,mBAAqBM,WAAW;;AAEvDzjB,UAAM2jB,kBAAkB3U,kBAAgB;AAAA,UAAA4U;AACtC5jB,YAAMwjB,cACJxU,eACI8T,0BAAyB,KAAEc,wBAC3B5jB,MAAMmP,aAAa+T,eAAU,OAAAU,wBAAId,0BAAyB,CAChE;;AAEF9iB,UAAM6jB,eAAe7nB,aAAW;AAC9BgE,YAAMwjB,cAAcjnB,SAAO;AACzB,YAAIwmB,YAAYhnB,iBAAiBC,SAASO,IAAIwmB,SAAS;AAEvD,cAAMe,eACJ,OAAO9jB,MAAMO,QAAQwjB,cAAc,eACnC/jB,MAAMO,QAAQwjB,cAAc,KACxBjY,OAAOwL,mBACPtX,MAAMO,QAAQwjB,YAAY;AAEhChB,oBAAYjkB,KAAKU,IAAI,GAAGV,KAAKW,IAAIsjB,WAAWe,YAAY,CAAC;AAEzD,eAAO;UACL,GAAGvnB;UACHwmB;;MAEJ,CAAC;;AAEH/iB,UAAMujB,iBAAiBvU,kBAAgB;AAAA,UAAAgV,wBAAA9U;AACrClP,YAAM6jB,aACJ7U,eACI4T,oBAAgBoB,0BAAA9U,sBAChBlP,MAAMmP,iBAAYD,SAAAA,sBAAlBA,oBAAoBgU,eAApBhU,OAAAA,SAAAA,oBAAgC6T,cAASiB,OAAAA,yBAAIpB,gBACnD;;AAEF5iB,UAAMikB,gBAAgBjV,kBAAgB;AAAA,UAAAkV,wBAAAC;AACpCnkB,YAAMokB,YACJpV,eACI6T,mBAAeqB,0BAAAC,uBACfnkB,MAAMmP,iBAAYgV,SAAAA,uBAAlBA,qBAAoBjB,eAApBiB,OAAAA,SAAAA,qBAAgCnB,aAAQkB,OAAAA,yBAAIrB,eAClD;;AAEF7iB,UAAMokB,cAAcpoB,aAAW;AAC7BgE,YAAMwjB,cAAcjnB,SAAO;AACzB,cAAMymB,WAAWlkB,KAAKU,IAAI,GAAGzD,iBAAiBC,SAASO,IAAIymB,QAAQ,CAAC;AACpE,cAAMqB,cAAc9nB,IAAIymB,WAAWzmB,IAAIwmB;AACvC,cAAMA,YAAYjkB,KAAKqR,MAAMkU,cAAcrB,QAAQ;AAEnD,eAAO;UACL,GAAGzmB;UACHwmB;UACAC;;MAEJ,CAAC;;AAGHhjB,UAAMskB,eAAetoB,aACnBgE,MAAMwjB,cAAcjnB,SAAO;AAAA,UAAAgoB;AACzB,UAAIC,eAAezoB,iBACjBC,UAAOuoB,wBACPvkB,MAAMO,QAAQwjB,cAAS,OAAAQ,wBAAI,EAC7B;AAEA,UAAI,OAAOC,iBAAiB,UAAU;AACpCA,uBAAe1lB,KAAKU,IAAI,IAAIglB,YAAY;MAC1C;AAEA,aAAO;QACL,GAAGjoB;QACHwnB,WAAWS;;IAEf,CAAC;AAEHxkB,UAAMykB,iBAAiB9mB,KACrB,MAAM,CAACqC,MAAM0kB,aAAY,CAAE,GAC3BX,eAAa;AACX,UAAIY,cAAwB,CAAA;AAC5B,UAAIZ,aAAaA,YAAY,GAAG;AAC9BY,sBAAc,CAAC,GAAG,IAAI/nB,MAAMmnB,SAAS,CAAC,EAAEa,KAAK,IAAI,EAAEphB,IAAI,CAACuV,GAAGvP,MAAMA,CAAC;MACpE;AACA,aAAOmb;OAETjlB,eAAeM,MAAMO,SAAS,cAAc,gBAAgB,CAC9D;AAEAP,UAAM6kB,qBAAqB,MAAM7kB,MAAM8D,SAAQ,EAAGof,WAAWH,YAAY;AAEzE/iB,UAAM8kB,iBAAiB,MAAM;AAC3B,YAAM;QAAE/B;MAAU,IAAI/iB,MAAM8D,SAAQ,EAAGof;AAEvC,YAAMa,YAAY/jB,MAAM0kB,aAAY;AAEpC,UAAIX,cAAc,IAAI;AACpB,eAAO;MACT;AAEA,UAAIA,cAAc,GAAG;AACnB,eAAO;MACT;AAEA,aAAOhB,YAAYgB,YAAY;;AAGjC/jB,UAAM+kB,eAAe,MAAM;AACzB,aAAO/kB,MAAM6jB,aAAatnB,SAAOA,MAAM,CAAC;;AAG1CyD,UAAMglB,WAAW,MAAM;AACrB,aAAOhlB,MAAM6jB,aAAatnB,SAAO;AAC/B,eAAOA,MAAM;MACf,CAAC;;AAGHyD,UAAMilB,YAAY,MAAM;AACtB,aAAOjlB,MAAM6jB,aAAa,CAAC;;AAG7B7jB,UAAMklB,WAAW,MAAM;AACrB,aAAOllB,MAAM6jB,aAAa7jB,MAAM0kB,aAAY,IAAK,CAAC;;AAGpD1kB,UAAM+gB,2BAA2B,MAAM/gB,MAAM4hB,oBAAmB;AAChE5hB,UAAMmlB,wBAAwB,MAAM;AAClC,UACE,CAACnlB,MAAMolB,0BACPplB,MAAMO,QAAQ4kB,uBACd;AACAnlB,cAAMolB,yBACJplB,MAAMO,QAAQ4kB,sBAAsBnlB,KAAK;MAC7C;AAEA,UAAIA,MAAMO,QAAQ+iB,oBAAoB,CAACtjB,MAAMolB,wBAAwB;AACnE,eAAOplB,MAAM+gB,yBAAwB;MACvC;AAEA,aAAO/gB,MAAMolB,uBAAsB;;AAGrCplB,UAAM0kB,eAAe,MAAM;AAAA,UAAAW;AACzB,cAAAA,yBACErlB,MAAMO,QAAQwjB,cAAS,OAAAsB,yBACvBvmB,KAAKwmB,KAAKtlB,MAAMulB,YAAW,IAAKvlB,MAAM8D,SAAQ,EAAGof,WAAWF,QAAQ;;AAIxEhjB,UAAMulB,cAAc,MAAM;AAAA,UAAAC;AACxB,cAAAA,wBACExlB,MAAMO,QAAQklB,aAAQD,OAAAA,wBAAIxlB,MAAM+gB,yBAAwB,EAAG2E,KAAKhoB;;EAGtE;AACF;AChRA,IAAMioB,4BAA4BA,OAAwB;EACxDC,KAAK,CAAA;EACLC,QAAQ,CAAA;AACV;AAEO,IAAMC,aAA2B;EACtCxZ,iBAAkBC,WAAgC;AAChD,WAAO;MACLwZ,YAAYJ,0BAAyB;MACrC,GAAGpZ;;;EAIPE,mBACEzM,WAC6B;AAC7B,WAAO;MACLgmB,oBAAoB7pB,iBAAiB,cAAc6D,KAAK;;;EAI5DgI,WAAWA,CACT/H,KACAD,UACS;AACTC,QAAI8U,MAAM,CAAClB,UAAUoS,iBAAiBC,sBAAsB;AAC1D,YAAMC,aAAaF,kBACfhmB,IAAI0I,YAAW,EAAGnF,IAAItC,UAAA;AAAA,YAAC;UAAET;QAAG,IAACS;AAAA,eAAKT;MAAE,CAAA,IACpC,CAAA;AACJ,YAAM2lB,eAAeF,oBACjBjmB,IAAI6I,cAAa,EAAGtF,IAAIsV,WAAA;AAAA,YAAC;UAAErY;QAAG,IAACqY;AAAA,eAAKrY;MAAE,CAAA,IACtC,CAAA;AACJ,YAAM8gB,SAAS,oBAAI7Q,IAAI,CAAC,GAAG0V,cAAcnmB,IAAIQ,IAAI,GAAG0lB,UAAU,CAAC;AAE/DnmB,YAAMqmB,cAAc9pB,SAAO;AAAA,YAAA+pB,WAAAC;AACzB,YAAI1S,aAAa,UAAU;AAAA,cAAA2S,UAAAC;AACzB,iBAAO;YACLb,OAAKY,WAACjqB,OAAAA,OAAAA,SAAAA,IAAKqpB,QAAGY,OAAAA,WAAI,CAAA,GAAIjiB,OAAO9H,OAAK,EAAC8kB,UAAM,QAANA,OAAQmF,IAAIjqB,CAAC,EAAE;YAClDopB,QAAQ,CACN,KAAGY,cAAClqB,OAAG,OAAA,SAAHA,IAAKspB,WAAMY,OAAAA,cAAI,CAAA,GAAIliB,OAAO9H,OAAK,EAAC8kB,UAAM,QAANA,OAAQmF,IAAIjqB,CAAC,EAAC,GAClD,GAAGG,MAAM6T,KAAK8Q,MAAM,CAAC;;QAG3B;AAEA,YAAI1N,aAAa,OAAO;AAAA,cAAA8S,WAAAC;AACtB,iBAAO;YACLhB,KAAK,CACH,KAAGe,YAACpqB,OAAG,OAAA,SAAHA,IAAKqpB,QAAGe,OAAAA,YAAI,CAAA,GAAIpiB,OAAO9H,OAAK,EAAC8kB,UAAAA,QAAAA,OAAQmF,IAAIjqB,CAAC,EAAE,GAChD,GAAGG,MAAM6T,KAAK8Q,MAAM,CAAC;YAEvBsE,UAAQe,eAACrqB,OAAAA,OAAAA,SAAAA,IAAKspB,WAAMe,OAAAA,eAAI,CAAA,GAAIriB,OAAO9H,OAAK,EAAC8kB,UAAAA,QAAAA,OAAQmF,IAAIjqB,CAAC,EAAC;;QAE3D;AAEA,eAAO;UACLmpB,OAAKU,YAAC/pB,OAAAA,OAAAA,SAAAA,IAAKqpB,QAAGU,OAAAA,YAAI,CAAA,GAAI/hB,OAAO9H,OAAK,EAAC8kB,UAAM,QAANA,OAAQmF,IAAIjqB,CAAC,EAAE;UAClDopB,UAAQU,eAAChqB,OAAAA,OAAAA,SAAAA,IAAKspB,WAAMU,OAAAA,eAAI,CAAA,GAAIhiB,OAAO9H,OAAK,EAAC8kB,UAAAA,QAAAA,OAAQmF,IAAIjqB,CAAC,EAAC;;MAE3D,CAAC;;AAEHwD,QAAIuV,YAAY,MAAM;AAAA,UAAA+E;AACpB,YAAM;QAAEsM;QAAkBnR;UAAkB1V,MAAMO;AAClD,UAAI,OAAOsmB,qBAAqB,YAAY;AAC1C,eAAOA,iBAAiB5mB,GAAG;MAC7B;AACA,cAAAsa,QAAOsM,oBAAAA,OAAAA,mBAAoBnR,kBAAa,OAAA6E,QAAI;;AAE9Cta,QAAI2V,cAAc,MAAM;AACtB,YAAM2L,SAAS,CAACthB,IAAIQ,EAAE;AAEtB,YAAM;QAAEmlB;QAAKC;MAAO,IAAI7lB,MAAM8D,SAAQ,EAAGiiB;AAEzC,YAAMe,QAAQvF,OAAO/iB,KAAK/B,OAAKmpB,OAAG,OAAA,SAAHA,IAAKhkB,SAASnF,CAAC,CAAC;AAC/C,YAAMsqB,WAAWxF,OAAO/iB,KAAK/B,OAAKopB,UAAM,OAAA,SAANA,OAAQjkB,SAASnF,CAAC,CAAC;AAErD,aAAOqqB,QAAQ,QAAQC,WAAW,WAAW;;AAE/C9mB,QAAI+V,iBAAiB,MAAM;AAAA,UAAAgR,OAAAC;AACzB,YAAMpT,WAAW5T,IAAI2V,YAAW;AAChC,UAAI,CAAC/B,SAAU,QAAO;AAEtB,YAAMqT,uBAAmBF,QACvBnT,aAAa,QAAQ7T,MAAMmnB,WAAU,IAAKnnB,MAAMonB,cAAa,MAAE,OAAA,SADrCJ,MAEzBxjB,IAAI6jB,WAAA;AAAA,YAAC;UAAE5mB;QAAG,IAAC4mB;AAAA,eAAK5mB;OAAG;AAEtB,cAAAwmB,wBAAOC,uBAAAA,OAAAA,SAAAA,oBAAqBjV,QAAQhS,IAAIQ,EAAE,MAAC,OAAAwmB,wBAAI;;;EAInDvjB,aAAqC1D,WAA8B;AACjEA,UAAMqmB,gBAAgBrqB,aAAWgE,MAAMO,QAAQylB,sBAAkB,OAAA,SAAhChmB,MAAMO,QAAQylB,mBAAqBhqB,OAAO;AAE3EgE,UAAMsnB,kBAAkBtY,kBAAY;AAAA,UAAAuY,uBAAArY;AAAA,aAClClP,MAAMqmB,cACJrX,eACI2W,0BAAyB,KAAE4B,yBAAArY,sBAC3BlP,MAAMmP,iBAAND,OAAAA,SAAAA,oBAAoB6W,eAAUwB,OAAAA,wBAAI5B,0BAAyB,CACjE;IAAC;AAEH3lB,UAAMwnB,sBAAsB3T,cAAY;AAAA,UAAA4C;AACtC,YAAMC,eAAe1W,MAAM8D,SAAQ,EAAGiiB;AAEtC,UAAI,CAAClS,UAAU;AAAA,YAAA4T,mBAAAC;AACb,eAAOljB,UAAQijB,oBAAA/Q,aAAakP,QAAG,OAAA,SAAhB6B,kBAAkB/pB,aAAMgqB,uBAAIhR,aAAamP,WAAb6B,OAAAA,SAAAA,qBAAqBhqB,OAAO;MACzE;AACA,aAAO8G,SAAOiS,wBAACC,aAAa7C,QAAQ,MAArB4C,OAAAA,SAAAA,sBAAwB/Y,MAAM;;AAG/CsC,UAAM2nB,iBAAiB,CAACC,aAAaC,cAAchU,aAAa;AAAA,UAAAiU;AAC9D,YAAMpC,SACJoC,wBAAA9nB,MAAMO,QAAQwnB,mBAAcD,OAAAA,wBAAI;;;SAG3BD,gBAAY,OAAZA,eAAgB,CAAA,GAAIrkB,IAAI0e,WAAS;AAChC,gBAAMjiB,MAAMD,MAAM6I,OAAOqZ,OAAO,IAAI;AACpC,iBAAOjiB,IAAIuiB,wBAAuB,IAAKviB,MAAM;QAC/C,CAAC;;;SAEA4nB,gBAAY,OAAZA,eAAgB,CAAA,GAAIrkB,IACnB0e,WAAS0F,YAAYtjB,KAAKrE,SAAOA,IAAIQ,OAAOyhB,KAAK,CACnD;;AAEN,aAAOwD,KAAKnhB,OAAOC,OAAO,EAAEhB,IAAI/G,QAAM;QAAE,GAAGA;QAAGoX;MAAS,EAAE;;AAG3D7T,UAAMmnB,aAAaxpB,KACjB,MAAM,CAACqC,MAAMohB,YAAW,EAAGsE,MAAM1lB,MAAM8D,SAAQ,EAAGiiB,WAAWH,GAAG,GAChE,CAACoC,SAASC,oBACRjoB,MAAM2nB,eAAeK,SAASC,iBAAiB,KAAK,GACtDvoB,eAAeM,MAAMO,SAAS,aAAa,YAAY,CACzD;AAEAP,UAAMonB,gBAAgBzpB,KACpB,MAAM,CAACqC,MAAMohB,YAAW,EAAGsE,MAAM1lB,MAAM8D,SAAQ,EAAGiiB,WAAWF,MAAM,GACnE,CAACmC,SAASE,uBACRloB,MAAM2nB,eAAeK,SAASE,oBAAoB,QAAQ,GAC5DxoB,eAAeM,MAAMO,SAAS,aAAa,eAAe,CAC5D;AAEAP,UAAMmoB,gBAAgBxqB,KACpB,MAAM,CACJqC,MAAMohB,YAAW,EAAGsE,MACpB1lB,MAAM8D,SAAQ,EAAGiiB,WAAWH,KAC5B5lB,MAAM8D,SAAQ,EAAGiiB,WAAWF,MAAM,GAEpC,CAACmC,SAASpC,KAAKC,WAAW;AACxB,YAAMuC,eAAe,oBAAI1X,IAAI,CAAC,GAAIkV,OAAG,OAAHA,MAAO,CAAA,GAAK,GAAIC,UAAM,OAANA,SAAU,CAAA,CAAE,CAAE;AAChE,aAAOmC,QAAQzjB,OAAO9H,OAAK,CAAC2rB,aAAa1B,IAAIjqB,EAAEgE,EAAE,CAAC;OAEpDf,eAAeM,MAAMO,SAAS,aAAa,eAAe,CAC5D;EACF;AACF;AC9EO,IAAM8nB,eAA6B;EACxC/b,iBAAkBC,WAAkC;AAClD,WAAO;MACL+b,cAAc,CAAA;MACd,GAAG/b;;;EAIPE,mBACEzM,WAC+B;AAC/B,WAAO;MACLuoB,sBAAsBpsB,iBAAiB,gBAAgB6D,KAAK;MAC5DwoB,oBAAoB;MACpBC,yBAAyB;MACzBC,uBAAuB;;;;;;EAO3BhlB,aAAqC1D,WAA8B;AACjEA,UAAM2oB,kBAAkB3sB,aACtBgE,MAAMO,QAAQgoB,wBAAoB,OAAA,SAAlCvoB,MAAMO,QAAQgoB,qBAAuBvsB,OAAO;AAC9CgE,UAAM4oB,oBAAoB5Z,kBAAY;AAAA,UAAAuY;AAAA,aACpCvnB,MAAM2oB,gBACJ3Z,eAAe,CAAA,KAAEuY,wBAAGvnB,MAAMmP,aAAamZ,iBAAY,OAAAf,wBAAI,CAAA,CACzD;IAAC;AACHvnB,UAAM6oB,wBAAwB5b,WAAS;AACrCjN,YAAM2oB,gBAAgBpsB,SAAO;AAC3B0Q,gBACE,OAAOA,UAAU,cAAcA,QAAQ,CAACjN,MAAM8oB,qBAAoB;AAEpE,cAAMR,eAAe;UAAE,GAAG/rB;;AAE1B,cAAMwsB,qBAAqB/oB,MAAM4S,sBAAqB,EAAG5F;AAIzD,YAAIC,OAAO;AACT8b,6BAAmBzrB,QAAQ2C,SAAO;AAChC,gBAAI,CAACA,IAAI+oB,aAAY,GAAI;AACvB;YACF;AACAV,yBAAaroB,IAAIQ,EAAE,IAAI;UACzB,CAAC;QACH,OAAO;AACLsoB,6BAAmBzrB,QAAQ2C,SAAO;AAChC,mBAAOqoB,aAAaroB,IAAIQ,EAAE;UAC5B,CAAC;QACH;AAEA,eAAO6nB;MACT,CAAC;;AAEHtoB,UAAMipB,4BAA4Bhc,WAChCjN,MAAM2oB,gBAAgBpsB,SAAO;AAC3B,YAAM2sB,gBACJ,OAAOjc,UAAU,cACbA,QACA,CAACjN,MAAMmpB,yBAAwB;AAErC,YAAMb,eAAkC;QAAE,GAAG/rB;;AAE7CyD,YAAMohB,YAAW,EAAGsE,KAAKpoB,QAAQ2C,SAAO;AACtCmpB,4BAAoBd,cAAcroB,IAAIQ,IAAIyoB,eAAe,MAAMlpB,KAAK;MACtE,CAAC;AAED,aAAOsoB;IACT,CAAC;AA4DHtoB,UAAMqpB,yBAAyB,MAAMrpB,MAAM+M,gBAAe;AAC1D/M,UAAMspB,sBAAsB3rB,KAC1B,MAAM,CAACqC,MAAM8D,SAAQ,EAAGwkB,cAActoB,MAAM+M,gBAAe,CAAE,GAC7D,CAACub,cAAciB,aAAa;AAC1B,UAAI,CAAClX,OAAO8O,KAAKmH,YAAY,EAAE5qB,QAAQ;AACrC,eAAO;UACLgoB,MAAM,CAAA;UACN1Y,UAAU,CAAA;UACVwU,UAAU,CAAA;;MAEd;AAEA,aAAOgI,aAAaxpB,OAAOupB,QAAQ;OAErC7pB,eAAeM,MAAMO,SAAS,cAAc,qBAAqB,CACnE;AAEAP,UAAMypB,8BAA8B9rB,KAClC,MAAM,CAACqC,MAAM8D,SAAQ,EAAGwkB,cAActoB,MAAMoP,oBAAmB,CAAE,GACjE,CAACkZ,cAAciB,aAAa;AAC1B,UAAI,CAAClX,OAAO8O,KAAKmH,YAAY,EAAE5qB,QAAQ;AACrC,eAAO;UACLgoB,MAAM,CAAA;UACN1Y,UAAU,CAAA;UACVwU,UAAU,CAAA;;MAEd;AAEA,aAAOgI,aAAaxpB,OAAOupB,QAAQ;OAErC7pB,eAAeM,MAAMO,SAAS,cAAc,6BAA6B,CAC3E;AAEAP,UAAM0pB,6BAA6B/rB,KACjC,MAAM,CAACqC,MAAM8D,SAAQ,EAAGwkB,cAActoB,MAAM2hB,kBAAiB,CAAE,GAC/D,CAAC2G,cAAciB,aAAa;AAC1B,UAAI,CAAClX,OAAO8O,KAAKmH,YAAY,EAAE5qB,QAAQ;AACrC,eAAO;UACLgoB,MAAM,CAAA;UACN1Y,UAAU,CAAA;UACVwU,UAAU,CAAA;;MAEd;AAEA,aAAOgI,aAAaxpB,OAAOupB,QAAQ;OAErC7pB,eAAeM,MAAMO,SAAS,cAAc,4BAA4B,CAC1E;AAkBAP,UAAM8oB,uBAAuB,MAAM;AACjC,YAAMC,qBAAqB/oB,MAAMoP,oBAAmB,EAAGpC;AACvD,YAAM;QAAEsb;MAAa,IAAItoB,MAAM8D,SAAQ;AAEvC,UAAI6lB,oBAAoBnlB,QACtBukB,mBAAmBrrB,UAAU2U,OAAO8O,KAAKmH,YAAY,EAAE5qB,MACzD;AAEA,UAAIisB,mBAAmB;AACrB,YACEZ,mBAAmBvqB,KACjByB,SAAOA,IAAI+oB,aAAY,KAAM,CAACV,aAAaroB,IAAIQ,EAAE,CACnD,GACA;AACAkpB,8BAAoB;QACtB;MACF;AAEA,aAAOA;;AAGT3pB,UAAMmpB,2BAA2B,MAAM;AACrC,YAAMS,qBAAqB5pB,MACxBmlB,sBAAqB,EACrBnY,SAASzI,OAAOtE,SAAOA,IAAI+oB,aAAY,CAAE;AAC5C,YAAM;QAAEV;MAAa,IAAItoB,MAAM8D,SAAQ;AAEvC,UAAI+lB,wBAAwB,CAAC,CAACD,mBAAmBlsB;AAEjD,UACEmsB,yBACAD,mBAAmBprB,KAAKyB,SAAO,CAACqoB,aAAaroB,IAAIQ,EAAE,CAAC,GACpD;AACAopB,gCAAwB;MAC1B;AAEA,aAAOA;;AAGT7pB,UAAM8pB,wBAAwB,MAAM;AAAA,UAAAC;AAClC,YAAMC,gBAAgB3X,OAAO8O,MAAI4I,wBAC/B/pB,MAAM8D,SAAQ,EAAGwkB,iBAAYyB,OAAAA,wBAAI,CAAA,CACnC,EAAErsB;AACF,aACEssB,gBAAgB,KAChBA,gBAAgBhqB,MAAMoP,oBAAmB,EAAGpC,SAAStP;;AAIzDsC,UAAMiqB,4BAA4B,MAAM;AACtC,YAAML,qBAAqB5pB,MAAMmlB,sBAAqB,EAAGnY;AACzD,aAAOhN,MAAMmpB,yBAAwB,IACjC,QACAS,mBACGrlB,OAAOtE,SAAOA,IAAI+oB,aAAY,CAAE,EAChCxqB,KAAK/B,OAAKA,EAAEytB,cAAa,KAAMztB,EAAE0tB,kBAAiB,CAAE;;AAG7DnqB,UAAMoqB,kCAAkC,MAAM;AAC5C,aAAQ1Q,OAAe;AACrB1Z,cAAM6oB,sBACFnP,EAAiB6D,OAA4BC,OACjD;;;AAIJxd,UAAMqqB,sCAAsC,MAAM;AAChD,aAAQ3Q,OAAe;AACrB1Z,cAAMipB,0BACFvP,EAAiB6D,OAA4BC,OACjD;;;;EAKNxV,WAAWA,CACT/H,KACAD,UACS;AACTC,QAAIqqB,iBAAiB,CAACrd,OAAOnP,SAAS;AACpC,YAAMysB,aAAatqB,IAAIiqB,cAAa;AAEpClqB,YAAM2oB,gBAAgBpsB,SAAO;AAAA,YAAAiuB;AAC3Bvd,gBAAQ,OAAOA,UAAU,cAAcA,QAAQ,CAACsd;AAEhD,YAAItqB,IAAI+oB,aAAY,KAAMuB,eAAetd,OAAO;AAC9C,iBAAO1Q;QACT;AAEA,cAAMkuB,iBAAiB;UAAE,GAAGluB;;AAE5B6sB,4BACEqB,gBACAxqB,IAAIQ,IACJwM,QAAKud,uBACL1sB,QAAAA,OAAAA,SAAAA,KAAM4sB,mBAAc,OAAAF,uBAAI,MACxBxqB,KACF;AAEA,eAAOyqB;MACT,CAAC;;AAEHxqB,QAAIiqB,gBAAgB,MAAM;AACxB,YAAM;QAAE5B;MAAa,IAAItoB,MAAM8D,SAAQ;AACvC,aAAO6mB,cAAc1qB,KAAKqoB,YAAY;;AAGxCroB,QAAIkqB,oBAAoB,MAAM;AAC5B,YAAM;QAAE7B;MAAa,IAAItoB,MAAM8D,SAAQ;AACvC,aAAO8mB,iBAAiB3qB,KAAKqoB,YAAmB,MAAM;;AAGxDroB,QAAI4qB,0BAA0B,MAAM;AAClC,YAAM;QAAEvC;MAAa,IAAItoB,MAAM8D,SAAQ;AACvC,aAAO8mB,iBAAiB3qB,KAAKqoB,YAAmB,MAAM;;AAGxDroB,QAAI+oB,eAAe,MAAM;AAAA,UAAAzb;AACvB,UAAI,OAAOvN,MAAMO,QAAQioB,uBAAuB,YAAY;AAC1D,eAAOxoB,MAAMO,QAAQioB,mBAAmBvoB,GAAG;MAC7C;AAEA,cAAAsN,wBAAOvN,MAAMO,QAAQioB,uBAAkB,OAAAjb,wBAAI;;AAG7CtN,QAAI6qB,sBAAsB,MAAM;AAAA,UAAAtd;AAC9B,UAAI,OAAOxN,MAAMO,QAAQmoB,0BAA0B,YAAY;AAC7D,eAAO1oB,MAAMO,QAAQmoB,sBAAsBzoB,GAAG;MAChD;AAEA,cAAAuN,yBAAOxN,MAAMO,QAAQmoB,0BAAqB,OAAAlb,yBAAI;;AAGhDvN,QAAI8qB,oBAAoB,MAAM;AAAA,UAAAC;AAC5B,UAAI,OAAOhrB,MAAMO,QAAQkoB,4BAA4B,YAAY;AAC/D,eAAOzoB,MAAMO,QAAQkoB,wBAAwBxoB,GAAG;MAClD;AAEA,cAAA+qB,yBAAOhrB,MAAMO,QAAQkoB,4BAAuB,OAAAuC,yBAAI;;AAElD/qB,QAAIgrB,2BAA2B,MAAM;AACnC,YAAMC,YAAYjrB,IAAI+oB,aAAY;AAElC,aAAQtP,OAAe;AAAA,YAAA8E;AACrB,YAAI,CAAC0M,UAAW;AAChBjrB,YAAIqqB,gBAAc9L,UACd9E,EAAiB6D,WAAnBiB,OAAAA,SAAAA,QAAgDhB,OAClD;;;EAGN;AACF;AAEA,IAAM4L,sBAAsBA,CAC1BqB,gBACAhqB,IACAwM,OACAke,iBACAnrB,UACG;AAAA,MAAAoT;AACH,QAAMnT,MAAMD,MAAM6I,OAAOpI,IAAI,IAAI;AAQjC,MAAIwM,OAAO;AACT,QAAI,CAAChN,IAAI8qB,kBAAiB,GAAI;AAC5B1Y,aAAO8O,KAAKsJ,cAAc,EAAEntB,QAAQlB,SAAO,OAAOquB,eAAeruB,GAAG,CAAC;IACvE;AACA,QAAI6D,IAAI+oB,aAAY,GAAI;AACtByB,qBAAehqB,EAAE,IAAI;IACvB;EACF,OAAO;AACL,WAAOgqB,eAAehqB,EAAE;EAC1B;AAGA,MAAI0qB,oBAAe/X,eAAInT,IAAIkI,YAAO,QAAXiL,aAAa1V,UAAUuC,IAAI6qB,oBAAmB,GAAI;AACvE7qB,QAAIkI,QAAQ7K,QAAQ2C,CAAAA,SAClBmpB,oBAAoBqB,gBAAgBxqB,KAAIQ,IAAIwM,OAAOke,iBAAiBnrB,KAAK,CAC3E;EACF;AACF;AAEO,SAASwpB,aACdxpB,OACAupB,UACiB;AACjB,QAAMjB,eAAetoB,MAAM8D,SAAQ,EAAGwkB;AAEtC,QAAM8C,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AAGxD,QAAMC,cAAc,SAAC5F,MAAoB1kB,OAA4B;AACnE,WAAO0kB,KACJliB,IAAIvD,SAAO;AAAA,UAAAsrB;AACV,YAAMhB,aAAaI,cAAc1qB,KAAKqoB,YAAY;AAElD,UAAIiC,YAAY;AACda,4BAAoB5tB,KAAKyC,GAAG;AAC5BorB,4BAAoBprB,IAAIQ,EAAE,IAAIR;MAChC;AAEA,WAAAsrB,gBAAItrB,IAAIkI,YAAJojB,QAAAA,cAAa7tB,QAAQ;AACvBuC,cAAM;UACJ,GAAGA;UACHkI,SAASmjB,YAAYrrB,IAAIkI,OAAkB;;MAE/C;AAEA,UAAIoiB,YAAY;AACd,eAAOtqB;MACT;IACF,CAAC,EACAsE,OAAOC,OAAO;;AAGnB,SAAO;IACLkhB,MAAM4F,YAAY/B,SAAS7D,IAAI;IAC/B1Y,UAAUoe;IACV5J,UAAU6J;;AAEd;AAEO,SAASV,cACd1qB,KACAurB,WACS;AAAA,MAAAC;AACT,UAAAA,oBAAOD,UAAUvrB,IAAIQ,EAAE,MAAC,OAAAgrB,oBAAI;AAC9B;AAEO,SAASb,iBACd3qB,KACAurB,WACAxrB,OAC0B;AAAA,MAAA0rB;AAC1B,MAAI,GAAAA,gBAACzrB,IAAIkI,YAAJujB,QAAAA,cAAahuB,QAAQ,QAAO;AAEjC,MAAIiuB,sBAAsB;AAC1B,MAAIC,eAAe;AAEnB3rB,MAAIkI,QAAQ7K,QAAQuuB,YAAU;AAE5B,QAAID,gBAAgB,CAACD,qBAAqB;AACxC;IACF;AAEA,QAAIE,OAAO7C,aAAY,GAAI;AACzB,UAAI2B,cAAckB,QAAQL,SAAS,GAAG;AACpCI,uBAAe;MACjB,OAAO;AACLD,8BAAsB;MACxB;IACF;AAGA,QAAIE,OAAO1jB,WAAW0jB,OAAO1jB,QAAQzK,QAAQ;AAC3C,YAAMouB,yBAAyBlB,iBAAiBiB,QAAQL,SAAgB;AACxE,UAAIM,2BAA2B,OAAO;AACpCF,uBAAe;MACjB,WAAWE,2BAA2B,QAAQ;AAC5CF,uBAAe;AACfD,8BAAsB;MACxB,OAAO;AACLA,8BAAsB;MACxB;IACF;EACF,CAAC;AAED,SAAOA,sBAAsB,QAAQC,eAAe,SAAS;AAC/D;ACzpBO,IAAMG,sBAAsB;AAEnC,IAAMC,eAA+BA,CAACC,MAAMC,MAAMhsB,aAAa;AAC7D,SAAOisB,oBACL7hB,SAAS2hB,KAAK3rB,SAASJ,QAAQ,CAAC,EAAEqK,YAAW,GAC7CD,SAAS4hB,KAAK5rB,SAASJ,QAAQ,CAAC,EAAEqK,YAAW,CAC/C;AACF;AAEA,IAAM6hB,4BAA4CA,CAACH,MAAMC,MAAMhsB,aAAa;AAC1E,SAAOisB,oBACL7hB,SAAS2hB,KAAK3rB,SAASJ,QAAQ,CAAC,GAChCoK,SAAS4hB,KAAK5rB,SAASJ,QAAQ,CAAC,CAClC;AACF;AAIA,IAAMmsB,OAAuBA,CAACJ,MAAMC,MAAMhsB,aAAa;AACrD,SAAOosB,aACLhiB,SAAS2hB,KAAK3rB,SAASJ,QAAQ,CAAC,EAAEqK,YAAW,GAC7CD,SAAS4hB,KAAK5rB,SAASJ,QAAQ,CAAC,EAAEqK,YAAW,CAC/C;AACF;AAIA,IAAMgiB,oBAAoCA,CAACN,MAAMC,MAAMhsB,aAAa;AAClE,SAAOosB,aACLhiB,SAAS2hB,KAAK3rB,SAASJ,QAAQ,CAAC,GAChCoK,SAAS4hB,KAAK5rB,SAASJ,QAAQ,CAAC,CAClC;AACF;AAEA,IAAMssB,WAA2BA,CAACP,MAAMC,MAAMhsB,aAAa;AACzD,QAAMoQ,IAAI2b,KAAK3rB,SAAeJ,QAAQ;AACtC,QAAMqQ,IAAI2b,KAAK5rB,SAAeJ,QAAQ;AAKtC,SAAOoQ,IAAIC,IAAI,IAAID,IAAIC,IAAI,KAAK;AAClC;AAEA,IAAMkc,QAAwBA,CAACR,MAAMC,MAAMhsB,aAAa;AACtD,SAAOosB,aAAaL,KAAK3rB,SAASJ,QAAQ,GAAGgsB,KAAK5rB,SAASJ,QAAQ,CAAC;AACtE;AAIA,SAASosB,aAAahc,GAAQC,GAAQ;AACpC,SAAOD,MAAMC,IAAI,IAAID,IAAIC,IAAI,IAAI;AACnC;AAEA,SAASjG,SAASgG,GAAQ;AACxB,MAAI,OAAOA,MAAM,UAAU;AACzB,QAAIvE,MAAMuE,CAAC,KAAKA,MAAMtE,YAAYsE,MAAM,WAAW;AACjD,aAAO;IACT;AACA,WAAOjR,OAAOiR,CAAC;EACjB;AACA,MAAI,OAAOA,MAAM,UAAU;AACzB,WAAOA;EACT;AACA,SAAO;AACT;AAKA,SAAS6b,oBAAoBO,MAAcC,MAAc;AAGvD,QAAMrc,IAAIoc,KAAK5qB,MAAMiqB,mBAAmB,EAAExnB,OAAOC,OAAO;AACxD,QAAM+L,IAAIoc,KAAK7qB,MAAMiqB,mBAAmB,EAAExnB,OAAOC,OAAO;AAGxD,SAAO8L,EAAE5S,UAAU6S,EAAE7S,QAAQ;AAC3B,UAAMkvB,KAAKtc,EAAEmE,MAAK;AAClB,UAAMoY,KAAKtc,EAAEkE,MAAK;AAElB,UAAMqY,KAAKC,SAASH,IAAI,EAAE;AAC1B,UAAMI,KAAKD,SAASF,IAAI,EAAE;AAE1B,UAAMI,QAAQ,CAACH,IAAIE,EAAE,EAAE3c,KAAI;AAG3B,QAAItE,MAAMkhB,MAAM,CAAC,CAAE,GAAG;AACpB,UAAIL,KAAKC,IAAI;AACX,eAAO;MACT;AACA,UAAIA,KAAKD,IAAI;AACX,eAAO;MACT;AACA;IACF;AAGA,QAAI7gB,MAAMkhB,MAAM,CAAC,CAAE,GAAG;AACpB,aAAOlhB,MAAM+gB,EAAE,IAAI,KAAK;IAC1B;AAGA,QAAIA,KAAKE,IAAI;AACX,aAAO;IACT;AACA,QAAIA,KAAKF,IAAI;AACX,aAAO;IACT;EACF;AAEA,SAAOxc,EAAE5S,SAAS6S,EAAE7S;AACtB;AAIO,IAAMwvB,aAAa;EACxBlB;EACAI;EACAC;EACAE;EACAC;EACAC;AACF;ACwJO,IAAMU,aAA2B;EACtC7gB,iBAAkBC,WAA6B;AAC7C,WAAO;MACL6gB,SAAS,CAAA;MACT,GAAG7gB;;;EAIPH,qBAAqBA,MAAsD;AACzE,WAAO;MACLihB,WAAW;MACXC,eAAe;;;EAInB7gB,mBACEzM,WAC0B;AAC1B,WAAO;MACLutB,iBAAiBpxB,iBAAiB,WAAW6D,KAAK;MAClDwtB,kBAAmB9T,OAAe;AAChC,eAAQA,EAAiB+T;MAC3B;;;EAIJ3sB,cAAcA,CACZpF,QACAsE,UACS;AACTtE,WAAOgyB,mBAAmB,MAAM;AAC9B,YAAMC,YAAY3tB,MAAMoP,oBAAmB,EAAGpC,SAAS0L,MAAM,EAAE;AAE/D,UAAIkV,WAAW;AAEf,iBAAW3tB,OAAO0tB,WAAW;AAC3B,cAAM1gB,QAAQhN,OAAAA,OAAAA,SAAAA,IAAKK,SAAS5E,OAAO+E,EAAE;AAErC,YAAI4R,OAAO9Q,UAAU+I,SAASgI,KAAKrF,KAAK,MAAM,iBAAiB;AAC7D,iBAAOigB,WAAWV;QACpB;AAEA,YAAI,OAAOvf,UAAU,UAAU;AAC7B2gB,qBAAW;AAEX,cAAI3gB,MAAMnL,MAAMiqB,mBAAmB,EAAEruB,SAAS,GAAG;AAC/C,mBAAOwvB,WAAWlB;UACpB;QACF;MACF;AAEA,UAAI4B,UAAU;AACZ,eAAOV,WAAWb;MACpB;AAEA,aAAOa,WAAWT;;AAEpB/wB,WAAOmyB,iBAAiB,MAAM;AAC5B,YAAM/gB,WAAW9M,MAAMoP,oBAAmB,EAAGpC,SAAS,CAAC;AAEvD,YAAMC,QAAQH,YAAAA,OAAAA,SAAAA,SAAUxM,SAAS5E,OAAO+E,EAAE;AAE1C,UAAI,OAAOwM,UAAU,UAAU;AAC7B,eAAO;MACT;AAEA,aAAO;;AAETvR,WAAOoyB,eAAe,MAAM;AAAA,UAAAC,uBAAAC;AAC1B,UAAI,CAACtyB,QAAQ;AACX,cAAM,IAAIwG,MAAK;MACjB;AAEA,aAAO1F,WAAWd,OAAOqF,UAAUssB,SAAS,IACxC3xB,OAAOqF,UAAUssB,YACjB3xB,OAAOqF,UAAUssB,cAAc,SAC7B3xB,OAAOgyB,iBAAgB,KAAEK,yBAAAC,yBACzBhuB,MAAMO,QAAQ2sB,eAAU,OAAA,SAAxBc,uBAA2BtyB,OAAOqF,UAAUssB,SAAS,MAAWU,OAAAA,wBAChEb,WAAWxxB,OAAOqF,UAAUssB,SAAS;;AAE7C3xB,WAAOuyB,gBAAgB,CAACC,MAAMC,UAAU;AAWtC,YAAMC,mBAAmB1yB,OAAO2yB,oBAAmB;AACnD,YAAMC,iBAAiB,OAAOJ,SAAS,eAAeA,SAAS;AAE/DluB,YAAMuuB,WAAWhyB,SAAO;AAEtB,cAAMiyB,kBAAkBjyB,OAAAA,OAAAA,SAAAA,IAAK+H,KAAK7H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE;AACzD,cAAMguB,gBAAgBlyB,OAAAA,OAAAA,SAAAA,IAAK2R,UAAUzR,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE;AAE5D,YAAIiuB,aAA2B,CAAA;AAG/B,YAAIC;AACJ,YAAIC,WAAWN,iBAAiBJ,OAAOE,qBAAqB;AAG5D,YAAI7xB,OAAG,QAAHA,IAAKmB,UAAUhC,OAAOmzB,gBAAe,KAAMV,OAAO;AACpD,cAAIK,iBAAiB;AACnBG,yBAAa;UACf,OAAO;AACLA,yBAAa;UACf;QACF,OAAO;AAEL,cAAIpyB,OAAG,QAAHA,IAAKmB,UAAU+wB,kBAAkBlyB,IAAImB,SAAS,GAAG;AACnDixB,yBAAa;qBACJH,iBAAiB;AAC1BG,yBAAa;UACf,OAAO;AACLA,yBAAa;UACf;QACF;AAGA,YAAIA,eAAe,UAAU;AAE3B,cAAI,CAACL,gBAAgB;AAEnB,gBAAI,CAACF,kBAAkB;AACrBO,2BAAa;YACf;UACF;QACF;AAEA,YAAIA,eAAe,OAAO;AAAA,cAAAG;AACxBJ,uBAAa,CACX,GAAGnyB,KACH;YACEkE,IAAI/E,OAAO+E;YACXytB,MAAMU;UACR,CAAC;AAGHF,qBAAW/Z,OACT,GACA+Z,WAAWhxB,WAAMoxB,wBACd9uB,MAAMO,QAAQwuB,yBAAoBD,OAAAA,wBAAIhjB,OAAOwL,iBAClD;QACF,WAAWqX,eAAe,UAAU;AAElCD,uBAAanyB,IAAIiH,IAAI/G,OAAK;AACxB,gBAAIA,EAAEgE,OAAO/E,OAAO+E,IAAI;AACtB,qBAAO;gBACL,GAAGhE;gBACHyxB,MAAMU;;YAEV;AACA,mBAAOnyB;UACT,CAAC;QACH,WAAWkyB,eAAe,UAAU;AAClCD,uBAAanyB,IAAIgI,OAAO9H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE;QACjD,OAAO;AACLiuB,uBAAa,CACX;YACEjuB,IAAI/E,OAAO+E;YACXytB,MAAMU;UACR,CAAC;QAEL;AAEA,eAAOF;MACT,CAAC;;AAGHhzB,WAAOszB,kBAAkB,MAAM;AAAA,UAAA9tB,MAAA+tB;AAC7B,YAAMC,iBAAahuB,QAAA+tB,wBACjBvzB,OAAOqF,UAAUmuB,kBAAa,OAAAD,wBAC9BjvB,MAAMO,QAAQ2uB,kBAAa,OAAAhuB,OAC3BxF,OAAOmyB,eAAc,MAAO;AAC9B,aAAOqB,gBAAgB,SAAS;;AAGlCxzB,WAAO2yB,sBAAuBF,WAAoB;AAAA,UAAA5gB,uBAAAC;AAChD,YAAM2hB,qBAAqBzzB,OAAOszB,gBAAe;AACjD,YAAMI,WAAW1zB,OAAO2zB,YAAW;AAEnC,UAAI,CAACD,UAAU;AACb,eAAOD;MACT;AAEA,UACEC,aAAaD,wBAAkB5hB,wBAC9BvN,MAAMO,QAAQ+uB,yBAAoB,OAAA/hB,wBAAI;OACtC4gB,SAAK3gB,yBAAGxN,MAAMO,QAAQgvB,sBAAiB,OAAA/hB,yBAAI,OAAO,OACnD;AACA,eAAO;MACT;AACA,aAAO4hB,aAAa,SAAS,QAAQ;;AAGvC1zB,WAAO8zB,aAAa,MAAM;AAAA,UAAAliB,uBAAA0d;AACxB,eACE1d,wBAAC5R,OAAOqF,UAAU0uB,kBAAaniB,OAAAA,wBAAI,WAAI0d,yBACtChrB,MAAMO,QAAQkvB,kBAAa,OAAAzE,yBAAI,SAChC,CAAC,CAACtvB,OAAOC;;AAIbD,WAAOmzB,kBAAkB,MAAM;AAAA,UAAA/V,OAAA4W;AAC7B,cAAA5W,SAAA4W,yBACEh0B,OAAOqF,UAAU4uB,oBAAe,OAAAD,yBAChC1vB,MAAMO,QAAQovB,oBAAe7W,OAAAA,QAC7B,CAAC,CAACpd,OAAOC;;AAIbD,WAAO2zB,cAAc,MAAM;AAAA,UAAAO;AACzB,YAAMC,cAAUD,wBAAG5vB,MAAM8D,SAAQ,EAAGspB,YAAO,OAAA,SAAxBwC,sBAA0BtrB,KAAK7H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE;AAEzE,aAAO,CAACovB,aAAa,QAAQA,WAAW3B,OAAO,SAAS;;AAG1DxyB,WAAOo0B,eAAe,MAAA;AAAA,UAAAC,wBAAAC;AAAA,cAAAD,0BAAAC,yBACpBhwB,MAAM8D,SAAQ,EAAGspB,YAAO,OAAA,SAAxB4C,uBAA0B9hB,UAAUzR,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE,MAAC,OAAAsvB,yBAAI;IAAE;AAEpEr0B,WAAOu0B,eAAe,MAAM;AAE1BjwB,YAAMuuB,WAAWhyB,SACfA,OAAG,QAAHA,IAAKmB,SAASnB,IAAIgI,OAAO9H,OAAKA,EAAEgE,OAAO/E,OAAO+E,EAAE,IAAI,CAAA,CACtD;;AAGF/E,WAAOw0B,0BAA0B,MAAM;AACrC,YAAMC,UAAUz0B,OAAO8zB,WAAU;AAEjC,aAAQ9V,OAAe;AACrB,YAAI,CAACyW,QAAS;AACZzW,UAAUC,WAAO,QAAjBD,EAAUC,QAAO;AACnBje,eAAOuyB,iBAAPvyB,QAAAA,OAAOuyB,cACLvsB,QACAhG,OAAOmzB,gBAAe,IAAK7uB,MAAMO,QAAQitB,oBAAgB,OAAA,SAA9BxtB,MAAMO,QAAQitB,iBAAmB9T,CAAC,IAAI,KACnE;;;;EAKNhW,aAAqC1D,WAA8B;AACjEA,UAAMuuB,aAAavyB,aAAWgE,MAAMO,QAAQgtB,mBAAe,OAAA,SAA7BvtB,MAAMO,QAAQgtB,gBAAkBvxB,OAAO;AACrEgE,UAAMowB,eAAephB,kBAAgB;AAAA,UAAAqhB,uBAAAnhB;AACnClP,YAAMuuB,WAAWvf,eAAe,CAAA,KAAEqhB,yBAAAnhB,sBAAGlP,MAAMmP,iBAAY,OAAA,SAAlBD,oBAAoBke,YAAOiD,OAAAA,wBAAI,CAAA,CAAE;;AAExErwB,UAAMswB,uBAAuB,MAAMtwB,MAAM6S,mBAAkB;AAC3D7S,UAAM2hB,oBAAoB,MAAM;AAC9B,UAAI,CAAC3hB,MAAMuwB,sBAAsBvwB,MAAMO,QAAQohB,mBAAmB;AAChE3hB,cAAMuwB,qBAAqBvwB,MAAMO,QAAQohB,kBAAkB3hB,KAAK;MAClE;AAEA,UAAIA,MAAMO,QAAQiwB,iBAAiB,CAACxwB,MAAMuwB,oBAAoB;AAC5D,eAAOvwB,MAAMswB,qBAAoB;MACnC;AAEA,aAAOtwB,MAAMuwB,mBAAkB;;EAEnC;AACF;ACrfA,IAAME,kBAAkB;EACtBhtB;EACAoZ;EACApJ;EACAoB;EACApL;EACA0C;EACAsS;;EACAO;;EACAmO;EACApc;;EACA8O;EACAoD;EACA6C;EACAuC;EACAvQ;AAAY;AAmOP,SAASpU,YACdnD,SACc;AAAA,MAAAmwB,oBAAAC;AACd,MAEGpwB,QAAQT,YAAYS,QAAQqwB,YAC7B;AACAtxB,YAAQC,KAAK,4BAA4B;EAC3C;AAEA,QAAMqB,YAAY,CAAC,GAAG6vB,iBAAiB,IAAAC,qBAAInwB,QAAQK,cAAS,OAAA8vB,qBAAI,CAAA,CAAE;AAElE,MAAI1wB,QAAQ;IAAEY;;AAEd,QAAMiwB,iBAAiB7wB,MAAMY,UAAU0I,OAAO,CAAC+U,KAAKxd,YAAY;AAC9D,WAAOwR,OAAOye,OAAOzS,KAAKxd,QAAQ4L,qBAAiB,OAAA,SAAzB5L,QAAQ4L,kBAAoBzM,KAAK,CAAC;KAC3D,CAAA,CAAE;AAEL,QAAM+wB,eAAgBxwB,CAAAA,aAAyC;AAC7D,QAAIP,MAAMO,QAAQwwB,cAAc;AAC9B,aAAO/wB,MAAMO,QAAQwwB,aAAaF,gBAAgBtwB,QAAO;IAC3D;AAEA,WAAO;MACL,GAAGswB;MACH,GAAGtwB;;;AAIP,QAAMywB,mBAAmC,CAAA;AAEzC,MAAI7hB,eAAe;IACjB,GAAG6hB;IACH,IAAAL,wBAAIpwB,QAAQ4O,iBAAYwhB,OAAAA,wBAAI,CAAA;;AAG9B3wB,QAAMY,UAAUtD,QAAQuD,aAAW;AAAA,QAAAowB;AACjC9hB,oBAAY8hB,wBAAIpwB,QAAQyL,mBAARzL,OAAAA,SAAAA,QAAQyL,gBAAkB6C,YAAY,MAAC8hB,OAAAA,wBACrD9hB;EACJ,CAAC;AAED,QAAM+Q,SAAyB,CAAA;AAC/B,MAAIgR,gBAAgB;AAEpB,QAAMC,eAAoC;IACxCvwB;IACAL,SAAS;MACP,GAAGswB;MACH,GAAGtwB;;IAEL4O;IACAkR,QAAQ+Q,QAAM;AACZlR,aAAO1iB,KAAK4zB,EAAE;AAEd,UAAI,CAACF,eAAe;AAClBA,wBAAgB;AAIhBG,gBAAQC,QAAO,EACZC,KAAK,MAAM;AACV,iBAAOrR,OAAOxiB,QAAQ;AACpBwiB,mBAAOzL,MAAK,EAAE;UAChB;AACAyc,0BAAgB;SACjB,EACAM,MAAMC,WACLC,WAAW,MAAM;AACf,gBAAMD;QACR,CAAC,CACH;MACJ;;IAEFE,OAAOA,MAAM;AACX3xB,YAAM1D,SAAS0D,MAAMmP,YAAY;;IAEnCyiB,YAAY51B,aAAW;AACrB,YAAM61B,aAAa91B,iBAAiBC,SAASgE,MAAMO,OAAO;AAC1DP,YAAMO,UAAUwwB,aAAac,UAAU;;IAMzC/tB,UAAUA,MAAM;AACd,aAAO9D,MAAMO,QAAQgM;;IAGvBjQ,UAAWN,aAAiC;AAC1CgE,YAAMO,QAAQuxB,iBAAd9xB,QAAAA,MAAMO,QAAQuxB,cAAgB91B,OAAO;;IAGvC+1B,WAAWA,CAAC9xB,KAAYvB,OAAeuC,WAAmB;AAAA,UAAAohB;AAAA,cAAAA,wBACxDriB,MAAMO,QAAQyxB,YAAdhyB,OAAAA,SAAAA,MAAMO,QAAQyxB,SAAW/xB,KAAKvB,OAAOuC,MAAM,MAACohB,OAAAA,wBAC5C,GAAGphB,SAAS,CAACA,OAAOR,IAAI/B,KAAK,EAAE0I,KAAK,GAAG,IAAI1I,KAAK;IAAE;IAEpDqO,iBAAiBA,MAAM;AACrB,UAAI,CAAC/M,MAAMiyB,kBAAkB;AAC3BjyB,cAAMiyB,mBAAmBjyB,MAAMO,QAAQwM,gBAAgB/M,KAAK;MAC9D;AAEA,aAAOA,MAAMiyB,iBAAgB;;;;IAM/B7Q,aAAaA,MAAM;AACjB,aAAOphB,MAAMmlB,sBAAqB;;;IAGpCtc,QAAQA,CAACpI,IAAYyxB,cAAwB;AAC3C,UAAIjyB,OACFiyB,YAAYlyB,MAAM+gB,yBAAwB,IAAK/gB,MAAMohB,YAAW,GAChEI,SAAS/gB,EAAE;AAEb,UAAI,CAACR,KAAK;AACRA,cAAMD,MAAM+M,gBAAe,EAAGyU,SAAS/gB,EAAE;AACzC,YAAI,CAACR,KAAK;AACR,cAAIgC,MAAuC;AACzC,kBAAM,IAAIC,MAAM,sCAAsCzB,EAAE,EAAE;UAC5D;AACA,gBAAM,IAAIyB,MAAK;QACjB;MACF;AAEA,aAAOjC;;IAEToB,sBAAsB1D,KACpB,MAAM,CAACqC,MAAMO,QAAQa,aAAa,GAClCA,mBAAiB;AAAA,UAAA+wB;AACf/wB,uBAAa+wB,iBAAI/wB,kBAAa,OAAA+wB,iBAAI,CAAA;AAIlC,aAAO;QACLxwB,QAAQsP,WAAS;AACf,gBAAM3P,oBAAoB2P,MAAMtP,OAAOjG,OACpCqF;AAEH,cAAIO,kBAAkB1F,aAAa;AACjC,mBAAO0F,kBAAkB1F;UAC3B;AAEA,cAAI0F,kBAAkB3F,YAAY;AAChC,mBAAO2F,kBAAkBb;UAC3B;AAEA,iBAAO;;;QAGTJ,MAAM4Q,WAAK;AAAA,cAAAmhB,uBAAAC;AAAA,kBAAAD,yBAAAC,qBAAIphB,MAAMvQ,YAAW,MAAjB2xB,QAAAA,mBAA0B/nB,YAAQ,OAAA,SAAlC+nB,mBAA0B/nB,SAAQ,MAAI,OAAA8nB,wBAAI;QAAI;QAC7D,GAAGpyB,MAAMY,UAAU0I,OAAO,CAAC+U,KAAKxd,YAAY;AAC1C,iBAAOwR,OAAOye,OAAOzS,KAAKxd,QAAQuL,uBAAmB,OAAA,SAA3BvL,QAAQuL,oBAAmB,CAAI;WACxD,CAAA,CAAE;QACL,GAAGhL;;OAGP1B,eAAea,SAAS,gBAAgB,sBAAsB,CAChE;IAEA+xB,gBAAgBA,MAAMtyB,MAAMO,QAAQ4B;IAEpCyB,eAAejG,KACb,MAAM,CAACqC,MAAMsyB,eAAc,CAAE,GAC7BC,gBAAc;AACZ,YAAMC,iBAAiB,SACrBD,aACAtxB,QACAD,OAC6B;AAAA,YAD7BA,UAAK,QAAA;AAALA,kBAAQ;QAAC;AAET,eAAOuxB,YAAW/uB,IAAIzC,eAAa;AACjC,gBAAMrF,SAASoF,aAAad,OAAOe,WAAWC,OAAOC,MAAM;AAE3D,gBAAMwxB,oBAAoB1xB;AAK1BrF,iBAAOyG,UAAUswB,kBAAkBtwB,UAC/BqwB,eAAeC,kBAAkBtwB,SAASzG,QAAQsF,QAAQ,CAAC,IAC3D,CAAA;AAEJ,iBAAOtF;QACT,CAAC;;AAGH,aAAO82B,eAAeD,UAAU;OAElC7yB,eAAea,SAAS,gBAAgB,eAAe,CACzD;IAEAsd,mBAAmBlgB,KACjB,MAAM,CAACqC,MAAM4D,cAAa,CAAE,GAC5BM,gBAAc;AACZ,aAAOA,WAAW5B,QAAQ5G,YAAU;AAClC,eAAOA,OAAO0G,eAAc;MAC9B,CAAC;OAEH1C,eAAea,SAAS,gBAAgB,mBAAmB,CAC7D;IAEAmyB,wBAAwB/0B,KACtB,MAAM,CAACqC,MAAM6d,kBAAiB,CAAE,GAChC8U,iBAAe;AACb,aAAOA,YAAYrpB,OACjB,CAACC,KAAK7N,WAAW;AACf6N,YAAI7N,OAAO+E,EAAE,IAAI/E;AACjB,eAAO6N;SAET,CAAA,CACF;OAEF7J,eAAea,SAAS,gBAAgB,uBAAuB,CACjE;IAEA4I,mBAAmBxL,KACjB,MAAM,CAACqC,MAAM4D,cAAa,GAAI5D,MAAMwC,mBAAkB,CAAE,GACxD,CAAC0B,YAAYzB,kBAAiB;AAC5B,UAAIE,cAAcuB,WAAW5B,QAAQ5G,YAAUA,OAAO6G,eAAc,CAAE;AACtE,aAAOE,cAAaE,WAAW;OAEjCjD,eAAea,SAAS,gBAAgB,mBAAmB,CAC7D;IAEAiI,WAAWtI,cAAY;AACrB,YAAMxE,SAASsE,MAAM0yB,uBAAsB,EAAGxyB,QAAQ;AAEtD,UAA6C,CAACxE,QAAQ;AACpD4D,gBAAQmyB,MAAM,2BAA2BvxB,QAAQ,mBAAmB;MACtE;AAEA,aAAOxE;IACT;;AAGF2W,SAAOye,OAAO9wB,OAAOmxB,YAAY;AAEjC,WAASzyB,QAAQ,GAAGA,QAAQsB,MAAMY,UAAUlD,QAAQgB,SAAS;AAC3D,UAAMmC,UAAUb,MAAMY,UAAUlC,KAAK;AACrCmC,eAAO,QAAPA,QAAS6C,eAAW,QAApB7C,QAAS6C,YAAc1D,KAAK;EAC9B;AAEA,SAAOA;AACT;AC1gBO,SAAS+M,kBAEW;AACzB,SAAO/M,WACLrC,KACE,MAAM,CAACqC,MAAMO,QAAQqyB,IAAI,GAEvBA,UAKG;AACH,UAAMrJ,WAA4B;MAChC7D,MAAM,CAAA;MACN1Y,UAAU,CAAA;MACVwU,UAAU,CAAA;;AAGZ,UAAMqR,aAAa,SACjBC,cACA9xB,OACAiI,WACiB;AAAA,UAFjBjI,UAAK,QAAA;AAALA,gBAAQ;MAAC;AAGT,YAAM0kB,OAAO,CAAA;AAEb,eAASlc,IAAI,GAAGA,IAAIspB,aAAap1B,QAAQ8L,KAAK;AAS5C,cAAMvJ,MAAM+H,UACVhI,OACAA,MAAM+xB,UAAUe,aAAatpB,CAAC,GAAIA,GAAGP,SAAS,GAC9C6pB,aAAatpB,CAAC,GACdA,GACAxI,OACAU,QACAuH,aAAS,OAAA,SAATA,UAAWxI,EACb;AAGA8oB,iBAASvc,SAASxP,KAAKyC,GAAG;AAE1BspB,iBAAS/H,SAASvhB,IAAIQ,EAAE,IAAIR;AAE5BylB,aAAKloB,KAAKyC,GAAG;AAGb,YAAID,MAAMO,QAAQwyB,YAAY;AAAA,cAAAC;AAC5B/yB,cAAIgzB,kBAAkBjzB,MAAMO,QAAQwyB,WAClCD,aAAatpB,CAAC,GACdA,CACF;AAGA,eAAAwpB,uBAAI/yB,IAAIgzB,oBAAJD,QAAAA,qBAAqBt1B,QAAQ;AAC/BuC,gBAAIkI,UAAU0qB,WAAW5yB,IAAIgzB,iBAAiBjyB,QAAQ,GAAGf,GAAG;UAC9D;QACF;MACF;AAEA,aAAOylB;;AAGT6D,aAAS7D,OAAOmN,WAAWD,IAAI;AAE/B,WAAOrJ;EACT,GACA7pB,eAAeM,MAAMO,SAAS,cAAc,eAAe,MACzDP,MAAMojB,oBAAmB,CAC3B,CACF;AACJ;AC9EO,SAASxB,sBAEW;AACzB,SAAO5hB,WACLrC,KACE,MAAM,CACJqC,MAAM8D,SAAQ,EAAGgc,UACjB9f,MAAM0hB,uBAAsB,GAC5B1hB,MAAMO,QAAQyf,oBAAoB,GAEpC,CAACF,UAAUyJ,UAAUvJ,yBAAyB;AAC5C,QACE,CAACuJ,SAAS7D,KAAKhoB,UACdoiB,aAAa,QAAQ,CAACzN,OAAO8O,KAAKrB,YAAAA,OAAAA,WAAY,CAAA,CAAE,EAAEpiB,QACnD;AACA,aAAO6rB;IACT;AAEA,QAAI,CAACvJ,sBAAsB;AAEzB,aAAOuJ;IACT;AAEA,WAAO2J,WAAW3J,QAAQ;KAE5B7pB,eAAeM,MAAMO,SAAS,cAAc,qBAAqB,CACnE;AACJ;AAEO,SAAS2yB,WAAkC3J,UAA2B;AAC3E,QAAM4J,eAA6B,CAAA;AAEnC,QAAMC,YAAanzB,SAAoB;AAAA,QAAAmT;AACrC+f,iBAAa31B,KAAKyC,GAAG;AAErB,SAAImT,eAAAnT,IAAIkI,YAAJiL,QAAAA,aAAa1V,UAAUuC,IAAIohB,cAAa,GAAI;AAC9CphB,UAAIkI,QAAQ7K,QAAQ81B,SAAS;IAC/B;;AAGF7J,WAAS7D,KAAKpoB,QAAQ81B,SAAS;AAE/B,SAAO;IACL1N,MAAMyN;IACNnmB,UAAUuc,SAASvc;IACnBwU,UAAU+H,SAAS/H;;AAEvB;AC/CO,SAASvX,yBAGwB;AACtC,SAAO,CAACjK,OAAOE,aACbvC,KACE,MAAA;AAAA,QAAA01B;AAAA,WAAM,EAAAA,mBAACrzB,MAAMwI,UAAUtI,QAAQ,MAAC,OAAA,SAAzBmzB,iBAA2B1pB,mBAAkB,CAAE;EAAC,GACvD2pB,qBAAmB;AACjB,QAAI,CAACA,gBAAiB,QAAO5xB;AAE7B,UAAM6xB,eAAeD,gBAAgBtmB,SAClC1K,QAAQkxB,aAAO;AAAA,UAAAC;AAAA,cAAAA,wBAAID,QAAQ/qB,gBAAgBvI,QAAQ,MAAC,OAAAuzB,wBAAI,CAAA;IAAE,CAAA,EAC1DjwB,IAAIsI,MAAM,EACVvH,OAAO0I,WAAS,CAACnB,OAAOC,MAAMkB,KAAK,CAAC;AAEvC,QAAI,CAACsmB,aAAa71B,OAAQ;AAE1B,QAAIg2B,kBAAkBH,aAAa,CAAC;AACpC,QAAII,kBAAkBJ,aAAaA,aAAa71B,SAAS,CAAC;AAE1D,eAAWuP,SAASsmB,cAAc;AAChC,UAAItmB,QAAQymB,gBAAiBA,mBAAkBzmB;eACtCA,QAAQ0mB,gBAAiBA,mBAAkB1mB;IACtD;AAEA,WAAO,CAACymB,iBAAiBC,eAAe;KAE1Cj0B,eAAeM,MAAMO,SAAS,cAAc,wBAAwB,CACtE;AACJ;AC7BO,SAASqzB,WACdlO,MACAmO,eACA7zB,OACA;AACA,MAAIA,MAAMO,QAAQoM,oBAAoB;AACpC,WAAOmnB,wBAAwBpO,MAAMmO,eAAe7zB,KAAK;EAC3D;AAEA,SAAO+zB,uBAAuBrO,MAAMmO,eAAe7zB,KAAK;AAC1D;AAEA,SAAS8zB,wBACPE,cACAC,WACAj0B,OACiB;AAAA,MAAAk0B;AACjB,QAAMC,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AACxD,QAAMrtB,YAAQmtB,wBAAGl0B,MAAMO,QAAQqM,0BAAqB,OAAAsnB,wBAAI;AAExD,QAAMG,oBAAoB,SAACL,eAA4BhzB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAC9D,UAAM0kB,OAAqB,CAAA;AAG3B,aAASlc,IAAI,GAAGA,IAAIwqB,cAAat2B,QAAQ8L,KAAK;AAAA,UAAA4J;AAC5C,UAAInT,MAAM+zB,cAAaxqB,CAAC;AAExB,YAAM8qB,SAAStsB,UACbhI,OACAC,IAAIQ,IACJR,IAAIgI,UACJhI,IAAIvB,OACJuB,IAAIe,OACJU,QACAzB,IAAImI,QACN;AACAksB,aAAO9nB,gBAAgBvM,IAAIuM;AAE3B,WAAI4G,eAAAnT,IAAIkI,YAAO,QAAXiL,aAAa1V,UAAUsD,QAAQ+F,UAAU;AAC3CutB,eAAOnsB,UAAUksB,kBAAkBp0B,IAAIkI,SAASnH,QAAQ,CAAC;AACzDf,cAAMq0B;AAEN,YAAIL,UAAUh0B,GAAG,KAAK,CAACq0B,OAAOnsB,QAAQzK,QAAQ;AAC5CgoB,eAAKloB,KAAKyC,GAAG;AACbm0B,8BAAoBn0B,IAAIQ,EAAE,IAAIR;AAC9Bk0B,8BAAoB32B,KAAKyC,GAAG;AAC5B;QACF;AAEA,YAAIg0B,UAAUh0B,GAAG,KAAKq0B,OAAOnsB,QAAQzK,QAAQ;AAC3CgoB,eAAKloB,KAAKyC,GAAG;AACbm0B,8BAAoBn0B,IAAIQ,EAAE,IAAIR;AAC9Bk0B,8BAAoB32B,KAAKyC,GAAG;AAC5B;QACF;MACF,OAAO;AACLA,cAAMq0B;AACN,YAAIL,UAAUh0B,GAAG,GAAG;AAClBylB,eAAKloB,KAAKyC,GAAG;AACbm0B,8BAAoBn0B,IAAIQ,EAAE,IAAIR;AAC9Bk0B,8BAAoB32B,KAAKyC,GAAG;QAC9B;MACF;IACF;AAEA,WAAOylB;;AAGT,SAAO;IACLA,MAAM2O,kBAAkBL,YAAY;IACpChnB,UAAUmnB;IACV3S,UAAU4S;;AAEd;AAEA,SAASL,uBACPC,cACAC,WACAj0B,OACiB;AAAA,MAAAu0B;AACjB,QAAMJ,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AACxD,QAAMrtB,YAAQwtB,yBAAGv0B,MAAMO,QAAQqM,0BAAqB,OAAA2nB,yBAAI;AAGxD,QAAMF,oBAAoB,SAACL,eAA4BhzB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAG9D,UAAM0kB,OAAqB,CAAA;AAG3B,aAASlc,IAAI,GAAGA,IAAIwqB,cAAat2B,QAAQ8L,KAAK;AAC5C,UAAIvJ,MAAM+zB,cAAaxqB,CAAC;AAExB,YAAMgrB,OAAOP,UAAUh0B,GAAG;AAE1B,UAAIu0B,MAAM;AAAA,YAAAjJ;AACR,aAAIA,gBAAAtrB,IAAIkI,YAAO,QAAXojB,cAAa7tB,UAAUsD,QAAQ+F,UAAU;AAC3C,gBAAMutB,SAAStsB,UACbhI,OACAC,IAAIQ,IACJR,IAAIgI,UACJhI,IAAIvB,OACJuB,IAAIe,OACJU,QACAzB,IAAImI,QACN;AACAksB,iBAAOnsB,UAAUksB,kBAAkBp0B,IAAIkI,SAASnH,QAAQ,CAAC;AACzDf,gBAAMq0B;QACR;AAEA5O,aAAKloB,KAAKyC,GAAG;AACbk0B,4BAAoB32B,KAAKyC,GAAG;AAC5Bm0B,4BAAoBn0B,IAAIQ,EAAE,IAAIR;MAChC;IACF;AAEA,WAAOylB;;AAGT,SAAO;IACLA,MAAM2O,kBAAkBL,YAAY;IACpChnB,UAAUmnB;IACV3S,UAAU4S;;AAEd;AC7HO,SAASzqB,qBAGW;AACzB,SAAO,CAAC3J,OAAOE,aACbvC,KACE,MAAM,CACJqC,MAAM4J,uBAAsB,GAC5B5J,MAAM8D,SAAQ,EAAG0I,eACjBxM,MAAM8D,SAAQ,EAAGmb,cACjBjf,MAAMoP,oBAAmB,CAAE,GAE7B,CAACqlB,aAAajoB,eAAeyS,iBAAiB;AAC5C,QACE,CAACwV,YAAY/O,KAAKhoB,UACjB,EAAC8O,iBAAa,QAAbA,cAAe9O,WAAU,CAACuhB,cAC5B;AACA,aAAOwV;IACT;AAEA,UAAMC,gBAAgB,CACpB,GAAGloB,cAAchJ,IAAI/G,OAAKA,EAAEgE,EAAE,EAAE8D,OAAO9H,OAAKA,MAAMyD,QAAQ,GAC1D+e,eAAe,eAAevd,MAAS,EACvC6C,OAAOC,OAAO;AAEhB,UAAMmwB,iBAAkB10B,SAAoB;AAE1C,eAASuJ,IAAI,GAAGA,IAAIkrB,cAAch3B,QAAQ8L,KAAK;AAC7C,YAAIvJ,IAAIuM,cAAckoB,cAAclrB,CAAC,CAAC,MAAO,OAAO;AAClD,iBAAO;QACT;MACF;AACA,aAAO;;AAGT,WAAOoqB,WAAWa,YAAY/O,MAAMiP,gBAAgB30B,KAAK;KAE3DN,eAAeM,MAAMO,SAAS,cAAc,oBAAoB,CAClE;AACJ;ACxCO,SAASuJ,yBAGY;AAC1B,SAAO,CAAC9J,OAAOE,aACbvC,KACE,MAAA;AAAA,QAAA01B;AAAA,WAAM,EAAAA,mBAACrzB,MAAMwI,UAAUtI,QAAQ,MAAC,OAAA,SAAzBmzB,iBAA2B1pB,mBAAkB,CAAE;EAAC,GACvD2pB,qBAAmB;AACjB,QAAI,CAACA,gBAAiB,QAAO,oBAAIvpB,IAAG;AAEpC,QAAI6qB,sBAAsB,oBAAI7qB,IAAG;AAEjC,aAASP,IAAI,GAAGA,IAAI8pB,gBAAgBtmB,SAAStP,QAAQ8L,KAAK;AACxD,YAAMyG,SACJqjB,gBAAgBtmB,SAASxD,CAAC,EAAGf,gBAAwBvI,QAAQ;AAE/D,eAAS20B,IAAI,GAAGA,IAAI5kB,OAAOvS,QAAQm3B,KAAK;AACtC,cAAM5nB,QAAQgD,OAAO4kB,CAAC;AAEtB,YAAID,oBAAoBlO,IAAIzZ,KAAK,GAAG;AAAA,cAAA6nB;AAClCF,8BAAoBG,IAClB9nB,SACA6nB,wBAACF,oBAAoBI,IAAI/nB,KAAK,MAAC6nB,OAAAA,wBAAI,KAAK,CAC1C;QACF,OAAO;AACLF,8BAAoBG,IAAI9nB,OAAO,CAAC;QAClC;MACF;IACF;AAEA,WAAO2nB;EACT,GACAl1B,eACEM,MAAMO,SACN,cACA,0BAA0BL,QAAQ,EACpC,CACF;AACJ;ACpCO,SAASkP,sBAEW;AACzB,SAAOpP,WACLrC,KACE,MAAM,CACJqC,MAAM4J,uBAAsB,GAC5B5J,MAAM8D,SAAQ,EAAG0I,eACjBxM,MAAM8D,SAAQ,EAAGmb,YAAY,GAE/B,CAACsK,UAAU/c,eAAeyS,iBAAiB;AACzC,QACE,CAACsK,SAAS7D,KAAKhoB,UACd,EAAC8O,iBAAa,QAAbA,cAAe9O,WAAU,CAACuhB,cAC5B;AACA,eAASzV,IAAI,GAAGA,IAAI+f,SAASvc,SAAStP,QAAQ8L,KAAK;AACjD+f,iBAASvc,SAASxD,CAAC,EAAGgD,gBAAgB,CAAA;AACtC+c,iBAASvc,SAASxD,CAAC,EAAGoF,oBAAoB,CAAA;MAC5C;AACA,aAAO2a;IACT;AAEA,UAAM0L,wBAAuD,CAAA;AAC7D,UAAMC,wBAAuD,CAAA;AAE5D,KAAC1oB,iBAAa,OAAbA,gBAAiB,CAAA,GAAIlP,QAAQb,OAAK;AAAA,UAAA04B;AAClC,YAAMz5B,SAASsE,MAAMwI,UAAU/L,EAAEgE,EAAE;AAEnC,UAAI,CAAC/E,QAAQ;AACX;MACF;AAEA,YAAM2Q,WAAW3Q,OAAOwR,YAAW;AAEnC,UAAI,CAACb,UAAU;AACb,YAAIpK,MAAuC;AACzC3C,kBAAQ0C,KACN,oEAAoEtG,OAAO+E,EAAE,GAC/E;QACF;AACA;MACF;AAEAw0B,4BAAsBz3B,KAAK;QACzBiD,IAAIhE,EAAEgE;QACN4L;QACA6c,gBAAaiM,wBAAE9oB,SAASb,sBAAkB,OAAA,SAA3Ba,SAASb,mBAAqB/O,EAAEwQ,KAAK,MAAC,OAAAkoB,wBAAI14B,EAAEwQ;MAC7D,CAAC;IACH,CAAC;AAED,UAAMynB,iBAAiBloB,iBAAa,OAAbA,gBAAiB,CAAA,GAAIhJ,IAAI/G,OAAKA,EAAEgE,EAAE;AAEzD,UAAM0e,iBAAiBnf,MAAM0f,kBAAiB;AAE9C,UAAM0V,4BAA4Bp1B,MAC/BmJ,kBAAiB,EACjB5E,OAAO7I,YAAUA,OAAO4jB,mBAAkB,CAAE;AAE/C,QACEL,gBACAE,kBACAiW,0BAA0B13B,QAC1B;AACAg3B,oBAAcl3B,KAAK,YAAY;AAE/B43B,gCAA0B93B,QAAQ5B,YAAU;AAAA,YAAA25B;AAC1CH,8BAAsB13B,KAAK;UACzBiD,IAAI/E,OAAO+E;UACX4L,UAAU8S;UACV+J,gBAAamM,wBACXlW,eAAe3T,sBAAkB,OAAA,SAAjC2T,eAAe3T,mBAAqByT,YAAY,MAAC,OAAAoW,wBACjDpW;QACJ,CAAC;MACH,CAAC;IACH;AAEA,QAAIqW;AACJ,QAAIC;AAGJ,aAASV,IAAI,GAAGA,IAAItL,SAASvc,SAAStP,QAAQm3B,KAAK;AACjD,YAAM50B,MAAMspB,SAASvc,SAAS6nB,CAAC;AAE/B50B,UAAIuM,gBAAgB,CAAA;AAEpB,UAAIyoB,sBAAsBv3B,QAAQ;AAChC,iBAAS8L,IAAI,GAAGA,IAAIyrB,sBAAsBv3B,QAAQ8L,KAAK;AACrD8rB,gCAAsBL,sBAAsBzrB,CAAC;AAC7C,gBAAM/I,KAAK60B,oBAAoB70B;AAG/BR,cAAIuM,cAAc/L,EAAE,IAAI60B,oBAAoBjpB,SAC1CpM,KACAQ,IACA60B,oBAAoBpM,eACpBsM,gBAAc;AACZv1B,gBAAI2O,kBAAkBnO,EAAE,IAAI+0B;UAC9B,CACF;QACF;MACF;AAEA,UAAIN,sBAAsBx3B,QAAQ;AAChC,iBAAS8L,IAAI,GAAGA,IAAI0rB,sBAAsBx3B,QAAQ8L,KAAK;AACrD+rB,gCAAsBL,sBAAsB1rB,CAAC;AAC7C,gBAAM/I,KAAK80B,oBAAoB90B;AAE/B,cACE80B,oBAAoBlpB,SAClBpM,KACAQ,IACA80B,oBAAoBrM,eACpBsM,gBAAc;AACZv1B,gBAAI2O,kBAAkBnO,EAAE,IAAI+0B;UAC9B,CACF,GACA;AACAv1B,gBAAIuM,cAAcipB,aAAa;AAC/B;UACF;QACF;AAEA,YAAIx1B,IAAIuM,cAAcipB,eAAe,MAAM;AACzCx1B,cAAIuM,cAAcipB,aAAa;QACjC;MACF;IACF;AAEA,UAAMd,iBAAkB10B,SAAoB;AAE1C,eAASuJ,IAAI,GAAGA,IAAIkrB,cAAch3B,QAAQ8L,KAAK;AAC7C,YAAIvJ,IAAIuM,cAAckoB,cAAclrB,CAAC,CAAC,MAAO,OAAO;AAClD,iBAAO;QACT;MACF;AACA,aAAO;;AAIT,WAAOoqB,WAAWrK,SAAS7D,MAAMiP,gBAAgB30B,KAAK;EACxD,GACAN,eAAeM,MAAMO,SAAS,cAAc,uBAAuB,MACjEP,MAAMojB,oBAAmB,CAC3B,CACF;AACJ;ACjJO,SAASvQ,qBAEW;AACzB,SAAO7S,WACLrC,KACE,MAAM,CAACqC,MAAM8D,SAAQ,EAAGuN,UAAUrR,MAAM4S,sBAAqB,CAAE,GAC/D,CAACvB,UAAUkY,aAAa;AACtB,QAAI,CAACA,SAAS7D,KAAKhoB,UAAU,CAAC2T,SAAS3T,QAAQ;AAC7C6rB,eAAS7D,KAAKpoB,QAAQ2C,SAAO;AAC3BA,YAAIe,QAAQ;AACZf,YAAImI,WAAW1G;MACjB,CAAC;AACD,aAAO6nB;IACT;AAGA,UAAMmM,mBAAmBrkB,SAAS9M,OAAOrE,cACvCF,MAAMwI,UAAUtI,QAAQ,CAC1B;AAEA,UAAMy1B,kBAAgC,CAAA;AACtC,UAAMC,kBAA8C,CAAA;AAOpD,UAAMC,qBAAqB,SACzBnQ,MACA1kB,OACAoH,UACG;AAAA,UAFHpH,UAAK,QAAA;AAALA,gBAAQ;MAAC;AAKT,UAAIA,SAAS00B,iBAAiBh4B,QAAQ;AACpC,eAAOgoB,KAAKliB,IAAIvD,SAAO;AACrBA,cAAIe,QAAQA;AAEZ20B,0BAAgBn4B,KAAKyC,GAAG;AACxB21B,0BAAgB31B,IAAIQ,EAAE,IAAIR;AAE1B,cAAIA,IAAIkI,SAAS;AACflI,gBAAIkI,UAAU0tB,mBAAmB51B,IAAIkI,SAASnH,QAAQ,GAAGf,IAAIQ,EAAE;UACjE;AAEA,iBAAOR;QACT,CAAC;MACH;AAEA,YAAMC,WAAmBw1B,iBAAiB10B,KAAK;AAG/C,YAAM80B,eAAeC,QAAQrQ,MAAMxlB,QAAQ;AAG3C,YAAM81B,wBAAwBp5B,MAAM6T,KAAKqlB,aAAaG,QAAO,CAAE,EAAEzyB,IAC/D,CAAAtC,MAA+BxC,UAAU;AAAA,YAAxC,CAACw3B,eAAeC,YAAW,IAACj1B;AAC3B,YAAIT,KAAK,GAAGP,QAAQ,IAAIg2B,aAAa;AACrCz1B,aAAK2H,WAAW,GAAGA,QAAQ,IAAI3H,EAAE,KAAKA;AAGtC,cAAM0H,UAAU0tB,mBAAmBM,cAAan1B,QAAQ,GAAGP,EAAE;AAE7D0H,gBAAQ7K,QAAQuuB,YAAU;AACxBA,iBAAOzjB,WAAW3H;QACpB,CAAC;AAGD,cAAMqP,WAAW9O,QACbhE,UAAUm5B,cAAal2B,CAAAA,SAAOA,KAAIkI,OAAO,IACzCguB;AAEJ,cAAMl2B,MAAM+H,UACVhI,OACAS,IACAqP,SAAS,CAAC,EAAG7H,UACbvJ,OACAsC,OACAU,QACA0G,QACF;AAEAiK,eAAOye,OAAO7wB,KAAK;UACjB+S,kBAAkB9S;UAClBg2B;UACA/tB;UACA2H;UACAxP,UAAWJ,CAAAA,cAAqB;AAE9B,gBAAIw1B,iBAAiB9zB,SAAS1B,SAAQ,GAAG;AACvC,kBAAID,IAAIoI,aAAaE,eAAerI,SAAQ,GAAG;AAC7C,uBAAOD,IAAIoI,aAAanI,SAAQ;cAClC;AAEA,kBAAIi2B,aAAY,CAAC,GAAG;AAAA,oBAAAC;AAClBn2B,oBAAIoI,aAAanI,SAAQ,KAACk2B,wBACxBD,aAAY,CAAC,EAAE71B,SAASJ,SAAQ,MAACk2B,OAAAA,wBAAI10B;cACzC;AAEA,qBAAOzB,IAAIoI,aAAanI,SAAQ;YAClC;AAEA,gBAAID,IAAIgT,qBAAqB1K,eAAerI,SAAQ,GAAG;AACrD,qBAAOD,IAAIgT,qBAAqB/S,SAAQ;YAC1C;AAGA,kBAAMxE,SAASsE,MAAMwI,UAAUtI,SAAQ;AACvC,kBAAMm2B,cAAc36B,UAAM,OAAA,SAANA,OAAQ6W,iBAAgB;AAE5C,gBAAI8jB,aAAa;AACfp2B,kBAAIgT,qBAAqB/S,SAAQ,IAAIm2B,YACnCn2B,WACA4P,UACAqmB,YACF;AAEA,qBAAOl2B,IAAIgT,qBAAqB/S,SAAQ;YAC1C;UACF;QACF,CAAC;AAEDiI,gBAAQ7K,QAAQuuB,YAAU;AACxB8J,0BAAgBn4B,KAAKquB,MAAM;AAC3B+J,0BAAgB/J,OAAOprB,EAAE,IAAIorB;QAQ/B,CAAC;AAED,eAAO5rB;MACT,CACF;AAEA,aAAO+1B;;AAGT,UAAMG,cAAcN,mBAAmBtM,SAAS7D,MAAM,CAAC;AAEvDyQ,gBAAY74B,QAAQuuB,YAAU;AAC5B8J,sBAAgBn4B,KAAKquB,MAAM;AAC3B+J,sBAAgB/J,OAAOprB,EAAE,IAAIorB;IAQ/B,CAAC;AAED,WAAO;MACLnG,MAAMyQ;MACNnpB,UAAU2oB;MACVnU,UAAUoU;;KAGdl2B,eAAeM,MAAMO,SAAS,cAAc,sBAAsB,MAAM;AACtEP,UAAMqgB,OAAO,MAAM;AACjBrgB,YAAMmgB,mBAAkB;AACxBngB,YAAMojB,oBAAmB;IAC3B,CAAC;EACH,CAAC,CACH;AACJ;AAEA,SAAS2S,QAA+BrQ,MAAoBxlB,UAAkB;AAC5E,QAAMo2B,WAAW,oBAAIvsB,IAAG;AAExB,SAAO2b,KAAKpc,OAAO,CAAC9F,KAAKvD,QAAQ;AAC/B,UAAMs2B,SAAS,GAAGt2B,IAAI2R,iBAAiB1R,QAAQ,CAAC;AAChD,UAAMs2B,WAAWhzB,IAAIwxB,IAAIuB,MAAM;AAC/B,QAAI,CAACC,UAAU;AACbhzB,UAAIuxB,IAAIwB,QAAQ,CAACt2B,GAAG,CAAC;IACvB,OAAO;AACLu2B,eAASh5B,KAAKyC,GAAG;IACnB;AACA,WAAOuD;KACN8yB,QAAQ;AACb;ACzLO,SAASnR,sBAA6CrnB,MAEV;AACjD,SAAOkC,WACLrC,KACE,MAAM,CACJqC,MAAM8D,SAAQ,EAAGof,YACjBljB,MAAM+gB,yBAAwB,GAC9B/gB,MAAMO,QAAQyf,uBACVte,SACA1B,MAAM8D,SAAQ,EAAGgc,QAAQ,GAE/B,CAACoD,YAAYqG,aAAa;AACxB,QAAI,CAACA,SAAS7D,KAAKhoB,QAAQ;AACzB,aAAO6rB;IACT;AAEA,UAAM;MAAEvG;MAAUD;IAAU,IAAIG;AAChC,QAAI;MAAEwC;MAAM1Y;MAAUwU;IAAS,IAAI+H;AACnC,UAAMkN,YAAYzT,WAAWD;AAC7B,UAAM2T,UAAUD,YAAYzT;AAE5B0C,WAAOA,KAAKhN,MAAM+d,WAAWC,OAAO;AAEpC,QAAIC;AAEJ,QAAI,CAAC32B,MAAMO,QAAQyf,sBAAsB;AACvC2W,0BAAoBzD,WAAW;QAC7BxN;QACA1Y;QACAwU;MACF,CAAC;IACH,OAAO;AACLmV,0BAAoB;QAClBjR;QACA1Y;QACAwU;;IAEJ;AAEAmV,sBAAkB3pB,WAAW,CAAA;AAE7B,UAAMomB,YAAanzB,SAAoB;AACrC02B,wBAAkB3pB,SAASxP,KAAKyC,GAAG;AACnC,UAAIA,IAAIkI,QAAQzK,QAAQ;AACtBuC,YAAIkI,QAAQ7K,QAAQ81B,SAAS;MAC/B;;AAGFuD,sBAAkBjR,KAAKpoB,QAAQ81B,SAAS;AAExC,WAAOuD;KAETj3B,eAAeM,MAAMO,SAAS,cAAc,uBAAuB,CACrE;AACJ;ACvDO,SAASohB,oBAEW;AACzB,SAAO3hB,WACLrC,KACE,MAAM,CAACqC,MAAM8D,SAAQ,EAAGspB,SAASptB,MAAMswB,qBAAoB,CAAE,GAC7D,CAAClD,SAAS7D,aAAa;AACrB,QAAI,CAACA,SAAS7D,KAAKhoB,UAAU,EAAC0vB,WAAO,QAAPA,QAAS1vB,SAAQ;AAC7C,aAAO6rB;IACT;AAEA,UAAMqN,eAAe52B,MAAM8D,SAAQ,EAAGspB;AAEtC,UAAMyJ,iBAA+B,CAAA;AAGrC,UAAMC,mBAAmBF,aAAaryB,OAAO8L,UAAI;AAAA,UAAAgjB;AAAA,cAAAA,mBAC/CrzB,MAAMwI,UAAU6H,KAAK5P,EAAE,MAAvB4yB,OAAAA,SAAAA,iBAA0B7D,WAAU;IAAE,CACxC;AAEA,UAAMuH,iBAOF,CAAA;AAEJD,qBAAiBx5B,QAAQ05B,eAAa;AACpC,YAAMt7B,SAASsE,MAAMwI,UAAUwuB,UAAUv2B,EAAE;AAC3C,UAAI,CAAC/E,OAAQ;AAEbq7B,qBAAeC,UAAUv2B,EAAE,IAAI;QAC7B6sB,eAAe5xB,OAAOqF,UAAUusB;QAChC2J,eAAev7B,OAAOqF,UAAUk2B;QAChC5J,WAAW3xB,OAAOoyB,aAAY;;IAElC,CAAC;AAED,UAAMoJ,WAAYxR,UAAuB;AAGvC,YAAMyR,aAAazR,KAAKliB,IAAIvD,UAAQ;QAAE,GAAGA;MAAI,EAAE;AAE/Ck3B,iBAAW9mB,KAAK,CAAC4b,MAAMC,SAAS;AAC9B,iBAAS1iB,IAAI,GAAGA,IAAIstB,iBAAiBp5B,QAAQ8L,KAAK,GAAG;AAAA,cAAA4tB;AACnD,gBAAMJ,YAAYF,iBAAiBttB,CAAC;AACpC,gBAAM6tB,aAAaN,eAAeC,UAAUv2B,EAAE;AAC9C,gBAAM6sB,gBAAgB+J,WAAW/J;AACjC,gBAAMgK,UAAMF,kBAAGJ,aAAS,OAAA,SAATA,UAAW9I,SAAI,OAAAkJ,kBAAI;AAElC,cAAIG,UAAU;AAGd,cAAIjK,eAAe;AACjB,kBAAMkK,SAASvL,KAAK3rB,SAAS02B,UAAUv2B,EAAE;AACzC,kBAAMg3B,SAASvL,KAAK5rB,SAAS02B,UAAUv2B,EAAE;AAEzC,kBAAMi3B,aAAaF,WAAW91B;AAC9B,kBAAMi2B,aAAaF,WAAW/1B;AAE9B,gBAAIg2B,cAAcC,YAAY;AAC5B,kBAAIrK,kBAAkB,QAAS,QAAOoK,aAAa,KAAK;AACxD,kBAAIpK,kBAAkB,OAAQ,QAAOoK,aAAa,IAAI;AACtDH,wBACEG,cAAcC,aACV,IACAD,aACEpK,gBACA,CAACA;YACX;UACF;AAEA,cAAIiK,YAAY,GAAG;AACjBA,sBAAUF,WAAWhK,UAAUpB,MAAMC,MAAM8K,UAAUv2B,EAAE;UACzD;AAGA,cAAI82B,YAAY,GAAG;AACjB,gBAAID,QAAQ;AACVC,yBAAW;YACb;AAEA,gBAAIF,WAAWJ,eAAe;AAC5BM,yBAAW;YACb;AAEA,mBAAOA;UACT;QACF;AAEA,eAAOtL,KAAKvtB,QAAQwtB,KAAKxtB;MAC3B,CAAC;AAGDy4B,iBAAW75B,QAAQ2C,SAAO;AAAA,YAAAmT;AACxByjB,uBAAer5B,KAAKyC,GAAG;AACvB,aAAAmT,eAAInT,IAAIkI,YAAJiL,QAAAA,aAAa1V,QAAQ;AACvBuC,cAAIkI,UAAU+uB,SAASj3B,IAAIkI,OAAO;QACpC;MACF,CAAC;AAED,aAAOgvB;;AAGT,WAAO;MACLzR,MAAMwR,SAAS3N,SAAS7D,IAAI;MAC5B1Y,UAAU6pB;MACVrV,UAAU+H,SAAS/H;;EAEvB,GACA9hB,eAAeM,MAAMO,SAAS,cAAc,qBAAqB,MAC/DP,MAAMojB,oBAAmB,CAC3B,CACF;AACJ;;;ACvGO,SAASwU,WACdC,MACAC,OACqC;AACrC,SAAO,CAACD,OAAO,OAAOE,iBAAyBF,IAAI,IACjDG,oBAACH,MAASC,KAAQ,IAElBD;AAEJ;AAEA,SAASE,iBACPE,WAC0C;AAC1C,SACEC,iBAAiBD,SAAS,KAC1B,OAAOA,cAAc,cACrBE,kBAAkBF,SAAS;AAE/B;AAEA,SAASC,iBAAiBD,WAAgB;AACxC,SACE,OAAOA,cAAc,eACpB,MAAM;AACL,UAAMG,QAAQC,OAAOC,eAAeL,SAAS;AAC7C,WAAOG,MAAMG,aAAaH,MAAMG,UAAUR;EAC5C,GAAC;AAEL;AAEA,SAASI,kBAAkBF,WAAgB;AACzC,SACE,OAAOA,cAAc,YACrB,OAAOA,UAAUO,aAAa,YAC9B,CAAC,cAAc,mBAAmB,EAAEC,SAASR,UAAUO,SAASE,WAAW;AAE/E;AAEO,SAASC,cACdC,SACA;AAEA,QAAMC,kBAA+C;IACnDC,OAAO,CAAA;;IACPC,eAAeA,MAAM;IAAA;;IACrBC,qBAAqB;IACrB,GAAGJ;;AAIL,QAAM,CAACK,QAAQ,IAAUC,eAAS,OAAO;IACvCC,SAASC,YAAmBP,eAAe;EAC7C,EAAE;AAGF,QAAM,CAACC,OAAOO,QAAQ,IAAUH,eAAS,MAAMD,SAASE,QAAQG,YAAY;AAI5EL,WAASE,QAAQI,WAAWC,WAAS;IACnC,GAAGA;IACH,GAAGZ;IACHE,OAAO;MACL,GAAGA;MACH,GAAGF,QAAQE;;;;IAIbC,eAAeU,aAAW;AACxBJ,eAASI,OAAO;AAChBb,cAAQG,iBAARH,QAAAA,QAAQG,cAAgBU,OAAO;IACjC;EACF,EAAE;AAEF,SAAOR,SAASE;AAClB;", "names": ["createColumnHelper", "accessor", "column", "accessorFn", "accessorKey", "display", "group", "functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "createCell", "table", "row", "columnId", "getRenderValue", "_cell$getValue", "cell", "getValue", "options", "renderFallbackValue", "id", "renderValue", "getContext", "_features", "feature", "createColumn", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "prototype", "replaceAll", "replace", "undefined", "header", "includes", "originalRow", "split", "_result", "warn", "process", "Error", "columns", "getFlatColumns", "_column$columns", "flatMap", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "find", "filter", "Boolean", "rightColumns", "centerColumns", "headerGroups", "buildHeaderGroups", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "childColSpan", "childRowSpan", "minChildRowSpan", "createRow", "original", "rowIndex", "subRows", "parentId", "_valuesCache", "_uniqueValuesCache", "hasOwnProperty", "getColumn", "getUniqueValues", "_row$getValue", "getLeafRows", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getAllCells", "getAllLeafColumns", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "i", "ColumnFaceting", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "includesString", "filterValue", "_filterValue$toString", "search", "toString", "toLowerCase", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "onColumnFiltersChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "previousFilter", "newFilter", "shouldAutoRemoveFilter", "_old$filter", "newFilterObj", "_old$map", "_table", "columnFiltersMeta", "updateFn", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "sum", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "values", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "size", "_columnId", "aggregationFns", "ColumnGrouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "Object", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "_groupingValuesCache", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "nonGroupingColumns", "col", "groupingColumns", "g", "ColumnOrdering", "columnOrder", "onColumnOrderChange", "getIndex", "position", "_getVisibleLeafColumns", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "getDefaultColumnPinningState", "ColumnPinning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "leftAndRight", "getLeftVisibleCells", "cells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "safelyAccessDocument", "_document", "document", "defaultColumnSizing", "minSize", "maxSize", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "columnSizing", "columnSizingInfo", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "slice", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "enableResizing", "enableColumnResizing", "getIsResizing", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "window", "err", "type", "ColumnVisibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "childColumns", "c", "enableHiding", "getToggleVisibilityHandler", "target", "checked", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "GlobalFaceting", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "GlobalFiltering", "globalFilter", "onGlobalFilterChange", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "getCanGlobalFilter", "_table$options$getCol", "enableGlobalFilter", "getGlobalAutoFilterFn", "getGlobalFilterFn", "setGlobalFilter", "resetGlobalFilter", "RowExpanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "_queue", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "getCanSomeRowsExpand", "getPrePaginationRowModel", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowIds", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "getRowCanExpand", "enableExpanding", "getIsAllParentsExpanded", "isFullyExpanded", "getToggleExpandedHandler", "canExpand", "defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "RowPagination", "pagination", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "safeUpdater", "newState", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "getDefaultRowPinningState", "top", "bottom", "RowPinning", "rowPinning", "onRowPinningChange", "includeLeafRows", "includeParentRows", "leafRowIds", "parentRowIds", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "has", "_old$top2", "_old$bottom2", "enableRowPinning", "isTop", "isBottom", "_ref4", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "getTopRows", "getBottomRows", "_ref5", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$top", "_pinningState$bottom", "_getPinnedRows", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "allRows", "topPinnedRowIds", "bottomPinnedRowIds", "getCenterRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "compareAlphanumeric", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "basic", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "RowSorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "builtInFeatures", "_options$_features", "_options$initialState", "debugTable", "defaultOptions", "assign", "mergeOptions", "coreInitialState", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "data", "accessRows", "originalRows", "getSubRows", "_row$originalSubRows", "originalSubRows", "expandRows", "expandedRows", "handleRow", "_table$getColumn", "facetedRowModel", "uniqueValues", "flatRow", "_flatRow$getUniqueVal", "facetedMinValue", "facetedMaxValue", "filterRows", "filterRowImpl", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "_table$options$maxLea2", "pass", "preRowModel", "filterableIds", "filterRowsImpl", "facetedUniqueValues", "j", "_facetedUniqueValues$", "set", "get", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "globallyFilterableColumns", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "filterMeta", "__global__", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "groupMap", "res<PERSON>ey", "previous", "pageStart", "pageEnd", "paginatedRowModel", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aValue", "bValue", "aUndefined", "bUndefined", "flexRender", "Comp", "props", "isReactComponent", "createElement", "component", "isClassComponent", "isExoticComponent", "proto", "Object", "getPrototypeOf", "prototype", "$$typeof", "includes", "description", "useReactTable", "options", "resolvedOptions", "state", "onStateChange", "renderFallbackValue", "tableRef", "useState", "current", "createTable", "setState", "initialState", "setOptions", "prev", "updater"]}