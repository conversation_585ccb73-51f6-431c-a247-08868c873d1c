{"version": 3, "sources": ["../../.pnpm/react-top-loading-bar@3.0.2_react@19.1.1/node_modules/react-top-loading-bar/src/useInterval.ts", "../../.pnpm/react-top-loading-bar@3.0.2_react@19.1.1/node_modules/react-top-loading-bar/src/utils.ts", "../../.pnpm/react-top-loading-bar@3.0.2_react@19.1.1/node_modules/react-top-loading-bar/src/index.tsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\n\n/** keep typescript happy */\nconst noop = () => {};\n\nexport function useInterval(\n  callback: () => void,\n  delay: number | null | false,\n  immediate?: boolean,\n) {\n  const savedCallback = useRef(noop);\n\n  // Remember the latest callback.\n  useEffect(() => {\n    savedCallback.current = callback;\n  });\n\n  // Execute callback if immediate is set.\n  useEffect(() => {\n    if (!immediate) return;\n    if (delay === null || delay === false) return;\n    savedCallback.current();\n  }, [immediate]);\n\n  // Set up the interval.\n  useEffect(() => {\n    if (delay === null || delay === false) return undefined;\n    const tick = () => savedCallback.current();\n    const id = setInterval(tick, delay);\n    return () => clearInterval(id);\n  }, [delay]);\n}\n", "export function randomValue(min: number, max: number): number {\n  return (Math.random() * (max - min + 1) + min)\n}\n\nexport function randomInt(min: number, max: number): number {\n  return Math.floor(randomValue(min, max))\n}\n", "import * as React from \"react\";\nimport {\n  CSSProperties,\n  useEffect,\n  useState,\n  forwardRef,\n  useImperativeHandle,\n  useRef,\n} from \"react\";\nimport { useInterval } from \"./useInterval\";\nimport { randomInt, randomValue } from \"./utils\";\n\nexport interface IProps {\n  progress?: number;\n  color?: string;\n  shadow?: boolean;\n  background?: string;\n  height?: number;\n  onLoaderFinished?: () => void;\n  className?: string;\n  containerClassName?: string;\n  loaderSpeed?: number;\n  transitionTime?: number;\n  waitingTime?: number;\n  style?: CSSProperties;\n  containerStyle?: CSSProperties;\n  shadowStyle?: CSSProperties;\n}\n\nexport interface LoadingBarRef {\n  continuousStart: (startingValue?: number, refreshRate?: number) => void;\n  staticStart: (startingValue?: number) => void;\n  start: (\n    type?: \"continuous\" | \"static\",\n    startingValue?: number,\n    refreshRate?: number,\n  ) => void;\n  complete: () => void;\n  increase: (value: number) => void;\n  decrease: (value: number) => void;\n  getProgress: () => number;\n}\n\nconst LoadingBar = forwardRef<LoadingBarRef, IProps>(\n  (\n    {\n      progress,\n      height = 2,\n      className = \"\",\n      color = \"red\",\n      background = \"transparent\",\n      onLoaderFinished,\n      transitionTime = 300,\n      loaderSpeed = 500,\n      waitingTime = 1000,\n      shadow = true,\n      containerStyle = {},\n      style = {},\n      shadowStyle: shadowStyleProp = {},\n      containerClassName = \"\",\n    },\n    ref,\n  ) => {\n    const isMounted = useRef(false);\n    const [localProgress, localProgressSet] = useState<number>(0);\n\n    const pressedContinuous = useRef<{\n      active: boolean;\n      refreshRate: number;\n    }>({ active: false, refreshRate: 1000 });\n\n    const [pressedStaticStart, setStaticStartPressed] = useState<{\n      active: boolean;\n      value: number;\n    }>({ active: false, value: 60 });\n\n    const initialLoaderStyle: CSSProperties = {\n      height: \"100%\",\n      background: color,\n      transition: `all ${loaderSpeed}ms ease`,\n      width: \"0%\",\n    };\n\n    const loaderContainerStyle: CSSProperties = {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      height,\n      background,\n      zIndex: 99999999999,\n      width: 100 + \"%\",\n    };\n\n    const initialShadowStyles: CSSProperties = {\n      boxShadow: `0 0 10px ${color}, 0 0 10px ${color}`,\n      width: \"5%\",\n      opacity: 1,\n      position: \"absolute\",\n      height: \"100%\",\n      transition: `all ${loaderSpeed}ms ease`,\n      transform: \"rotate(2deg) translate(0px, -2px)\",\n      left: \"-10rem\",\n    };\n\n    const [loaderStyle, loaderStyleSet] =\n      useState<CSSProperties>(initialLoaderStyle);\n    const [shadowStyle, shadowStyleSet] =\n      useState<CSSProperties>(initialShadowStyles);\n\n    useEffect(() => {\n      isMounted.current = true;\n      return () => {\n        isMounted.current = false;\n      };\n    }, []);\n\n    useImperativeHandle(ref, () => ({\n      continuousStart(startingValue?: number, refreshRate: number = 1000) {\n        if (pressedStaticStart.active) return;\n        if (progress !== undefined) {\n          console.warn(\n            \"react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!\",\n          );\n          return;\n        }\n\n        const val = startingValue || randomInt(10, 20);\n\n        pressedContinuous.current = {\n          active: true,\n          refreshRate,\n        };\n\n        localProgressSet(val);\n        checkIfFull(val);\n      },\n      staticStart(startingValue?: number) {\n        if (pressedContinuous.current.active) return;\n        if (progress !== undefined) {\n          console.warn(\n            \"react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!\",\n          );\n          return;\n        }\n\n        const val = startingValue || randomInt(30, 60);\n\n        setStaticStartPressed({\n          active: true,\n          value: val,\n        });\n        localProgressSet(val);\n        checkIfFull(val);\n      },\n      start(type = \"continuous\", startingValue?: number, refreshRate?: number) {\n        if (progress !== undefined) {\n          console.warn(\n            \"react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!\",\n          );\n          return;\n        }\n\n        if (type === \"continuous\") {\n          pressedContinuous.current = {\n            active: true,\n            refreshRate: refreshRate || 1000,\n          };\n        } else {\n          setStaticStartPressed({\n            active: true,\n            value: startingValue || 20,\n          });\n        }\n\n        const continuousRandom = randomInt(10, 20);\n        const staticRandom = randomInt(30, 70);\n\n        const val =\n          startingValue ||\n          (type === \"continuous\" ? continuousRandom : staticRandom);\n\n        localProgressSet(val);\n        checkIfFull(val);\n      },\n      complete() {\n        if (progress !== undefined) {\n          console.warn(\n            \"react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!\",\n          );\n          return;\n        }\n        localProgressSet(100);\n        checkIfFull(100);\n      },\n      increase(value: number) {\n        if (progress !== undefined) {\n          console.warn(\n            \"react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!\",\n          );\n          return;\n        }\n        localProgressSet((prev) => {\n          const newVal = prev + value;\n          checkIfFull(newVal);\n          return newVal;\n        });\n      },\n      decrease(value: number) {\n        if (progress !== undefined) {\n          console.warn(\n            \"react-top-loading-bar: You can't use both controlling by props and ref methods to control the bar!\",\n          );\n          return;\n        }\n        localProgressSet((prev) => {\n          const newVal = prev - value;\n          checkIfFull(newVal);\n          return newVal;\n        });\n      },\n      getProgress() {\n        return localProgress;\n      },\n    }));\n\n    useEffect(() => {\n      loaderStyleSet({\n        ...loaderStyle,\n        background: color,\n      });\n\n      shadowStyleSet({\n        ...shadowStyle,\n        boxShadow: `0 0 10px ${color}, 0 0 5px ${color}`,\n      });\n    }, [color]);\n\n    useEffect(() => {\n      if (ref) {\n        if (ref && progress !== undefined) {\n          console.warn(\n            'react-top-loading-bar: You can\\'t use both controlling by props and ref methods to control the bar! Please use only props or only ref methods! Ref methods will override props if \"ref\" property is available.',\n          );\n          return;\n        }\n        checkIfFull(localProgress);\n      } else {\n        if (progress) checkIfFull(progress);\n      }\n    }, [progress]);\n\n    const checkIfFull = (_progress: number) => {\n      if (_progress >= 100) {\n        // now it should wait a little\n        loaderStyleSet({\n          ...loaderStyle,\n          width: \"100%\",\n        });\n        if (shadow) {\n          shadowStyleSet({\n            ...shadowStyle,\n            left: _progress - 10 + \"%\",\n          });\n        }\n\n        setTimeout(() => {\n          if (!isMounted.current) {\n            return;\n          }\n          // now it can fade out\n          loaderStyleSet({\n            ...loaderStyle,\n            opacity: 0,\n            width: \"100%\",\n            transition: `all ${transitionTime}ms ease-out`,\n            color: color,\n          });\n\n          setTimeout(() => {\n            if (!isMounted.current) {\n              return;\n            }\n            // here we wait for it to fade\n            if (pressedContinuous.current.active) {\n              // if we have continuous loader just ending, we kill it and reset it\n\n              pressedContinuous.current = {\n                ...pressedContinuous.current,\n                active: false,\n              };\n\n              localProgressSet(0);\n              checkIfFull(0);\n            }\n\n            if (pressedStaticStart.active) {\n              setStaticStartPressed({\n                ...pressedStaticStart,\n                active: false,\n              });\n              localProgressSet(0);\n              checkIfFull(0);\n            }\n\n            if (onLoaderFinished) onLoaderFinished();\n            localProgressSet(0);\n            checkIfFull(0);\n          }, transitionTime);\n        }, waitingTime);\n      } else {\n        loaderStyleSet((_loaderStyle) => {\n          return {\n            ..._loaderStyle,\n            width: _progress + \"%\",\n            opacity: 1,\n            transition: _progress > 0 ? `all ${loaderSpeed}ms ease` : \"\",\n          };\n        });\n\n        if (shadow) {\n          shadowStyleSet({\n            ...shadowStyle,\n            left: _progress - 5.5 + \"%\",\n            transition: _progress > 0 ? `all ${loaderSpeed}ms ease` : \"\",\n          });\n        }\n      }\n    };\n\n    useInterval(\n      () => {\n        const minValue = Math.min(10, (100 - localProgress) / 5);\n        const maxValue = Math.min(20, (100 - localProgress) / 3);\n\n        const random = randomValue(minValue, maxValue);\n\n        if (localProgress + random < 95) {\n          localProgressSet(localProgress + random);\n          checkIfFull(localProgress + random);\n        }\n      },\n      pressedContinuous.current.active\n        ? pressedContinuous.current.refreshRate\n        : null,\n    );\n\n    return (\n      <div\n        className={containerClassName}\n        style={{ ...loaderContainerStyle, ...containerStyle }}\n      >\n        <div className={className} style={{ ...loaderStyle, ...style }}>\n          {shadow ? (\n            <div style={{ ...shadowStyle, ...shadowStyleProp }} />\n          ) : null}\n        </div>\n      </div>\n    );\n  },\n);\n\ninterface IContext\n  extends Omit<LoadingBarRef, \"continuousStart\" | \"staticStart\"> {\n  setProps: (props: IProps) => void;\n}\n\nconst LoaderContext = React.createContext<IContext>(undefined as any);\n\nexport const LoadingBarContainer = ({\n  children,\n  props,\n}: {\n  children: React.ReactNode;\n  props?: Omit<IProps, \"progress\">;\n}) => {\n  const [hookProps, setProps] = useState<IProps>(props || {});\n\n  const ref = useRef<LoadingBarRef>(null);\n\n  const start = (type: \"continuous\" | \"static\" = \"continuous\") =>\n    ref.current?.start(type);\n\n  return (\n    <LoaderContext.Provider\n      value={{\n        start,\n        complete: () => ref.current?.complete(),\n        getProgress: () => ref.current?.getProgress() || 0,\n        increase: (value: number) => ref.current?.increase(value),\n        decrease: (value: number) => ref.current?.decrease(value),\n        setProps: (props: IProps) => setProps({ ...props, ...hookProps }),\n      }}\n    >\n      <LoadingBar ref={ref} {...hookProps} />\n      {children}\n    </LoaderContext.Provider>\n  );\n};\n\nexport const useLoadingBar = (props?: IProps): Omit<IContext, \"setProps\"> => {\n  const context = React.useContext(LoaderContext);\n\n  if (!context) {\n    throw new Error(\n      \"[react-top-loading-bar] useLoadingBar hook must be used within a LoadingBarContainer. Try wrapping parent component in <LoadingBarContainer>.\",\n    );\n  }\n\n  useEffect(() => {\n    if (props) context.setProps(props);\n  }, []);\n\n  return {\n    start: context.start,\n    complete: context.complete,\n    increase: context.increase,\n    decrease: context.decrease,\n    getProgress: context.getProgress,\n  };\n};\n\nexport { LoadingBar as default };\n"], "mappings": ";;;;;;;;;;AAGA,IAAMA,IAAO,MAAM;AAAA;AAEZ,SAASC,EACdC,GACAC,GACAC,GACA;AACA,MAAMC,QAAgBC,qBAAON,CAAI;AAGjCO,8BAAU,MAAM;AACdF,MAAc,UAAUH;EAC1B,CAAC,OAGDK,wBAAU,MAAM;EAIhB,GAAG,CAACH,CAAS,CAAC,OAGdG,wBAAU,MAAM;AACd,QAAIJ,MAAU,QAAQA,MAAU,MAAO;AAEvC,QAAMK,IAAK,YADE,MAAMH,EAAc,QAAQ,GACZF,CAAK;AAClC,WAAO,MAAM,cAAcK,CAAE;EAC/B,GAAG,CAACL,CAAK,CAAC;AACZ;AC/BO,SAASM,EAAYC,GAAaC,GAAqB;AAC5D,SAAQ,KAAK,OAAA,KAAYA,IAAMD,IAAM,KAAKA;AAC5C;AAEO,SAASE,EAAUF,GAAaC,GAAqB;AAC1D,SAAO,KAAK,MAAMF,EAAYC,GAAKC,CAAG,CAAC;AACzC;ACqCA,IAAME,QAAaC,yBACjB,CACE,EACE,UAAAC,GACA,QAAAC,IAAS,GACT,WAAAC,IAAY,IACZ,OAAAC,IAAQ,OACR,YAAAC,IAAa,eACb,kBAAAC,GACA,gBAAAC,IAAiB,KACjB,aAAAC,IAAc,KACd,aAAAC,IAAc,KACd,QAAAC,IAAS,MACT,gBAAAC,IAAiB,CAAA,GACjB,OAAAC,IAAQ,CAAA,GACR,aAAaC,IAAkB,CAAA,GAC/B,oBAAAC,IAAqB,GACvB,GACAC,MACG;AACH,MAAMC,QAAYxB,qBAAO,KAAK,GACxB,CAACyB,GAAeC,CAAgB,QAAIC,uBAAiB,CAAC,GAEtDC,QAAoB5B,qBAGvB,EAAE,QAAQ,OAAO,aAAa,IAAK,CAAC,GAEjC,CAAC6B,GAAoBC,CAAqB,QAAIH,uBAGjD,EAAE,QAAQ,OAAO,OAAO,GAAG,CAAC,GAEzBI,IAAoC,EACxC,QAAQ,QACR,YAAYnB,GACZ,YAAY,OAAOI,CAAW,WAC9B,OAAO,KACT,GAEMgB,IAAsC,EAC1C,UAAU,SACV,KAAK,GACL,MAAM,GACN,QAAAtB,GACA,YAAAG,GACA,QAAQ,aACR,OAAO,OACT,GAEMoB,IAAqC,EACzC,WAAW,YAAYrB,CAAK,cAAcA,CAAK,IAC/C,OAAO,MACP,SAAS,GACT,UAAU,YACV,QAAQ,QACR,YAAY,OAAOI,CAAW,WAC9B,WAAW,qCACX,MAAM,SACR,GAEM,CAACkB,GAAaC,CAAc,QAChCR,uBAAwBI,CAAkB,GACtC,CAACK,GAAaC,CAAc,QAChCV,uBAAwBM,CAAmB;AAE7ChC,8BAAU,OACRuB,EAAU,UAAU,MACb,MAAM;AACXA,MAAU,UAAU;EACtB,IACC,CAAA,CAAE,OAELc,kCAAoBf,GAAK,OAAO,EAC9B,gBAAgBgB,GAAwBC,IAAsB,KAAM;AAClE,QAAIX,EAAmB,OAAQ;AAC/B,QAAIpB,MAAa,QAAW;AAC1B,cAAQ,KACN,oGACF;AACA;IACF;AAEA,QAAMgC,IAAMF,KAAiBjC,EAAU,IAAI,EAAE;AAE7CsB,MAAkB,UAAU,EAC1B,QAAQ,MACR,aAAAY,EACF,GAEAd,EAAiBe,CAAG,GACpBC,EAAYD,CAAG;EACjB,GACA,YAAYF,GAAwB;AAClC,QAAIX,EAAkB,QAAQ,OAAQ;AACtC,QAAInB,MAAa,QAAW;AAC1B,cAAQ,KACN,oGACF;AACA;IACF;AAEA,QAAMgC,IAAMF,KAAiBjC,EAAU,IAAI,EAAE;AAE7CwB,MAAsB,EACpB,QAAQ,MACR,OAAOW,EACT,CAAC,GACDf,EAAiBe,CAAG,GACpBC,EAAYD,CAAG;EACjB,GACA,MAAME,IAAO,cAAcJ,GAAwBC,GAAsB;AACvE,QAAI/B,MAAa,QAAW;AAC1B,cAAQ,KACN,oGACF;AACA;IACF;AAEIkC,UAAS,eACXf,EAAkB,UAAU,EAC1B,QAAQ,MACR,aAAaY,KAAe,IAC9B,IAEAV,EAAsB,EACpB,QAAQ,MACR,OAAOS,KAAiB,GAC1B,CAAC;AAGH,QAAMK,IAAmBtC,EAAU,IAAI,EAAE,GACnCuC,IAAevC,EAAU,IAAI,EAAE,GAE/BmC,IACJF,MACCI,MAAS,eAAeC,IAAmBC;AAE9CnB,MAAiBe,CAAG,GACpBC,EAAYD,CAAG;EACjB,GACA,WAAW;AACT,QAAIhC,MAAa,QAAW;AAC1B,cAAQ,KACN,oGACF;AACA;IACF;AACAiB,MAAiB,GAAG,GACpBgB,EAAY,GAAG;EACjB,GACA,SAASI,GAAe;AACtB,QAAIrC,MAAa,QAAW;AAC1B,cAAQ,KACN,oGACF;AACA;IACF;AACAiB,MAAkBqB,OAAS;AACzB,UAAMC,IAASD,IAAOD;AACtB,aAAAJ,EAAYM,CAAM,GACXA;IACT,CAAC;EACH,GACA,SAASF,GAAe;AACtB,QAAIrC,MAAa,QAAW;AAC1B,cAAQ,KACN,oGACF;AACA;IACF;AACAiB,MAAkBqB,OAAS;AACzB,UAAMC,IAASD,IAAOD;AACtB,aAAAJ,EAAYM,CAAM,GACXA;IACT,CAAC;EACH,GACA,cAAc;AACZ,WAAOvB;EACT,EACF,EAAE,OAEFxB,wBAAU,MAAM;AACdkC,MAAe,EACb,GAAGD,GACH,YAAYtB,EACd,CAAC,GAEDyB,EAAe,EACb,GAAGD,GACH,WAAW,YAAYxB,CAAK,aAAaA,CAAK,GAChD,CAAC;EACH,GAAG,CAACA,CAAK,CAAC,OAEVX,wBAAU,MAAM;AACd,QAAIsB,GAAK;AACP,UAAIA,KAAOd,MAAa,QAAW;AACjC,gBAAQ,KACN,+MACF;AACA;MACF;AACAiC,QAAYjB,CAAa;IAC3B,MACMhB,MAAUiC,EAAYjC,CAAQ;EAEtC,GAAG,CAACA,CAAQ,CAAC;AAEb,MAAMiC,IAAeO,OAAsB;AACrCA,SAAa,OAEfd,EAAe,EACb,GAAGD,GACH,OAAO,OACT,CAAC,GACGhB,KACFmB,EAAe,EACb,GAAGD,GACH,MAAMa,IAAY,KAAK,IACzB,CAAC,GAGH,WAAW,MAAM;AACVzB,QAAU,YAIfW,EAAe,EACb,GAAGD,GACH,SAAS,GACT,OAAO,QACP,YAAY,OAAOnB,CAAc,eACjC,OAAOH,EACT,CAAC,GAED,WAAW,MAAM;AACVY,UAAU,YAIXI,EAAkB,QAAQ,WAG5BA,EAAkB,UAAU,EAC1B,GAAGA,EAAkB,SACrB,QAAQ,MACV,GAEAF,EAAiB,CAAC,GAClBgB,EAAY,CAAC,IAGXb,EAAmB,WACrBC,EAAsB,EACpB,GAAGD,GACH,QAAQ,MACV,CAAC,GACDH,EAAiB,CAAC,GAClBgB,EAAY,CAAC,IAGX5B,KAAkBA,EAAAA,GACtBY,EAAiB,CAAC,GAClBgB,EAAY,CAAC;MACf,GAAG3B,CAAc;IACnB,GAAGE,CAAW,MAEdkB,EAAgBe,QACP,EACL,GAAGA,GACH,OAAOD,IAAY,KACnB,SAAS,GACT,YAAYA,IAAY,IAAI,OAAOjC,CAAW,YAAY,GAC5D,EACD,GAEGE,KACFmB,EAAe,EACb,GAAGD,GACH,MAAMa,IAAY,MAAM,KACxB,YAAYA,IAAY,IAAI,OAAOjC,CAAW,YAAY,GAC5D,CAAC;EAGP;AAEA,SAAArB,EACE,MAAM;AACJ,QAAMwD,IAAW,KAAK,IAAI,KAAK,MAAM1B,KAAiB,CAAC,GACjD2B,IAAW,KAAK,IAAI,KAAK,MAAM3B,KAAiB,CAAC,GAEjD4B,IAASlD,EAAYgD,GAAUC,CAAQ;AAEzC3B,QAAgB4B,IAAS,OAC3B3B,EAAiBD,IAAgB4B,CAAM,GACvCX,EAAYjB,IAAgB4B,CAAM;EAEtC,GACAzB,EAAkB,QAAQ,SACtBA,EAAkB,QAAQ,cAC1B,IACN,GAGE,gBAAC,OACC,EAAA,WAAWN,GACX,OAAO,EAAE,GAAGU,GAAsB,GAAGb,EAAe,EAAA,GAEnD,gBAAA,OAAA,EAAI,WAAWR,GAAW,OAAO,EAAE,GAAGuB,GAAa,GAAGd,EAAM,EAAA,GAC1DF,IACC,gBAAC,OAAI,EAAA,OAAO,EAAE,GAAGkB,GAAa,GAAGf,EAAgB,EAAG,CAAA,IAClD,IACN,CACF;AAEJ,CACF;AA5TA,IAmUMiC,IAA8C,gBAAA,MAAgB;AAnUpE,IAqUaC,KAAsB,CAAC,EAClC,UAAAC,GACA,OAAAC,EACF,MAGM;AACJ,MAAM,CAACC,GAAWC,CAAQ,QAAIhC,uBAAiB8B,KAAS,CAAA,CAAE,GAEpDlC,QAAMvB,qBAAsB,IAAI,GAEhC4D,IAAQ,CAACjB,IAAgC,iBAAc;AA3X/D,QAAAkB;AA4XI,YAAAA,IAAAtC,EAAI,YAAJ,OAAAsC,SAAAA,EAAa,MAAMlB,CAAAA;EAAAA;AAErB,SACE,gBAACW,EAAc,UAAd,EACC,OAAO,EACL,OAAAM,GACA,UAAU,MAAG;AAlYrB,QAAAC;AAkYwB,YAAAA,IAAAtC,EAAI,YAAJ,OAAA,SAAAsC,EAAa,SAAA;EAAA,GAC7B,aAAa,MAAG;AAnYxB,QAAAA;AAmY2B,aAAAA,IAAAtC,EAAI,YAAJ,OAAAsC,SAAAA,EAAa,YAAiB,MAAA;EAAA,GACjD,UAAWf,OAAe;AApYlC,QAAAe;AAoYqC,YAAAA,IAAAtC,EAAI,YAAJ,OAAAsC,SAAAA,EAAa,SAASf,CAAAA;EAAAA,GACnD,UAAWA,OAAe;AArYlC,QAAAe;AAqYqC,YAAAA,IAAAtC,EAAI,YAAJ,OAAAsC,SAAAA,EAAa,SAASf,CAAAA;EAAAA,GACnD,UAAWW,OAAkBE,EAAS,EAAE,GAAGF,GAAO,GAAGC,EAAU,CAAC,EAClE,EAAA,GAECnD,gBAAAA,GAAA,EAAW,KAAKgB,GAAM,GAAGmC,EAAW,CAAA,GACpCF,CACH;AAEJ;AAlWA,IAoWaM,KAAiBL,OAA+C;AAC3E,MAAMM,IAAgB,aAAWT,CAAa;AAE9C,MAAI,CAACS,EACH,OAAM,IAAI,MACR,+IACF;AAGF,aAAA9D,wBAAU,MAAM;AACVwD,SAAOM,EAAQ,SAASN,CAAK;EACnC,GAAG,CAAA,CAAE,GAEE,EACL,OAAOM,EAAQ,OACf,UAAUA,EAAQ,UAClB,UAAUA,EAAQ,UAClB,UAAUA,EAAQ,UAClB,aAAaA,EAAQ,YACvB;AACF;", "names": ["noop", "useInterval", "callback", "delay", "immediate", "savedCallback", "useRef", "useEffect", "id", "randomValue", "min", "max", "randomInt", "LoadingBar", "forwardRef", "progress", "height", "className", "color", "background", "onLoaderFinished", "transitionTime", "loaderSpeed", "waitingTime", "shadow", "containerStyle", "style", "shadowStyleProp", "containerClassName", "ref", "isMounted", "localProgress", "localProgressSet", "useState", "pressedContinuous", "pressedStaticStart", "setStaticStartPressed", "initialLoaderStyle", "loaderContainerStyle", "initialShadowStyles", "loaderStyle", "loaderStyleSet", "shadowStyle", "shadowStyleSet", "useImperativeHandle", "startingValue", "refreshRate", "val", "checkIfFull", "type", "continuousRandom", "staticRandom", "value", "prev", "newVal", "_progress", "_loaderStyle", "minValue", "maxValue", "random", "LoaderContext", "LoadingBarContainer", "children", "props", "hookProps", "setProps", "start", "_a", "useLoadingBar", "context"]}