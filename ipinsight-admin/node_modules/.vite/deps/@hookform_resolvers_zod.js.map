{"version": 3, "sources": ["../../.pnpm/@hookform+resolvers@5.2.1_react-hook-form@7.62.0_react@19.1.1_/node_modules/@hookform/resolvers/src/validateFieldsNatively.ts", "../../.pnpm/@hookform+resolvers@5.2.1_react-hook-form@7.62.0_react@19.1.1_/node_modules/@hookform/resolvers/src/toNestErrors.ts", "../../.pnpm/@hookform+resolvers@5.2.1_react-hook-form@7.62.0_react@19.1.1_/node_modules/@hookform/resolvers/zod/src/zod.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field && field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => {\n  const path = escapeBrackets(name);\n  return names.some((n) => escapeBrackets(n).match(`^${path}\\\\.\\\\d+`));\n};\n\n/**\n * Escapes special characters in a string to be used in a regex pattern.\n * it removes the brackets from the string to match the `set` method.\n *\n * @param input - The input string to escape.\n * @returns The escaped string.\n */\nfunction escapeBrackets(input: string): string {\n  return input.replace(/\\]|\\[/g, '');\n}\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  ResolverError,\n  ResolverSuccess,\n  appendErrors,\n} from 'react-hook-form';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4/core';\n\nconst isZod3Error = (error: any): error is z3.ZodError => {\n  return Array.isArray(error?.issues);\n};\nconst isZod3Schema = (schema: any): schema is z3.ZodSchema => {\n  return (\n    '_def' in schema &&\n    typeof schema._def === 'object' &&\n    'typeName' in schema._def\n  );\n};\nconst isZod4Error = (error: any): error is z4.$ZodError => {\n  // instanceof is safe in Zod 4 (uses Symbol.hasInstance)\n  return error instanceof z4.$ZodError;\n};\nconst isZod4Schema = (schema: any): schema is z4.$ZodType => {\n  return '_zod' in schema && typeof schema._zod === 'object';\n};\n\nfunction parseZod3Issues(\n  zodErrors: z3.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\nfunction parseZod4Issues(\n  zodErrors: z4.$ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  // const _zodErrors = zodErrors as z4.$ZodISsue; //\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if (error.code === 'invalid_union' && error.errors.length > 0) {\n        const unionError = error.errors[0][0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if (error.code === 'invalid_union') {\n      error.errors.forEach((unionError) =>\n        unionError.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\ntype RawResolverOptions = {\n  mode?: 'async' | 'sync';\n  raw: true;\n};\ntype NonRawResolverOptions = {\n  mode?: 'async' | 'sync';\n  raw?: false;\n};\n\n// minimal interfaces to avoid asssignability issues between versions\ninterface Zod3Type<O = unknown, I = unknown> {\n  _output: O;\n  _input: I;\n  _def: {\n    typeName: string;\n  };\n}\n\n// some type magic to make versions pre-3.25.0 still work\ntype IsUnresolved<T> = PropertyKey extends keyof T ? true : false;\ntype UnresolvedFallback<T, Fallback> = IsUnresolved<typeof z3> extends true\n  ? Fallback\n  : T;\ntype FallbackIssue = {\n  code: string;\n  message: string;\n  path: (string | number)[];\n};\ntype Zod3ParseParams = UnresolvedFallback<\n  z3.ParseParams,\n  // fallback if user is on <3.25.0\n  {\n    path?: (string | number)[];\n    errorMap?: (\n      iss: FallbackIssue,\n      ctx: {\n        defaultError: string;\n        data: any;\n      },\n    ) => { message: string };\n    async?: boolean;\n  }\n>;\ntype Zod4ParseParams = UnresolvedFallback<\n  z4.ParseContext<z4.$ZodIssue>,\n  // fallback if user is on <3.25.0\n  {\n    readonly error?: (\n      iss: FallbackIssue,\n    ) => null | undefined | string | { message: string };\n    readonly reportInput?: boolean;\n    readonly jitless?: boolean;\n  }\n>;\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: Zod3Type<Output, Input>,\n  schemaOptions?: Zod3ParseParams,\n  resolverOptions?: NonRawResolverOptions,\n): Resolver<Input, Context, Output>;\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: Zod3Type<Output, Input>,\n  schemaOptions: Zod3ParseParams | undefined,\n  resolverOptions: RawResolverOptions,\n): Resolver<Input, Context, Input>;\n// the Zod 4 overloads need to be generic for complicated reasons\nexport function zodResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n  T extends z4.$ZodType<Output, Input> = z4.$ZodType<Output, Input>,\n>(\n  schema: T,\n  schemaOptions?: Zod4ParseParams, // already partial\n  resolverOptions?: NonRawResolverOptions,\n): Resolver<z4.input<T>, Context, z4.output<T>>;\nexport function zodResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n  T extends z4.$ZodType<Output, Input> = z4.$ZodType<Output, Input>,\n>(\n  schema: z4.$ZodType<Output, Input>,\n  schemaOptions: Zod4ParseParams | undefined, // already partial\n  resolverOptions: RawResolverOptions,\n): Resolver<z4.input<T>, Context, z4.output<T>>;\n/**\n * Creates a resolver function for react-hook-form that validates form data using a Zod schema\n * @param {z3.ZodSchema<Input>} schema - The Zod schema used to validate the form data\n * @param {Partial<z3.ParseParams>} [schemaOptions] - Optional configuration options for Zod parsing\n * @param {Object} [resolverOptions] - Optional resolver-specific configuration\n * @param {('async'|'sync')} [resolverOptions.mode='async'] - Validation mode. Use 'sync' for synchronous validation\n * @param {boolean} [resolverOptions.raw=false] - If true, returns the raw form values instead of the parsed data\n * @returns {Resolver<z3.output<typeof schema>>} A resolver function compatible with react-hook-form\n * @throws {Error} Throws if validation fails with a non-Zod error\n * @example\n * const schema = z3.object({\n *   name: z3.string().min(2),\n *   age: z3.number().min(18)\n * });\n *\n * useForm({\n *   resolver: zodResolver(schema)\n * });\n */\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: object,\n  schemaOptions?: object,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  if (isZod3Schema(schema)) {\n    return async (values: Input, _, options) => {\n      try {\n        const data = await schema[\n          resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n        ](values, schemaOptions);\n\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          errors: {} as FieldErrors,\n          values: resolverOptions.raw ? Object.assign({}, values) : data,\n        } satisfies ResolverSuccess<Output | Input>;\n      } catch (error) {\n        if (isZod3Error(error)) {\n          return {\n            values: {},\n            errors: toNestErrors(\n              parseZod3Issues(\n                error.errors,\n                !options.shouldUseNativeValidation &&\n                  options.criteriaMode === 'all',\n              ),\n              options,\n            ),\n          } satisfies ResolverError<Input>;\n        }\n\n        throw error;\n      }\n    };\n  }\n\n  if (isZod4Schema(schema)) {\n    return async (values: Input, _, options) => {\n      try {\n        const parseFn =\n          resolverOptions.mode === 'sync' ? z4.parse : z4.parseAsync;\n        const data: any = await parseFn(schema, values, schemaOptions);\n\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          errors: {} as FieldErrors,\n          values: resolverOptions.raw ? Object.assign({}, values) : data,\n        } satisfies ResolverSuccess<Output | Input>;\n      } catch (error) {\n        if (isZod4Error(error)) {\n          return {\n            values: {},\n            errors: toNestErrors(\n              parseZod4Issues(\n                error.issues,\n                !options.shouldUseNativeValidation &&\n                  options.criteriaMode === 'all',\n              ),\n              options,\n            ),\n          } satisfies ResolverError<Input>;\n        }\n\n        throw error;\n      }\n    };\n  }\n\n  throw new Error('Invalid input: not a Zod schema');\n}\n"], "mappings": ";;;;;;;;;;;;;;AASA,IAAMA,IAAoBA,CACxBC,IACAC,IACAC,OAAAA;AAEA,MAAIF,MAAO,oBAAoBA,IAAK;AAClC,UAAMG,KAAQC,IAAIF,IAAQD,EAAAA;AAC1BD,IAAAA,GAAID,kBAAmBI,MAASA,GAAME,WAAY,EAAA,GAElDL,GAAIM,eAAAA;EACN;AAAA;AAVF,IAcaC,IAAyBA,CACpCL,GACAM,OAAAA;AAEA,aAAWP,MAAaO,GAAQC,QAAQ;AACtC,UAAMC,KAAQF,GAAQC,OAAOR,EAAAA;AACzBS,IAAAA,MAASA,GAAMV,OAAO,oBAAoBU,GAAMV,MAClDD,EAAkBW,GAAMV,KAAKC,IAAWC,CAAAA,IAC/BQ,MAASA,GAAMC,QACxBD,GAAMC,KAAKC,QAASZ,CAAAA,OAClBD,EAAkBC,IAAKC,IAAWC,CAAAA,CAAAA;EAGxC;AAAA;AA3BF,ICEaW,IAAeA,CAC1BX,IACAM,OAAAA;AAEAA,EAAAA,GAAQM,6BAA6BP,EAAuBL,IAAQM,EAAAA;AAEpE,QAAMO,KAAc,CAAA;AACpB,aAAWC,MAAQd,IAAQ;AACzB,UAAMQ,IAAQN,IAAII,GAAQC,QAAQO,EAAAA,GAC5Bb,IAAQc,OAAOC,OAAOhB,GAAOc,EAAAA,KAAS,CAAA,GAAI,EAC9ChB,KAAKU,KAASA,EAAMV,IAAAA,CAAAA;AAGtB,QAAImB,EAAmBX,GAAQY,SAASH,OAAOI,KAAKnB,EAAAA,GAASc,EAAAA,GAAO;AAClE,YAAMM,KAAmBL,OAAOC,OAAO,CAAA,GAAId,IAAIW,IAAaC,EAAAA,CAAAA;AAE5DO,UAAID,IAAkB,QAAQnB,CAAAA,GAC9BoB,IAAIR,IAAaC,IAAMM,EAAAA;IACzB,MACEC,KAAIR,IAAaC,IAAMb,CAAAA;EAE3B;AAEA,SAAOY;AAAAA;ADzBT,IC4BMI,IAAqBA,CACzBC,GACAI,OAAAA;AAEA,QAAMR,KAAOS,EAAeD,EAAAA;AAC5B,SAAOJ,EAAMM,KAAMC,CAAAA,OAAMF,EAAeE,EAAAA,EAAGC,MAAM,IAAIZ,EAAAA,SAAAA,CAAAA;AAAc;AAUrE,SAASS,EAAeI,GAAAA;AACtB,SAAOA,EAAMC,QAAQ,UAAU,EAAA;AACjC;;;;;;;;;;;ACvBA,SAASC,GACPC,IACAC,GAAAA;AAGA,WADMC,KAAqC,CAAE,GACtCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,KAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,KAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,EAAAA,EACV,KAAI,iBAAiBH,IAAO;AAC1B,UAAMM,IAAaN,GAAMO,YAAY,CAAA,EAAGT,OAAO,CAAA;AAE/CA,MAAAA,GAAOK,EAAAA,IAAS,EACdD,SAASI,EAAWJ,SACpBM,MAAMF,EAAWL,KAAAA;IAErB,MACEH,CAAAA,GAAOK,EAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,GAAAA;AAUrC,QANI,iBAAiBD,MACnBA,GAAMO,YAAYE,QAAQ,SAACH,IAAAA;AACzB,aAAAA,GAAWR,OAAOW,QAAQ,SAACC,IAAAA;AAAM,eAAAd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAInDb,GAA0B;AAC5B,UAAMe,IAAQd,GAAOK,EAAAA,EAAOS,OACtBC,IAAWD,KAASA,EAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,EAAAA,IAASW,aACdX,IACAN,GACAC,IACAG,IACAY,IACK,CAAA,EAAgBE,OAAOF,GAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEd;AAEAN,IAAAA,GAAUoB,MAAAA;EACZ;AAEA,SAAOlB;AACT;AAEA,SAASmB,GACPrB,IACAC,GAAAA;AAIA,WAFMC,KAAqC,CAAA,GAEpCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,KAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,KAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,EAAAA,EACV,KAAmB,oBAAfH,GAAMC,QAA4BD,GAAMF,OAAOC,SAAS,GAAG;AAC7D,UAAMO,IAAaN,GAAMF,OAAO,CAAA,EAAG,CAAA;AAEnCA,MAAAA,GAAOK,EAAAA,IAAS,EACdD,SAASI,EAAWJ,SACpBM,MAAMF,EAAWL,KAAAA;IAErB,MACEH,CAAAA,GAAOK,EAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,GAAAA;AAUrC,QANmB,oBAAfD,GAAMC,QACRD,GAAMF,OAAOW,QAAQ,SAACH,IAAAA;AACpB,aAAAA,GAAWG,QAAQ,SAACC,IAAAA;AAAM,eAAAd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAI5Cb,GAA0B;AAC5B,UAAMe,IAAQd,GAAOK,EAAAA,EAAOS,OACtBC,IAAWD,KAASA,EAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,EAAAA,IAASW,aACdX,IACAN,GACAC,IACAG,IACAY,IACK,CAAA,EAAgBE,OAAOF,GAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEd;AAEAN,IAAAA,GAAUoB,MAAAA;EACZ;AAEA,SAAOlB;AACT;AA2GgB,SAAAoB,EACdC,IACAC,IACAC,GAAAA;AAKA,MAAA,WALAA,MAAAA,IAGI,CAAA,IAnOe,SAACF,IAAAA;AACpB,WACE,UAAUA,MACa,YAAA,OAAhBA,GAAOG,QACd,cAAcH,GAAOG;EAEzB,EA+NmBH,EAAAA,EACf,QAAcI,SAAAA,IAAeC,IAAGC,GAAAA;AAAW,QAAA;AAAA,aAAAC,QAAAC,QAAAC,EAAA,WAAA;AACrCF,eAAAA,QAAAC,QACiBR,GACQ,WAAzBE,EAAgBQ,OAAkB,UAAU,YAAA,EAC5CN,IAAQH,EAAAA,CAAAA,EAAcU,KAAA,SAFlBC,GAAAA;AAON,iBAHAN,EAAQO,6BACNC,EAAuB,CAAA,GAAIR,CAAAA,GAEtB,EACL3B,QAAQ,CAAiB,GACzByB,QAAQF,EAAgBa,MAAMC,OAAOC,OAAO,CAAA,GAAIb,EAAAA,IAAUQ,EAAAA;QAChB,CAAA;MAC9C,GAAS/B,SAAAA,IAAAA;AACP,YAvPY,SAACA,IAAAA;AACnB,iBAAOqC,MAAMC,QAAAA,QAAQtC,KAAAA,SAAAA,GAAOuC,MAAAA;QAC9B,EAqPwBvC,EAAAA,EACd,QAAO,EACLuB,QAAQ,CAAA,GACRzB,QAAQ0C,EACN7C,GACEK,GAAMF,QAAAA,CACL2B,EAAQO,6BACkB,UAAzBP,EAAQgB,YAAAA,GAEZhB,CAAAA,EAAAA;AAKN,cAAMzB;MACR,CAAA,CAAA;IACF,SAACU,IAAAA;AAAAgB,aAAAA,QAAAgB,OAAAhC,EAAAA;IACH;EAAA;AAEA,MA5PmB,SAACS,IAAAA;AACpB,WAAO,UAAUA,MAAiC,YAAA,OAAhBA,GAAOwB;EAC3C,EA0PmBxB,EAAAA,EACf,QAAA,SAAcI,IAAeC,GAAGC,GAAAA;AAAW,QAAA;AAAA,aAAAC,QAAAC,QAAAC,EAAA,WAAA;AAGsB,eAAAF,QAAAC,SAAlC,WAAzBN,EAAgBQ,OAAqBe,QAAWC,YAClB1B,IAAQI,IAAQH,EAAAA,CAAAA,EAAcU,KAAA,SAAxDC,GAAAA;AAKN,iBAHAN,EAAQO,6BACNC,EAAuB,CAAE,GAAER,CAAAA,GAEtB,EACL3B,QAAQ,CAAA,GACRyB,QAAQF,EAAgBa,MAAMC,OAAOC,OAAO,CAAE,GAAEb,EAAAA,IAAUQ,EAAAA;QAChB,CAAA;MAC9C,GAAS/B,SAAAA,IAAAA;AACP,YA/QY,SAACA,IAAAA;AAEnB,iBAAOA,cAAoB8C;QAC7B,EA4QwB9C,EAAAA,EACd,QAAO,EACLuB,QAAQ,CAAE,GACVzB,QAAQ0C,EACNvB,GACEjB,GAAMuC,QAAAA,CACLd,EAAQO,6BACkB,UAAzBP,EAAQgB,YAAAA,GAEZhB,CAAAA,EAAAA;AAKN,cAAMzB;MACR,CAAA,CAAA;IACF,SAACU,IAAAA;AAAAgB,aAAAA,QAAAgB,OAAAhC,EAAAA;IACH;EAAA;AAEA,QAAM,IAAIqC,MAAM,iCAAA;AAClB;", "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "fields", "field", "refs", "for<PERSON>ach", "toNestErrors", "shouldUseNativeValidation", "fieldErrors", "path", "Object", "assign", "isNameInFieldArray", "names", "keys", "fieldArrayErrors", "set", "name", "escapeBrackets", "some", "n", "match", "input", "replace", "parseZod3Issues", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "parseZod4Issues", "zodResolver", "schema", "schemaOptions", "resolverOptions", "_def", "values", "_", "options", "Promise", "resolve", "_catch", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Object", "assign", "Array", "isArray", "issues", "toNestErrors", "criteriaMode", "reject", "_zod", "parse", "parseAsync", "$ZodError", "Error"]}