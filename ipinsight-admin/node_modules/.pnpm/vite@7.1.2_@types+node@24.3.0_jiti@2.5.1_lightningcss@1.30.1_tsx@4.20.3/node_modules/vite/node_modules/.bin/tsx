#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/dist/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/tsx@4.20.3/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/dist/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/tsx@4.20.3/node_modules:/Users/<USER>/Desktop/conan-work/ipInsight/ipinsight-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../tsx@4.20.3/node_modules/tsx/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../../../../../tsx@4.20.3/node_modules/tsx/dist/cli.mjs" "$@"
fi
