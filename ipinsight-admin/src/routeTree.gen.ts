/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthenticatedRouteRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedSystemRouteImport } from './routes/_authenticated/system'
import { Route as AuthenticatedDatasourcesRouteImport } from './routes/_authenticated/datasources'
import { Route as AuthenticatedApiTestRouteImport } from './routes/_authenticated/api-test'
import { Route as AuthenticatedApiStatsRouteImport } from './routes/_authenticated/api-stats'
import { Route as AuthenticatedAboutRouteImport } from './routes/_authenticated/about'
import { Route as errors503RouteImport } from './routes/(errors)/503'
import { Route as errors500RouteImport } from './routes/(errors)/500'
import { Route as errors404RouteImport } from './routes/(errors)/404'
import { Route as errors403RouteImport } from './routes/(errors)/403'
import { Route as errors401RouteImport } from './routes/(errors)/401'
import { Route as authSignInRouteImport } from './routes/(auth)/sign-in'
import { Route as AuthenticatedSettingsRouteRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedSettingsIndexRouteImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedSettingsNotificationsRouteImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayRouteImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceRouteImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountRouteImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedErrorsErrorRouteImport } from './routes/_authenticated/errors/$error'

const AuthenticatedRouteRoute = AuthenticatedRouteRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedSystemRoute = AuthenticatedSystemRouteImport.update({
  id: '/system',
  path: '/system',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedDatasourcesRoute =
  AuthenticatedDatasourcesRouteImport.update({
    id: '/datasources',
    path: '/datasources',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedApiTestRoute = AuthenticatedApiTestRouteImport.update({
  id: '/api-test',
  path: '/api-test',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedApiStatsRoute = AuthenticatedApiStatsRouteImport.update({
  id: '/api-stats',
  path: '/api-stats',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedAboutRoute = AuthenticatedAboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const errors503Route = errors503RouteImport.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRouteImport,
} as any)
const errors500Route = errors500RouteImport.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRouteImport,
} as any)
const errors404Route = errors404RouteImport.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any)
const errors403Route = errors403RouteImport.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRouteImport,
} as any)
const errors401Route = errors401RouteImport.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRouteImport,
} as any)
const authSignInRoute = authSignInRouteImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedSettingsRouteRoute =
  AuthenticatedSettingsRouteRouteImport.update({
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedSettingsIndexRoute =
  AuthenticatedSettingsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsRouteImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayRouteImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceRouteImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountRouteImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedErrorsErrorRoute =
  AuthenticatedErrorsErrorRouteImport.update({
    id: '/errors/$error',
    path: '/errors/$error',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/sign-in': typeof authSignInRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/about': typeof AuthenticatedAboutRoute
  '/api-stats': typeof AuthenticatedApiStatsRoute
  '/api-test': typeof AuthenticatedApiTestRoute
  '/datasources': typeof AuthenticatedDatasourcesRoute
  '/system': typeof AuthenticatedSystemRoute
  '/': typeof AuthenticatedIndexRoute
  '/errors/$error': typeof AuthenticatedErrorsErrorRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
}
export interface FileRoutesByTo {
  '/sign-in': typeof authSignInRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/about': typeof AuthenticatedAboutRoute
  '/api-stats': typeof AuthenticatedApiStatsRoute
  '/api-test': typeof AuthenticatedApiTestRoute
  '/datasources': typeof AuthenticatedDatasourcesRoute
  '/system': typeof AuthenticatedSystemRoute
  '/': typeof AuthenticatedIndexRoute
  '/errors/$error': typeof AuthenticatedErrorsErrorRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/(auth)/sign-in': typeof authSignInRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/about': typeof AuthenticatedAboutRoute
  '/_authenticated/api-stats': typeof AuthenticatedApiStatsRoute
  '/_authenticated/api-test': typeof AuthenticatedApiTestRoute
  '/_authenticated/datasources': typeof AuthenticatedDatasourcesRoute
  '/_authenticated/system': typeof AuthenticatedSystemRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/errors/$error': typeof AuthenticatedErrorsErrorRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/settings'
    | '/sign-in'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/about'
    | '/api-stats'
    | '/api-test'
    | '/datasources'
    | '/system'
    | '/'
    | '/errors/$error'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/settings/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/sign-in'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/about'
    | '/api-stats'
    | '/api-test'
    | '/datasources'
    | '/system'
    | '/'
    | '/errors/$error'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/settings'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_authenticated/settings'
    | '/(auth)/sign-in'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/about'
    | '/_authenticated/api-stats'
    | '/_authenticated/api-test'
    | '/_authenticated/datasources'
    | '/_authenticated/system'
    | '/_authenticated/'
    | '/_authenticated/errors/$error'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/_authenticated/settings/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  authSignInRoute: typeof authSignInRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/system': {
      id: '/_authenticated/system'
      path: '/system'
      fullPath: '/system'
      preLoaderRoute: typeof AuthenticatedSystemRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/datasources': {
      id: '/_authenticated/datasources'
      path: '/datasources'
      fullPath: '/datasources'
      preLoaderRoute: typeof AuthenticatedDatasourcesRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/api-test': {
      id: '/_authenticated/api-test'
      path: '/api-test'
      fullPath: '/api-test'
      preLoaderRoute: typeof AuthenticatedApiTestRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/api-stats': {
      id: '/_authenticated/api-stats'
      path: '/api-stats'
      fullPath: '/api-stats'
      preLoaderRoute: typeof AuthenticatedApiStatsRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/about': {
      id: '/_authenticated/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AuthenticatedAboutRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/errors/$error': {
      id: '/_authenticated/errors/$error'
      path: '/errors/$error'
      fullPath: '/errors/$error'
      preLoaderRoute: typeof AuthenticatedErrorsErrorRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
  }
}

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedAboutRoute: typeof AuthenticatedAboutRoute
  AuthenticatedApiStatsRoute: typeof AuthenticatedApiStatsRoute
  AuthenticatedApiTestRoute: typeof AuthenticatedApiTestRoute
  AuthenticatedDatasourcesRoute: typeof AuthenticatedDatasourcesRoute
  AuthenticatedSystemRoute: typeof AuthenticatedSystemRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedErrorsErrorRoute: typeof AuthenticatedErrorsErrorRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedAboutRoute: AuthenticatedAboutRoute,
  AuthenticatedApiStatsRoute: AuthenticatedApiStatsRoute,
  AuthenticatedApiTestRoute: AuthenticatedApiTestRoute,
  AuthenticatedDatasourcesRoute: AuthenticatedDatasourcesRoute,
  AuthenticatedSystemRoute: AuthenticatedSystemRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedErrorsErrorRoute: AuthenticatedErrorsErrorRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  authSignInRoute: authSignInRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
