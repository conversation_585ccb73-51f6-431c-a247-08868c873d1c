import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ConfigDrawer } from '@/components/config-drawer'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { 
  Info, 
  Globe, 
  Database,
  Code,
  Shield,
  Zap,
  Users,
  Github,
  ExternalLink
} from 'lucide-react'
import { apiService } from '@/services/api'
import type { SystemStatus } from '@/types/api'

export function About() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSystemStatus = async () => {
      try {
        const status = await apiService.getSystemStatus()
        setSystemStatus(status)
      } catch (error) {
        console.error('获取系统状态失败:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSystemStatus()
  }, [])

  const features = [
    {
      icon: <Globe className='h-6 w-6 text-blue-500' />,
      title: 'IP地理位置查询',
      description: '支持IPv4和IPv6地址的精确地理位置查询，包含国家、地区、城市等详细信息'
    },
    {
      icon: <Database className='h-6 w-6 text-green-500' />,
      title: '多数据源支持',
      description: '集成多个权威IP地理位置数据库，确保查询结果的准确性和覆盖率'
    },
    {
      icon: <Zap className='h-6 w-6 text-yellow-500' />,
      title: '高性能缓存',
      description: '智能缓存机制，大幅提升查询响应速度，减少重复查询的延迟'
    },
    {
      icon: <Shield className='h-6 w-6 text-red-500' />,
      title: '安全可靠',
      description: '完善的认证授权机制，确保API接口的安全访问和数据保护'
    },
    {
      icon: <Code className='h-6 w-6 text-purple-500' />,
      title: 'RESTful API',
      description: '标准的REST API设计，支持单个和批量IP查询，易于集成和使用'
    },
    {
      icon: <Users className='h-6 w-6 text-indigo-500' />,
      title: '管理后台',
      description: '功能完善的Web管理界面，实时监控系统状态和API使用情况'
    }
  ]

  const techStack = [
    { name: 'Go', description: '高性能后端服务开发' },
    { name: 'React', description: '现代化前端界面框架' },
    { name: 'TypeScript', description: '类型安全的JavaScript' },
    { name: 'TailwindCSS', description: '实用优先的CSS框架' },
    { name: 'Vite', description: '快速的前端构建工具' },
    { name: 'Tanstack Router', description: '类型安全的路由管理' }
  ]

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='flex items-center space-x-4'>
          <h1 className='text-lg font-semibold'>关于项目</h1>
        </div>
        <div className='ms-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ConfigDrawer />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6'>
          <h2 className='text-2xl font-bold tracking-tight'>关于 IP Insight</h2>
          <p className='text-muted-foreground'>
            专业的IP地理位置查询服务和管理平台
          </p>
        </div>

        <div className='space-y-6'>
          {/* 项目介绍 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Info className='h-5 w-5' />
                <span>项目介绍</span>
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <p className='text-muted-foreground leading-relaxed'>
                IP Insight 是一个高性能的IP地理位置查询服务，提供准确、快速的IP地址地理信息查询功能。
                系统集成了多个权威数据源，通过智能缓存和优化算法，为用户提供毫秒级的查询响应。
              </p>
              <p className='text-muted-foreground leading-relaxed'>
                本项目不仅提供了强大的API服务，还配备了完善的Web管理界面，支持实时监控、数据源管理、
                API测试等功能，是企业级IP地理位置服务的理想选择。
              </p>
            </CardContent>
          </Card>

          {/* 系统信息 */}
          <Card>
            <CardHeader>
              <CardTitle>系统信息</CardTitle>
              <CardDescription>
                当前系统的版本和运行状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-3'>
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className='space-y-2'>
                      <Skeleton className='h-4 w-16' />
                      <Skeleton className='h-6 w-24' />
                    </div>
                  ))}
                </div>
              ) : (
                <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-3'>
                  <div>
                    <p className='text-sm text-muted-foreground'>版本号</p>
                    <p className='text-lg font-semibold'>
                      {systemStatus?.version || 'v1.0.0'}
                    </p>
                  </div>
                  <div>
                    <p className='text-sm text-muted-foreground'>系统状态</p>
                    <Badge variant={systemStatus?.status === 'healthy' ? 'default' : 'destructive'}>
                      {systemStatus?.status || 'unknown'}
                    </Badge>
                  </div>
                  <div>
                    <p className='text-sm text-muted-foreground'>认证方式</p>
                    <p className='text-lg font-semibold'>
                      {systemStatus?.auth_method || 'Token'}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 核心功能 */}
          <Card>
            <CardHeader>
              <CardTitle>核心功能</CardTitle>
              <CardDescription>
                IP Insight 提供的主要功能特性
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid gap-6 sm:grid-cols-2 lg:grid-cols-3'>
                {features.map((feature, index) => (
                  <div key={index} className='space-y-3'>
                    <div className='flex items-center space-x-3'>
                      {feature.icon}
                      <h3 className='font-semibold'>{feature.title}</h3>
                    </div>
                    <p className='text-sm text-muted-foreground leading-relaxed'>
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 技术栈 */}
          <Card>
            <CardHeader>
              <CardTitle>技术栈</CardTitle>
              <CardDescription>
                项目使用的主要技术和框架
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-3'>
                {techStack.map((tech, index) => (
                  <div key={index} className='flex items-center justify-between p-3 border rounded-lg'>
                    <div>
                      <p className='font-medium'>{tech.name}</p>
                      <p className='text-sm text-muted-foreground'>{tech.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* API文档和资源 */}
          <Card>
            <CardHeader>
              <CardTitle>API文档和资源</CardTitle>
              <CardDescription>
                相关文档和资源链接
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-4 border rounded-lg'>
                  <div className='flex items-center space-x-3'>
                    <Code className='h-5 w-5 text-blue-500' />
                    <div>
                      <p className='font-medium'>API文档</p>
                      <p className='text-sm text-muted-foreground'>详细的API接口文档和使用说明</p>
                    </div>
                  </div>
                  <ExternalLink className='h-4 w-4 text-muted-foreground' />
                </div>
                
                <div className='flex items-center justify-between p-4 border rounded-lg'>
                  <div className='flex items-center space-x-3'>
                    <Github className='h-5 w-5 text-gray-700' />
                    <div>
                      <p className='font-medium'>源代码</p>
                      <p className='text-sm text-muted-foreground'>项目的完整源代码仓库</p>
                    </div>
                  </div>
                  <ExternalLink className='h-4 w-4 text-muted-foreground' />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 版权信息 */}
          <Card>
            <CardContent className='pt-6'>
              <div className='text-center space-y-2'>
                <p className='text-sm text-muted-foreground'>
                  © 2024 IP Insight. All rights reserved.
                </p>
                <p className='text-xs text-muted-foreground'>
                  Built with ❤️ using modern web technologies
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}
