import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Clock, 
  Database, 
  Wifi,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { formatPercentage, formatUptime, formatTimestamp } from '@/utils/utils'
import type { MonitoringSummary } from '@/types/api'

interface SystemInfoProps {
  summary: MonitoringSummary | null
  loading: boolean
}

export function SystemInfo({ summary, loading }: SystemInfoProps) {
  if (loading) {
    return (
      <>
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className='h-5 w-24' />
              <Skeleton className='h-4 w-32' />
            </CardHeader>
            <CardContent className='space-y-3'>
              {Array.from({ length: 3 }).map((_, j) => (
                <div key={j} className='space-y-2'>
                  <div className='flex justify-between'>
                    <Skeleton className='h-4 w-16' />
                    <Skeleton className='h-4 w-12' />
                  </div>
                  <Skeleton className='h-2 w-full' />
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </>
    )
  }

  if (!summary) {
    return (
      <Card className='col-span-3'>
        <CardContent className='flex items-center justify-center h-32'>
          <p className='text-muted-foreground'>系统信息加载失败</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      {/* 系统信息 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Cpu className='h-5 w-5' />
            <span>系统信息</span>
          </CardTitle>
          <CardDescription>
            服务器性能和资源使用情况
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>CPU使用率</span>
              <span>{summary.system_load.cpu.toFixed(1)}%</span>
            </div>
            <Progress value={summary.system_load.cpu} className='h-2' />
          </div>
          
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>内存使用率</span>
              <span>{summary.system_load.memory.toFixed(1)}%</span>
            </div>
            <Progress value={summary.system_load.memory} className='h-2' />
          </div>
          
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>磁盘使用率</span>
              <span>{summary.system_load.disk.toFixed(1)}%</span>
            </div>
            <Progress value={summary.system_load.disk} className='h-2' />
          </div>
          
          <div className='pt-2 border-t'>
            <div className='flex items-center justify-between text-sm'>
              <div className='flex items-center space-x-2'>
                <Clock className='h-4 w-4 text-muted-foreground' />
                <span>运行时间</span>
              </div>
              <span className='text-muted-foreground'>
                {formatUptime(Date.now() / 1000 - summary.last_data_update)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 网络状态 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Wifi className='h-5 w-5' />
            <span>网络状态</span>
          </CardTitle>
          <CardDescription>
            API响应时间和连接状态
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <span className='text-sm'>API响应时间</span>
            <Badge variant='outline'>
              {summary.avg_response_time_24h.toFixed(0)}ms
            </Badge>
          </div>
          
          <div className='flex items-center justify-between'>
            <span className='text-sm'>24小时请求数</span>
            <Badge variant='secondary'>
              {summary.total_requests_24h.toLocaleString()}
            </Badge>
          </div>
          
          <div className='flex items-center justify-between'>
            <span className='text-sm'>错误率</span>
            <Badge variant={summary.error_rate_24h > 5 ? 'destructive' : 'outline'}>
              {summary.error_rate_24h.toFixed(2)}%
            </Badge>
          </div>
          
          <div className='flex items-center justify-between'>
            <span className='text-sm'>缓存命中率</span>
            <Badge variant='outline'>
              {summary.cache_hit_rate_24h.toFixed(1)}%
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* 数据源状态 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Database className='h-5 w-5' />
            <span>数据源状态</span>
          </CardTitle>
          <CardDescription>
            IP数据库同步和更新状态
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <span className='text-sm'>活跃数据源</span>
            <Badge variant='secondary'>
              {summary.active_data_sources}
            </Badge>
          </div>
          
          <div className='flex items-center justify-between'>
            <span className='text-sm'>最后更新</span>
            <span className='text-sm text-muted-foreground'>
              {formatTimestamp(summary.last_data_update)}
            </span>
          </div>
          
          <div className='flex items-center justify-between'>
            <span className='text-sm'>同步状态</span>
            <div className='flex items-center space-x-2'>
              <CheckCircle className='h-4 w-4 text-green-500' />
              <span className='text-sm'>正常</span>
            </div>
          </div>
          
          {/* 告警信息 */}
          {summary.alerts && summary.alerts.length > 0 && (
            <div className='pt-2 border-t'>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2 text-sm font-medium'>
                  <AlertTriangle className='h-4 w-4 text-yellow-500' />
                  <span>系统告警</span>
                </div>
                {summary.alerts.slice(0, 3).map((alert) => (
                  <div key={alert.id} className='text-xs text-muted-foreground'>
                    {alert.message}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
}
