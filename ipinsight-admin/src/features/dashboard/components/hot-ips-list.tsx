import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { formatNumber, getRelativeTime } from '@/utils/utils'
import type { HotIP } from '@/types/api'

interface HotIPsListProps {
  hotIPs: HotIP[]
  loading: boolean
}

export function HotIPsList({ hotIPs, loading }: HotIPsListProps) {
  if (loading) {
    return (
      <div className='space-y-4'>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className='flex items-center space-x-4'>
            <Skeleton className='h-9 w-9 rounded-full' />
            <div className='space-y-1'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-3 w-16' />
            </div>
            <div className='ml-auto'>
              <Skeleton className='h-5 w-12' />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!hotIPs || hotIPs.length === 0) {
    return (
      <div className='flex items-center justify-center h-32 text-muted-foreground'>
        <p>暂无热门IP数据</p>
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      {hotIPs.slice(0, 10).map((hotIP, index) => (
        <div key={hotIP.ip} className='flex items-center space-x-4'>
          <Avatar className='h-9 w-9'>
            <AvatarFallback className='text-xs font-medium'>
              #{index + 1}
            </AvatarFallback>
          </Avatar>
          <div className='min-w-0 flex-1'>
            <p className='text-sm font-medium leading-none truncate'>
              {hotIP.ip}
            </p>
            <p className='text-xs text-muted-foreground mt-1'>
              {hotIP.country && hotIP.city 
                ? `${hotIP.country} ${hotIP.city}`
                : '位置未知'
              }
            </p>
            <p className='text-xs text-muted-foreground'>
              最后查询: {getRelativeTime(new Date(hotIP.last_access).getTime() / 1000)}
            </p>
          </div>
          <div className='flex flex-col items-end space-y-1'>
            <Badge variant='secondary' className='text-xs'>
              {formatNumber(hotIP.query_count)}次
            </Badge>
            {hotIP.cache_hit && (
              <Badge variant='outline' className='text-xs'>
                缓存
              </Badge>
            )}
          </div>
        </div>
      ))}
      
      {hotIPs.length > 10 && (
        <div className='text-center pt-2'>
          <p className='text-xs text-muted-foreground'>
            还有 {hotIPs.length - 10} 个热门IP...
          </p>
        </div>
      )}
    </div>
  )
}
