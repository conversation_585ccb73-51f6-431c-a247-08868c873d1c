import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ConfigDrawer } from '@/components/config-drawer'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { 
  Cpu, 
  MemoryStick, 
  HardDrive, 
  Clock, 
  Server,
  Database,
  Zap,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react'
import { apiService } from '@/services/api'
import { formatUptime, formatPercentage, getStatusColor, getStatusBgColor } from '@/utils/utils'
import type { MonitoringMetrics, HealthStatus } from '@/types/api'

export function SystemStatus() {
  const [metrics, setMetrics] = useState<MonitoringMetrics | null>(null)
  const [health, setHealth] = useState<HealthStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  // 获取数据
  const fetchData = async () => {
    try {
      setLoading(true)
      const [metricsData, healthData] = await Promise.all([
        apiService.getMetrics(),
        apiService.getHealthStatus()
      ])
      
      setMetrics(metricsData)
      setHealth(healthData)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('获取系统状态失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
    // 每15秒自动刷新数据
    const interval = setInterval(fetchData, 15000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'active':
      case 'online':
        return <CheckCircle className='h-4 w-4 text-green-500' />
      case 'warning':
        return <AlertCircle className='h-4 w-4 text-yellow-500' />
      case 'error':
      case 'critical':
      case 'offline':
        return <XCircle className='h-4 w-4 text-red-500' />
      default:
        return <AlertCircle className='h-4 w-4 text-gray-500' />
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='flex items-center space-x-4'>
          <h1 className='text-lg font-semibold'>系统状态监控</h1>
          <Badge variant="outline" className='text-xs'>
            最后更新: {lastUpdate.toLocaleTimeString()}
          </Badge>
        </div>
        <div className='ms-auto flex items-center space-x-4'>
          <button
            onClick={fetchData}
            disabled={loading}
            className='flex items-center space-x-2 px-3 py-2 text-sm border rounded-md hover:bg-accent'
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
          <Search />
          <ThemeSwitch />
          <ConfigDrawer />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6'>
          <h2 className='text-2xl font-bold tracking-tight'>系统状态监控</h2>
          <p className='text-muted-foreground'>
            实时监控服务器性能、服务状态和系统资源使用情况
          </p>
        </div>

        <div className='space-y-6'>
          {/* 系统概览卡片 */}
          <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
            {/* CPU使用率 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>CPU使用率</CardTitle>
                <Cpu className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-2 w-full' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.system.cpu_usage.toFixed(1)}%
                    </div>
                    <Progress value={metrics?.system.cpu_usage || 0} className='h-2' />
                    <p className='text-xs text-muted-foreground'>
                      {metrics?.system.cpu_usage && metrics.system.cpu_usage > 80 
                        ? '使用率较高' 
                        : '运行正常'
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 内存使用率 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>内存使用率</CardTitle>
                <MemoryStick className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-2 w-full' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.system.memory_usage.toFixed(1)}%
                    </div>
                    <Progress value={metrics?.system.memory_usage || 0} className='h-2' />
                    <p className='text-xs text-muted-foreground'>
                      堆内存: {metrics?.system.heap_in_use ? 
                        (metrics.system.heap_in_use / 1024 / 1024).toFixed(0) + 'MB' : 
                        '--'
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 磁盘使用率 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>磁盘使用率</CardTitle>
                <HardDrive className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-2 w-full' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.system.disk_usage?.toFixed(1) || '0.0'}%
                    </div>
                    <Progress value={metrics?.system.disk_usage || 0} className='h-2' />
                    <p className='text-xs text-muted-foreground'>
                      存储空间监控
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 系统运行时间 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>系统运行时间</CardTitle>
                <Clock className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-24' />
                    <Skeleton className='h-3 w-16' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-lg font-bold'>
                      {metrics?.system.uptime ? formatUptime(metrics.system.uptime) : '--'}
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      Goroutines: {metrics?.system.goroutine_count || 0}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 服务状态表格 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Server className='h-5 w-5' />
                <span>服务状态</span>
              </CardTitle>
              <CardDescription>
                各个服务组件的运行状态和健康检查
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className='space-y-3'>
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className='flex items-center justify-between'>
                      <Skeleton className='h-4 w-24' />
                      <Skeleton className='h-6 w-16' />
                    </div>
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>服务名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>响应时间</TableHead>
                      <TableHead>最后检查</TableHead>
                      <TableHead>消息</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {health && Object.entries(health).map(([serviceName, healthCheck]) => (
                      <TableRow key={serviceName}>
                        <TableCell className='font-medium'>
                          <div className='flex items-center space-x-2'>
                            {getStatusIcon(healthCheck.status)}
                            <span>{healthCheck.service || serviceName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusBgColor(healthCheck.status)}>
                            {healthCheck.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {healthCheck.duration.toFixed(0)}ms
                        </TableCell>
                        <TableCell className='text-muted-foreground'>
                          {new Date(healthCheck.timestamp).toLocaleTimeString()}
                        </TableCell>
                        <TableCell className='text-muted-foreground'>
                          {healthCheck.message || '--'}
                        </TableCell>
                      </TableRow>
                    ))}
                    {(!health || Object.keys(health).length === 0) && (
                      <TableRow>
                        <TableCell colSpan={5} className='text-center text-muted-foreground'>
                          暂无服务状态数据
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* 性能指标面板 */}
          <div className='grid gap-6 lg:grid-cols-2'>
            {/* API性能指标 */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <Zap className='h-5 w-5' />
                  <span>API性能指标</span>
                </CardTitle>
                <CardDescription>
                  接口响应时间和请求统计
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-4'>
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className='flex justify-between'>
                        <Skeleton className='h-4 w-20' />
                        <Skeleton className='h-4 w-16' />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className='space-y-4'>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>总请求数</span>
                      <Badge variant='secondary'>
                        {metrics?.service.total_requests.toLocaleString() || 0}
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>成功请求数</span>
                      <Badge variant='outline'>
                        {metrics?.service.success_requests.toLocaleString() || 0}
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>平均响应时间</span>
                      <Badge variant='outline'>
                        {metrics?.service.avg_response_time.toFixed(0) || 0}ms
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>每秒请求数</span>
                      <Badge variant='outline'>
                        {metrics?.service.requests_per_second.toFixed(1) || 0}/s
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>活跃连接数</span>
                      <Badge variant='secondary'>
                        {metrics?.service.active_connections || 0}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 数据库性能指标 */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <Database className='h-5 w-5' />
                  <span>数据库性能</span>
                </CardTitle>
                <CardDescription>
                  数据库查询统计和连接池状态
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-4'>
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className='flex justify-between'>
                        <Skeleton className='h-4 w-20' />
                        <Skeleton className='h-4 w-16' />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className='space-y-4'>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>总查询数</span>
                      <Badge variant='secondary'>
                        {metrics?.database.total_queries.toLocaleString() || 0}
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>成功查询数</span>
                      <Badge variant='outline'>
                        {metrics?.database.success_queries.toLocaleString() || 0}
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>平均查询时间</span>
                      <Badge variant='outline'>
                        {metrics?.database.avg_query_time.toFixed(2) || 0}ms
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>活跃连接</span>
                      <Badge variant='secondary'>
                        {metrics?.database.active_connections || 0}
                      </Badge>
                    </div>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm'>连接池大小</span>
                      <Badge variant='outline'>
                        {metrics?.database.pool_size || 0}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </Main>
    </>
  )
}
