import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ConfigDrawer } from '@/components/config-drawer'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { 
  BarChart3, 
  TrendingUp, 
  Zap, 
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  Activity
} from 'lucide-react'
import { apiService } from '@/services/api'
import { formatNumber, formatResponseTime, getStatusColor } from '@/utils/utils'
import type { MonitoringMetrics, EndpointStat } from '@/types/api'

export function APIStats() {
  const [metrics, setMetrics] = useState<MonitoringMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  // 获取数据
  const fetchData = async () => {
    try {
      setLoading(true)
      const metricsData = await apiService.getMetrics()
      setMetrics(metricsData)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('获取API统计失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
    // 每30秒自动刷新数据
    const interval = setInterval(fetchData, 30000)
    return () => clearInterval(interval)
  }, [])

  // 获取端点性能状态
  const getPerformanceStatus = (avgDuration: number) => {
    if (avgDuration < 100) return { status: 'excellent', color: 'text-green-600', label: '优秀' }
    if (avgDuration < 500) return { status: 'good', color: 'text-blue-600', label: '良好' }
    if (avgDuration < 1000) return { status: 'warning', color: 'text-yellow-600', label: '需要关注' }
    return { status: 'poor', color: 'text-red-600', label: '需要优化' }
  }

  // 计算错误率
  const getErrorRate = (errorCount: number, totalCount: number) => {
    if (totalCount === 0) return 0
    return (errorCount / totalCount) * 100
  }

  // 获取端点统计数据
  const endpointStats = metrics?.api.endpoint_stats ? Object.entries(metrics.api.endpoint_stats) : []

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='flex items-center space-x-4'>
          <h1 className='text-lg font-semibold'>API统计中心</h1>
          <Badge variant="outline" className='text-xs'>
            最后更新: {lastUpdate.toLocaleTimeString()}
          </Badge>
        </div>
        <div className='ms-auto flex items-center space-x-4'>
          <button
            onClick={fetchData}
            disabled={loading}
            className='flex items-center space-x-2 px-3 py-2 text-sm border rounded-md hover:bg-accent'
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
          <Search />
          <ThemeSwitch />
          <ConfigDrawer />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6'>
          <h2 className='text-2xl font-bold tracking-tight'>API统计中心</h2>
          <p className='text-muted-foreground'>
            接口调用统计、性能分析和错误监控
          </p>
        </div>

        <div className='space-y-6'>
          {/* 统计卡片网格 */}
          <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-3'>
            {/* 总请求数 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>总请求数</CardTitle>
                <BarChart3 className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-20' />
                    <Skeleton className='h-3 w-16' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.service.total_requests ? formatNumber(metrics.service.total_requests) : '--'}
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      累计API调用次数
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 成功率 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>成功率</CardTitle>
                <CheckCircle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-3 w-20' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.service.total_requests ? 
                        ((metrics.service.success_requests / metrics.service.total_requests) * 100).toFixed(1) + '%' : 
                        '--'
                      }
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      请求成功率
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 平均响应时间 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>平均响应时间</CardTitle>
                <Clock className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-3 w-20' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.service.avg_response_time ? 
                        formatResponseTime(metrics.service.avg_response_time) : 
                        '--'
                      }
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      接口响应时间
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 错误率 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>错误率</CardTitle>
                <AlertTriangle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-3 w-16' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.service.total_requests ? 
                        ((metrics.service.error_requests / metrics.service.total_requests) * 100).toFixed(2) + '%' : 
                        '--'
                      }
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      请求错误率
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 每秒请求数 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>每秒请求数</CardTitle>
                <TrendingUp className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-3 w-16' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.service.requests_per_second ? 
                        metrics.service.requests_per_second.toFixed(1) : 
                        '--'
                      }
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      QPS (每秒查询数)
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 活跃连接数 */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>活跃连接数</CardTitle>
                <Activity className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='space-y-2'>
                    <Skeleton className='h-8 w-16' />
                    <Skeleton className='h-3 w-16' />
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <div className='text-2xl font-bold'>
                      {metrics?.service.active_connections || '--'}
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      当前活跃连接
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* API端点统计列表 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Zap className='h-5 w-5' />
                <span>API端点统计</span>
              </CardTitle>
              <CardDescription>
                各个API端点的详细性能统计和调用情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className='space-y-3'>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className='flex items-center justify-between'>
                      <div className='space-y-1'>
                        <Skeleton className='h-4 w-32' />
                        <Skeleton className='h-3 w-24' />
                      </div>
                      <div className='flex space-x-2'>
                        <Skeleton className='h-6 w-12' />
                        <Skeleton className='h-6 w-16' />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>端点</TableHead>
                      <TableHead>方法</TableHead>
                      <TableHead>请求数</TableHead>
                      <TableHead>错误数</TableHead>
                      <TableHead>错误率</TableHead>
                      <TableHead>平均响应时间</TableHead>
                      <TableHead>性能状态</TableHead>
                      <TableHead>最后访问</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {endpointStats.map(([endpoint, stat]) => {
                      const errorRate = getErrorRate(stat.error_count, stat.request_count)
                      const performance = getPerformanceStatus(stat.avg_duration)

                      return (
                        <TableRow key={endpoint}>
                          <TableCell className='font-medium'>
                            <code className='text-sm bg-muted px-1 py-0.5 rounded'>
                              {stat.path}
                            </code>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline' className='text-xs'>
                              {stat.method}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatNumber(stat.request_count)}
                          </TableCell>
                          <TableCell>
                            <span className={stat.error_count > 0 ? 'text-red-600' : ''}>
                              {formatNumber(stat.error_count)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={errorRate > 5 ? 'destructive' : errorRate > 1 ? 'secondary' : 'outline'}
                              className='text-xs'
                            >
                              {errorRate.toFixed(2)}%
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatResponseTime(stat.avg_duration)}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant='outline'
                              className={`text-xs ${performance.color}`}
                            >
                              {performance.label}
                            </Badge>
                          </TableCell>
                          <TableCell className='text-muted-foreground text-xs'>
                            {new Date(stat.last_access).toLocaleString()}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                    {endpointStats.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={8} className='text-center text-muted-foreground'>
                          暂无API端点统计数据
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* 性能趋势图表区域 */}
          <div className='grid gap-6 lg:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>请求量趋势</CardTitle>
                <CardDescription>
                  过去24小时的API请求量变化趋势
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='h-64 flex items-center justify-center text-muted-foreground'>
                  <p>图表组件待实现</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>响应时间趋势</CardTitle>
                <CardDescription>
                  过去24小时的平均响应时间变化
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='h-64 flex items-center justify-center text-muted-foreground'>
                  <p>图表组件待实现</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Main>
    </>
  )
}
