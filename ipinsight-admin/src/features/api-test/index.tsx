import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { ConfigDrawer } from '@/components/config-drawer'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { 
  Play, 
  Copy, 
  Download,
  Clock,
  CheckCircle,
  XCircle,
  Globe,
  Code,
  FileText
} from 'lucide-react'
import { toast } from 'sonner'
import { apiService } from '@/services/api'
import { copyToClipboard, isValidIP, formatResponseTime } from '@/utils/utils'
import type { IPQueryResponse, BatchQueryResponse } from '@/types/api'

interface TestResult {
  timestamp: number
  method: string
  url: string
  requestData?: any
  responseData?: any
  responseTime: number
  status: 'success' | 'error'
  error?: string
}

export function APITest() {
  // 单个IP查询
  const [singleIP, setSingleIP] = useState('*******')
  const [singleLoading, setSingleLoading] = useState(false)
  const [singleResult, setSingleResult] = useState<TestResult | null>(null)

  // 批量IP查询
  const [batchIPs, setBatchIPs] = useState('*******\n*******\n***************')
  const [batchLoading, setBatchLoading] = useState(false)
  const [batchResult, setBatchResult] = useState<TestResult | null>(null)

  // 测试历史
  const [testHistory, setTestHistory] = useState<TestResult[]>([])

  // 单个IP查询测试
  const testSingleIP = async () => {
    if (!isValidIP(singleIP.trim())) {
      toast.error('请输入有效的IP地址')
      return
    }

    setSingleLoading(true)
    const startTime = Date.now()

    try {
      const response = await apiService.queryIP(singleIP.trim())
      const endTime = Date.now()
      
      const result: TestResult = {
        timestamp: Date.now(),
        method: 'GET',
        url: `/api/v1/ip/${singleIP.trim()}`,
        responseData: response,
        responseTime: endTime - startTime,
        status: 'success'
      }
      
      setSingleResult(result)
      setTestHistory(prev => [result, ...prev.slice(0, 9)]) // 保留最近10条记录
      toast.success('IP查询成功')
    } catch (error) {
      const endTime = Date.now()
      const result: TestResult = {
        timestamp: Date.now(),
        method: 'GET',
        url: `/api/v1/ip/${singleIP.trim()}`,
        responseTime: endTime - startTime,
        status: 'error',
        error: error instanceof Error ? error.message : '未知错误'
      }
      
      setSingleResult(result)
      setTestHistory(prev => [result, ...prev.slice(0, 9)])
      toast.error('IP查询失败')
    } finally {
      setSingleLoading(false)
    }
  }

  // 批量IP查询测试
  const testBatchIPs = async () => {
    const ips = batchIPs.split('\n').map(ip => ip.trim()).filter(ip => ip)
    
    if (ips.length === 0) {
      toast.error('请输入至少一个IP地址')
      return
    }

    const invalidIPs = ips.filter(ip => !isValidIP(ip))
    if (invalidIPs.length > 0) {
      toast.error(`以下IP地址格式无效: ${invalidIPs.join(', ')}`)
      return
    }

    setBatchLoading(true)
    const startTime = Date.now()

    try {
      const response = await apiService.batchQueryIP({ ips })
      const endTime = Date.now()
      
      const result: TestResult = {
        timestamp: Date.now(),
        method: 'POST',
        url: '/api/v1/ip/batch',
        requestData: { ips },
        responseData: response,
        responseTime: endTime - startTime,
        status: 'success'
      }
      
      setBatchResult(result)
      setTestHistory(prev => [result, ...prev.slice(0, 9)])
      toast.success(`批量查询成功，处理了 ${response.total} 个IP地址`)
    } catch (error) {
      const endTime = Date.now()
      const result: TestResult = {
        timestamp: Date.now(),
        method: 'POST',
        url: '/api/v1/ip/batch',
        requestData: { ips },
        responseTime: endTime - startTime,
        status: 'error',
        error: error instanceof Error ? error.message : '未知错误'
      }
      
      setBatchResult(result)
      setTestHistory(prev => [result, ...prev.slice(0, 9)])
      toast.error('批量查询失败')
    } finally {
      setBatchLoading(false)
    }
  }

  // 复制结果到剪贴板
  const copyResult = async (result: TestResult) => {
    const text = JSON.stringify(result.responseData || result.error, null, 2)
    const success = await copyToClipboard(text)
    if (success) {
      toast.success('结果已复制到剪贴板')
    } else {
      toast.error('复制失败')
    }
  }

  // 下载结果为JSON文件
  const downloadResult = (result: TestResult) => {
    const data = result.responseData || { error: result.error }
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `api-test-result-${result.timestamp}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('结果已下载')
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='flex items-center space-x-4'>
          <h1 className='text-lg font-semibold'>API接口测试</h1>
        </div>
        <div className='ms-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ConfigDrawer />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6'>
          <h2 className='text-2xl font-bold tracking-tight'>API接口测试</h2>
          <p className='text-muted-foreground'>
            在线测试IP地理位置查询API接口
          </p>
        </div>

        <div className='space-y-6'>
          <Tabs defaultValue='single' className='w-full'>
            <TabsList className='grid w-full grid-cols-3'>
              <TabsTrigger value='single'>单个IP查询</TabsTrigger>
              <TabsTrigger value='batch'>批量IP查询</TabsTrigger>
              <TabsTrigger value='history'>测试历史</TabsTrigger>
            </TabsList>

            {/* 单个IP查询 */}
            <TabsContent value='single' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center space-x-2'>
                    <Globe className='h-5 w-5' />
                    <span>单个IP地址查询</span>
                  </CardTitle>
                  <CardDescription>
                    输入单个IP地址进行地理位置查询测试
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='single-ip'>IP地址</Label>
                    <div className='flex space-x-2'>
                      <Input
                        id='single-ip'
                        placeholder='请输入IP地址，例如: *******'
                        value={singleIP}
                        onChange={(e) => setSingleIP(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && testSingleIP()}
                      />
                      <Button 
                        onClick={testSingleIP}
                        disabled={singleLoading}
                        className='flex items-center space-x-2'
                      >
                        <Play className='h-4 w-4' />
                        <span>{singleLoading ? '查询中...' : '测试'}</span>
                      </Button>
                    </div>
                  </div>

                  {singleResult && (
                    <div className='space-y-4'>
                      <div className='flex items-center justify-between'>
                        <h4 className='font-medium'>测试结果</h4>
                        <div className='flex items-center space-x-2'>
                          <Badge variant={singleResult.status === 'success' ? 'default' : 'destructive'}>
                            {singleResult.status === 'success' ? (
                              <CheckCircle className='h-3 w-3 mr-1' />
                            ) : (
                              <XCircle className='h-3 w-3 mr-1' />
                            )}
                            {singleResult.status}
                          </Badge>
                          <Badge variant='outline'>
                            <Clock className='h-3 w-3 mr-1' />
                            {formatResponseTime(singleResult.responseTime)}
                          </Badge>
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => copyResult(singleResult)}
                          >
                            <Copy className='h-3 w-3' />
                          </Button>
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => downloadResult(singleResult)}
                          >
                            <Download className='h-3 w-3' />
                          </Button>
                        </div>
                      </div>
                      
                      <div className='bg-muted p-4 rounded-lg'>
                        <pre className='text-sm overflow-auto max-h-96'>
                          {JSON.stringify(singleResult.responseData || singleResult.error, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 批量IP查询 */}
            <TabsContent value='batch' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center space-x-2'>
                    <Code className='h-5 w-5' />
                    <span>批量IP地址查询</span>
                  </CardTitle>
                  <CardDescription>
                    输入多个IP地址进行批量地理位置查询测试
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='batch-ips'>IP地址列表（每行一个）</Label>
                    <Textarea
                      id='batch-ips'
                      placeholder='请输入IP地址，每行一个，例如:&#10;*******&#10;*******&#10;***************'
                      value={batchIPs}
                      onChange={(e) => setBatchIPs(e.target.value)}
                      rows={6}
                    />
                    <div className='flex justify-between items-center'>
                      <p className='text-sm text-muted-foreground'>
                        共 {batchIPs.split('\n').filter(ip => ip.trim()).length} 个IP地址
                      </p>
                      <Button
                        onClick={testBatchIPs}
                        disabled={batchLoading}
                        className='flex items-center space-x-2'
                      >
                        <Play className='h-4 w-4' />
                        <span>{batchLoading ? '查询中...' : '批量测试'}</span>
                      </Button>
                    </div>
                  </div>

                  {batchResult && (
                    <div className='space-y-4'>
                      <div className='flex items-center justify-between'>
                        <h4 className='font-medium'>批量测试结果</h4>
                        <div className='flex items-center space-x-2'>
                          <Badge variant={batchResult.status === 'success' ? 'default' : 'destructive'}>
                            {batchResult.status === 'success' ? (
                              <CheckCircle className='h-3 w-3 mr-1' />
                            ) : (
                              <XCircle className='h-3 w-3 mr-1' />
                            )}
                            {batchResult.status}
                          </Badge>
                          <Badge variant='outline'>
                            <Clock className='h-3 w-3 mr-1' />
                            {formatResponseTime(batchResult.responseTime)}
                          </Badge>
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => copyResult(batchResult)}
                          >
                            <Copy className='h-3 w-3' />
                          </Button>
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => downloadResult(batchResult)}
                          >
                            <Download className='h-3 w-3' />
                          </Button>
                        </div>
                      </div>

                      {batchResult.status === 'success' && batchResult.responseData && (
                        <div className='grid gap-4 sm:grid-cols-3'>
                          <Card>
                            <CardContent className='pt-4'>
                              <div className='text-2xl font-bold'>
                                {(batchResult.responseData as BatchQueryResponse).total}
                              </div>
                              <p className='text-xs text-muted-foreground'>总处理数</p>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className='pt-4'>
                              <div className='text-2xl font-bold text-green-600'>
                                {(batchResult.responseData as BatchQueryResponse).success}
                              </div>
                              <p className='text-xs text-muted-foreground'>成功数</p>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className='pt-4'>
                              <div className='text-2xl font-bold text-red-600'>
                                {(batchResult.responseData as BatchQueryResponse).failed}
                              </div>
                              <p className='text-xs text-muted-foreground'>失败数</p>
                            </CardContent>
                          </Card>
                        </div>
                      )}

                      <div className='bg-muted p-4 rounded-lg'>
                        <pre className='text-sm overflow-auto max-h-96'>
                          {JSON.stringify(batchResult.responseData || batchResult.error, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 测试历史 */}
            <TabsContent value='history' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center space-x-2'>
                    <FileText className='h-5 w-5' />
                    <span>测试历史</span>
                  </CardTitle>
                  <CardDescription>
                    最近的API测试记录
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {testHistory.length === 0 ? (
                    <div className='text-center py-8 text-muted-foreground'>
                      <FileText className='h-12 w-12 mx-auto mb-4 opacity-50' />
                      <p>暂无测试历史记录</p>
                      <p className='text-sm'>开始测试API接口后，历史记录将显示在这里</p>
                    </div>
                  ) : (
                    <div className='space-y-4'>
                      {testHistory.map((result, index) => (
                        <div key={result.timestamp} className='border rounded-lg p-4'>
                          <div className='flex items-center justify-between mb-2'>
                            <div className='flex items-center space-x-2'>
                              <Badge variant='outline'>{result.method}</Badge>
                              <code className='text-sm bg-muted px-2 py-1 rounded'>
                                {result.url}
                              </code>
                              <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                                {result.status === 'success' ? (
                                  <CheckCircle className='h-3 w-3 mr-1' />
                                ) : (
                                  <XCircle className='h-3 w-3 mr-1' />
                                )}
                                {result.status}
                              </Badge>
                              <Badge variant='outline'>
                                <Clock className='h-3 w-3 mr-1' />
                                {formatResponseTime(result.responseTime)}
                              </Badge>
                            </div>
                            <div className='flex items-center space-x-2'>
                              <span className='text-sm text-muted-foreground'>
                                {new Date(result.timestamp).toLocaleString()}
                              </span>
                              <Button
                                size='sm'
                                variant='outline'
                                onClick={() => copyResult(result)}
                              >
                                <Copy className='h-3 w-3' />
                              </Button>
                              <Button
                                size='sm'
                                variant='outline'
                                onClick={() => downloadResult(result)}
                              >
                                <Download className='h-3 w-3' />
                              </Button>
                            </div>
                          </div>

                          {result.requestData && (
                            <div className='mb-2'>
                              <p className='text-sm font-medium mb-1'>请求数据:</p>
                              <div className='bg-muted p-2 rounded text-xs'>
                                <pre>{JSON.stringify(result.requestData, null, 2)}</pre>
                              </div>
                            </div>
                          )}

                          <div>
                            <p className='text-sm font-medium mb-1'>
                              {result.status === 'success' ? '响应数据:' : '错误信息:'}
                            </p>
                            <div className='bg-muted p-2 rounded text-xs max-h-32 overflow-auto'>
                              <pre>{JSON.stringify(result.responseData || result.error, null, 2)}</pre>
                            </div>
                          </div>
                        </div>
                      ))}

                      {testHistory.length >= 10 && (
                        <p className='text-center text-sm text-muted-foreground'>
                          只显示最近10条测试记录
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </Main>
    </>
  )
}
