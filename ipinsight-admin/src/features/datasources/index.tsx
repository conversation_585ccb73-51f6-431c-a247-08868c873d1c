import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ConfigDrawer } from '@/components/config-drawer'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { 
  Database, 
  RefreshCw, 
  Download,
  CheckCircle,
  AlertCircle,
  XCircle,
  Clock,
  HardDrive,
  Calendar
} from 'lucide-react'
import { toast } from 'sonner'
import { apiService } from '@/services/api'
import { formatFileSize, formatTimestamp, getStatusColor, getStatusBgColor } from '@/utils/utils'
import type { DataSourceStatus, DataSourceUpdateResult } from '@/types/api'

export function DataSources() {
  const [dataSources, setDataSources] = useState<DataSourceStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false)

  // 获取数据源状态
  const fetchDataSources = async () => {
    try {
      setLoading(true)
      const data = await apiService.getDataSources()
      setDataSources(data)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('获取数据源状态失败:', error)
      toast.error('获取数据源状态失败')
    } finally {
      setLoading(false)
    }
  }

  // 更新数据源
  const updateDataSources = async (sources?: string[], force = false) => {
    try {
      setUpdating(true)
      const results = await apiService.updateDataSources({
        sources: sources || selectedSources,
        force
      })
      
      // 显示更新结果
      results.forEach((result) => {
        if (result.success) {
          toast.success(`${result.source}: ${result.message}`)
        } else {
          toast.error(`${result.source}: ${result.message}`)
        }
      })
      
      // 刷新数据源状态
      await fetchDataSources()
      setSelectedSources([])
      setUpdateDialogOpen(false)
    } catch (error) {
      console.error('更新数据源失败:', error)
      toast.error('更新数据源失败')
    } finally {
      setUpdating(false)
    }
  }

  // 处理选择变化
  const handleSelectChange = (sourceName: string, checked: boolean) => {
    if (checked) {
      setSelectedSources(prev => [...prev, sourceName])
    } else {
      setSelectedSources(prev => prev.filter(name => name !== sourceName))
    }
  }

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSources(dataSources.map(ds => ds.name))
    } else {
      setSelectedSources([])
    }
  }

  useEffect(() => {
    fetchDataSources()
    // 每60秒自动刷新数据
    const interval = setInterval(fetchDataSources, 60000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <CheckCircle className='h-4 w-4 text-green-500' />
      case 'updating':
        return <RefreshCw className='h-4 w-4 text-blue-500 animate-spin' />
      case 'error':
        return <XCircle className='h-4 w-4 text-red-500' />
      case 'inactive':
        return <AlertCircle className='h-4 w-4 text-gray-500' />
      default:
        return <AlertCircle className='h-4 w-4 text-gray-500' />
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='flex items-center space-x-4'>
          <h1 className='text-lg font-semibold'>数据源管理</h1>
          <Badge variant="outline" className='text-xs'>
            最后更新: {lastUpdate.toLocaleTimeString()}
          </Badge>
        </div>
        <div className='ms-auto flex items-center space-x-4'>
          <button
            onClick={fetchDataSources}
            disabled={loading}
            className='flex items-center space-x-2 px-3 py-2 text-sm border rounded-md hover:bg-accent'
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
          <Search />
          <ThemeSwitch />
          <ConfigDrawer />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6'>
          <h2 className='text-2xl font-bold tracking-tight'>数据源管理</h2>
          <p className='text-muted-foreground'>
            管理IP地理位置数据库的同步和更新状态
          </p>
        </div>

        <div className='space-y-6'>
          {/* 操作按钮区域 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Database className='h-5 w-5' />
                <span>数据源操作</span>
              </CardTitle>
              <CardDescription>
                选择数据源进行批量更新操作
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex items-center space-x-4'>
                <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      disabled={selectedSources.length === 0 || updating}
                      className='flex items-center space-x-2'
                    >
                      <Download className='h-4 w-4' />
                      <span>更新选中数据源 ({selectedSources.length})</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>确认更新数据源</DialogTitle>
                      <DialogDescription>
                        即将更新以下数据源，此操作可能需要几分钟时间：
                        <ul className='mt-2 list-disc list-inside'>
                          {selectedSources.map(source => (
                            <li key={source} className='text-sm'>{source}</li>
                          ))}
                        </ul>
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button 
                        variant='outline' 
                        onClick={() => setUpdateDialogOpen(false)}
                        disabled={updating}
                      >
                        取消
                      </Button>
                      <Button 
                        onClick={() => updateDataSources(selectedSources, false)}
                        disabled={updating}
                      >
                        {updating ? (
                          <>
                            <RefreshCw className='h-4 w-4 animate-spin mr-2' />
                            更新中...
                          </>
                        ) : (
                          '确认更新'
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Button 
                  variant='outline'
                  onClick={() => updateDataSources([], true)}
                  disabled={updating}
                  className='flex items-center space-x-2'
                >
                  <RefreshCw className={`h-4 w-4 ${updating ? 'animate-spin' : ''}`} />
                  <span>强制更新全部</span>
                </Button>

                <div className='text-sm text-muted-foreground'>
                  已选择 {selectedSources.length} / {dataSources.length} 个数据源
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据源状态表格 */}
          <Card>
            <CardHeader>
              <CardTitle>数据源状态</CardTitle>
              <CardDescription>
                各个IP地理位置数据库的详细状态信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className='space-y-3'>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className='flex items-center space-x-4'>
                      <Skeleton className='h-4 w-4' />
                      <Skeleton className='h-4 w-32' />
                      <Skeleton className='h-6 w-16' />
                      <Skeleton className='h-4 w-24' />
                    </div>
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className='w-12'>
                        <Checkbox
                          checked={selectedSources.length === dataSources.length && dataSources.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>数据源名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>记录数</TableHead>
                      <TableHead>文件大小</TableHead>
                      <TableHead>最后更新</TableHead>
                      <TableHead>下次更新</TableHead>
                      <TableHead>错误信息</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dataSources.map((dataSource) => (
                      <TableRow key={dataSource.name}>
                        <TableCell>
                          <Checkbox
                            checked={selectedSources.includes(dataSource.name)}
                            onCheckedChange={(checked) =>
                              handleSelectChange(dataSource.name, checked as boolean)
                            }
                          />
                        </TableCell>
                        <TableCell className='font-medium'>
                          <div className='flex items-center space-x-2'>
                            {getStatusIcon(dataSource.status)}
                            <span>{dataSource.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusBgColor(dataSource.status)}>
                            {dataSource.enabled ? dataSource.status : 'disabled'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {dataSource.records_count ?
                            dataSource.records_count.toLocaleString() :
                            '--'
                          }
                        </TableCell>
                        <TableCell>
                          {dataSource.size ?
                            formatFileSize(dataSource.size) :
                            '--'
                          }
                        </TableCell>
                        <TableCell className='text-muted-foreground'>
                          {dataSource.last_update ?
                            formatTimestamp(dataSource.last_update) :
                            '--'
                          }
                        </TableCell>
                        <TableCell className='text-muted-foreground'>
                          {dataSource.next_update ?
                            formatTimestamp(dataSource.next_update) :
                            '--'
                          }
                        </TableCell>
                        <TableCell>
                          {dataSource.error ? (
                            <span className='text-red-600 text-xs'>
                              {dataSource.error}
                            </span>
                          ) : (
                            <span className='text-muted-foreground'>--</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                    {dataSources.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={8} className='text-center text-muted-foreground'>
                          暂无数据源信息
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* 数据源统计信息 */}
          <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>总数据源</CardTitle>
                <Database className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{dataSources.length}</div>
                <p className='text-xs text-muted-foreground'>
                  已配置的数据源数量
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>活跃数据源</CardTitle>
                <CheckCircle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {dataSources.filter(ds => ds.status === 'active').length}
                </div>
                <p className='text-xs text-muted-foreground'>
                  正常运行的数据源
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>总记录数</CardTitle>
                <HardDrive className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {dataSources.reduce((sum, ds) => sum + (ds.records_count || 0), 0).toLocaleString()}
                </div>
                <p className='text-xs text-muted-foreground'>
                  所有数据源的记录总数
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>错误数据源</CardTitle>
                <XCircle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {dataSources.filter(ds => ds.status === 'error').length}
                </div>
                <p className='text-xs text-muted-foreground'>
                  需要处理的错误数据源
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </Main>
    </>
  )
}
