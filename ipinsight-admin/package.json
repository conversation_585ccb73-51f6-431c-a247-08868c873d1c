{"name": "shadcn-admin", "private": false, "version": "2.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format:check": "prettier --check .", "format": "prettier --write .", "knip": "knip"}, "dependencies": {"@clerk/clerk-react": "^5.42.1", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-direction": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-query": "^5.85.3", "@tanstack/react-router": "^1.131.16", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-day-picker": "9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-top-loading-bar": "^3.0.2", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.6", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@faker-js/faker": "^9.9.0", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/react-query-devtools": "^5.85.3", "@tanstack/react-router-devtools": "^1.131.16", "@tanstack/router-plugin": "^1.131.16", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "knip": "^5.62.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "~5.9.2", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}