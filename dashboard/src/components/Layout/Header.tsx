import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { TooltipProvider } from '@/components/ui/tooltip'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Menu as MenuIcon,
  User as AccountCircle,
  Logout,
  Person,

  ChevronLeft,
  ChevronRight,
  Wifi,
} from '@/components/ui/icons'
import { useAuth } from '../../contexts/AuthContext'
import { useMonitoringData } from '../../hooks/useApi'
import { apiService } from '../../services/api'

import { cn } from '@/lib/utils'

interface HeaderProps {
  drawerWidth: number
  onMenuClick: () => void
  collapsed?: boolean
  onToggleCollapse?: () => void
}

const Header: React.FC<HeaderProps> = ({
  drawerWidth,
  onMenuClick,
  collapsed = false,
  onToggleCollapse,
}) => {
  const { logout } = useAuth()
  const [showProfile, setShowProfile] = useState(false)

  // 获取系统状态用于显示健康状态
  const { data: systemStatus } = useMonitoringData(
    () => apiService.getSystemStatus(),
    30000 // 30秒刷新一次
  )

  const handleLogout = () => {
    logout()
  }

  const handleShowProfile = () => {
    setShowProfile(true)
  }

  return (
    <TooltipProvider>
      {/* 现代化顶部导航栏 */}
      <div className="bg-background border-b border-border sticky top-0 z-50">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* 左侧：菜单控制 */}
            <div className="flex items-center space-x-4 flex-1">
              {/* 移动端菜单按钮 */}
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={onMenuClick}
                aria-label="打开菜单"
              >
                <MenuIcon className="h-5 w-5" />
              </Button>

              {/* 桌面端折叠按钮 */}
              {drawerWidth > 0 && onToggleCollapse && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="hidden md:flex"
                  onClick={onToggleCollapse}
                  aria-label="切换侧边栏"
                >
                  {collapsed ? (
                    <ChevronRight className="h-5 w-5" />
                  ) : (
                    <ChevronLeft className="h-5 w-5" />
                  )}
                </Button>
              )}
            </div>

            {/* 右侧：状态和用户菜单 */}
            <div className="flex items-center space-x-3">
              {/* 系统状态指示器 */}
              {systemStatus && (
                <Badge
                  variant={systemStatus.status === 'ok' ? 'success' : 'destructive'}
                  className="animate-pulse"
                >
                  <div className="mr-1 h-2 w-2 rounded-full bg-current" />
                  {systemStatus.status === 'ok' ? '系统正常' : '系统异常'}
                </Badge>
              )}

              <Button variant="ghost" size="sm">
                <Wifi className="h-4 w-4 mr-2" />
                网络状态
              </Button>

              {/* 用户菜单 */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback>
                        <AccountCircle className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem onClick={handleShowProfile}>
                    <Person className="mr-2 h-4 w-4" />
                    用户资料
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-destructive">
                    <Logout className="mr-2 h-4 w-4" />
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* 用户资料对话框 */}
      <Dialog open={showProfile} onOpenChange={setShowProfile}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>用户资料</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <div className="bg-muted rounded-lg p-6">
              <p className="text-muted-foreground">用户资料功能正在迁移中...</p>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowProfile(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  )
}

export default Header