import { useState } from 'react'
import { But<PERSON> } from './ui/button'
import { RefreshCw } from '@/components/ui/icons'
type RefreshButtonProps = {
  handleRefreshAll: () => void
}
const RefreshButton = ({ handleRefreshAll }: RefreshButtonProps) => {
  const [refreshing, setRefreshing] = useState(false)
  const handleRefresh = async () => {
    setRefreshing(true)
    await handleRefreshAll()
    await new Promise((resolve) => setTimeout(resolve, 200))
    setRefreshing(false)
  }
  return (
    <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
      <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
      刷新数据
    </Button>
  )
}

export default RefreshButton