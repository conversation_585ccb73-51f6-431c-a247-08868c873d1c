import React, { useCallback, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/spinner'
import { Progress } from '@/components/ui/progress'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Storage,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Database,
  Pause,
  Info,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../hooks/useToast'
import { formatBytes, formatRelativeTime } from '@/lib/utils'

const DataSourcesPage: React.FC = () => {
  const { showError, showSuccess } = useToast()
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false)
  const [selectedSource, setSelectedSource] = useState<string | null>(null)
  const [updating, setUpdating] = useState(false)

  // API调用函数
  const getDataSources = useCallback(() => apiService.getDataSources(), [])

  // 获取数据源状态
  const {
    data: dataSources,
    loading: sourcesLoading,
    error: sourcesError,
    refetch: refetchSources,
  } = useMonitoringData(getDataSources, 30000)

  // 处理错误
  React.useEffect(() => {
    if (sourcesError) showError('获取数据源状态失败: ' + sourcesError)
  }, [sourcesError, showError])

  // 使用真实的数据源数据
  const sources = dataSources || []

  // 获取状态颜色和文本
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'active':
        return { variant: 'success' as const, text: '正常', icon: CheckCircle }
      case 'updating':
        return { variant: 'warning' as const, text: '更新中', icon: RefreshCw }
      case 'error':
        return { variant: 'destructive' as const, text: '错误', icon: AlertTriangle }
      case 'disabled':
        return { variant: 'secondary' as const, text: '已禁用', icon: Pause }
      default:
        return { variant: 'secondary' as const, text: '未知', icon: Info }
    }
  }

  // 手动更新数据源
  const handleUpdateSource = async (sourceId: string) => {
    setUpdating(true)
    try {
      // 调用真实的后端API
      await apiService.fetchDataSource([sourceId])
      showSuccess(`数据源 ${sourceId} 更新成功`)
      refetchSources()
    } catch (error) {
      showError(`更新数据源失败: ${error.message || error}`)
    } finally {
      setUpdating(false)
      setUpdateDialogOpen(false)
      setSelectedSource(null)
    }
  }

  // 统计信息
  const stats = {
    total: sources.length,
    active: sources.filter((s) => s.status === 'active').length,
    updating: sources.filter((s) => s.status === 'updating').length,
    error: sources.filter((s) => s.status === 'error').length,
    totalSize: sources.reduce((sum, s) => sum + (s.size || 0), 0),
    totalRecords: sources.reduce((sum, s) => sum + (s.records || 0), 0),
  }

  return (
    <div className="min-h-full bg-slate-50 -m-4 sm:-m-6 lg:-m-8 p-4 sm:p-6 lg:p-8">
      {/* 页面内容 */}
      <div className="max-w-7xl mx-auto">
        {/* 页面标题区域 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div>
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-600 mb-2">
                数据源管理
              </h1>
              <p className="text-gray-600">管理和监控IP地理位置数据源</p>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={refetchSources}
                size="sm"
                className="rounded-full text-black shadow-lg hover:shadow-xl transition-all duration-200"
                variant='ghost'
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 统计概览 - 2x2网格布局 */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 mb-8">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-primary/10">
              <Database className="h-5 w-5 text-primary" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">数据源概览</h2>
          </div>
          <div>
            <div className="grid grid-cols-2 gap-4 sm:gap-6">
              {/* 总数据源 */}
              <div className="flex items-center gap-3 p-4 bg-pink-50 rounded-xl border border-pink-200 hover:border-pink-300 transition-colors">
                <div className="p-2 rounded-xl bg-pink-100">
                  <Storage className="h-5 w-5 text-pink-600" />
                </div>
                <div>
                  <div className="text-xl font-bold text-pink-600">{stats.total}</div>
                  <div className="text-sm text-gray-600">总数据源</div>
                </div>
              </div>

              {/* 活跃数据源 */}
              <div className="flex items-center gap-3 p-4 bg-green-50 rounded-xl border border-green-200 hover:border-green-300 transition-colors">
                <div className="p-2 rounded-xl bg-green-100">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div className="text-xl font-bold text-green-600">{stats.active}</div>
                  <div className="text-sm text-gray-600">活跃数据源</div>
                </div>
              </div>

              {/* 异常数据源 */}
              <div className="flex items-center gap-3 p-4 bg-red-50 rounded-xl border border-red-200 hover:border-red-300 transition-colors">
                <div className="p-2 rounded-xl bg-red-100">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <div className="text-xl font-bold text-red-600">{stats.error}</div>
                  <div className="text-sm text-gray-600">异常数据源</div>
                </div>
              </div>

              {/* 总数据大小 */}
              <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-xl border border-purple-200 hover:border-purple-300 transition-colors">
                <div className="p-2 rounded-xl bg-purple-100">
                  <Database className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <div className="text-xl font-bold text-purple-600">
                    {formatBytes(stats.totalSize)}
                  </div>
                  <div className="text-sm text-gray-600">总数据大小</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 数据源列表 */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-purple-100">
              <Storage className="h-5 w-5 text-purple-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">数据源状态详情</h2>
          </div>
          <div>
            {sourcesLoading ? (
              <div className="flex items-center justify-center h-64">
                <Spinner size="lg" text="加载数据源状态..." />
              </div>
            ) : sourcesError ? (
              <Alert variant="destructive">
                <AlertDescription>加载数据源失败: {sourcesError}</AlertDescription>
              </Alert>
            ) : (
              <div className="rounded-md text-gray-600">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>数据源</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>大小</TableHead>
                      <TableHead>记录数</TableHead>
                      <TableHead>最后更新</TableHead>
                      {/* <TableHead>下次更新</TableHead> */}
                      <TableHead className="text-center">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sources.map((source) => {
                      const statusInfo = getStatusInfo(source.status)
                      const StatusIcon = statusInfo.icon

                      return (
                        <TableRow key={source.name}>
                          <TableCell>
                            <div>
                              <div className="font-semibold">{source.name}</div>
                              <div className="text-xs text-muted-foreground">
                                数据源: {source.name}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">数据库</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <StatusIcon className="h-4 w-4" />
                              <Badge variant={statusInfo.variant}>{statusInfo.text}</Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            {formatBytes((source as { size?: number }).size || 0)}
                          </TableCell>
                          <TableCell>{(source.records_count || 0).toLocaleString()}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatRelativeTime(
                                new Date(source.last_update * 1000).toISOString()
                              )}
                            </div>
                          </TableCell>
                          {/* <TableCell>
                            <div className="text-sm">
                              {formatRelativeTime(
                                new Date(source.next_update * 1000).toISOString()
                              )}
                            </div>
                          </TableCell> */}
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedSource(source.name)
                                  setUpdateDialogOpen(true)
                                }}
                                disabled={source.status === 'updating'}
                              >
                                <RefreshCw className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </div>

        {/* 更新进度 */}
        {stats.updating > 0 && (
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 mt-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-success/10">
                <RefreshCw className="h-5 w-5 text-success animate-spin" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">更新进度</h2>
            </div>
            <div>
              <div className="space-y-4">
                {sources
                  .filter((s) => s.status === 'updating')
                  .map((source) => (
                    <div key={source.name} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{source.name}</span>
                        <span>更新中...</span>
                      </div>
                      <Progress value={65} className="h-2" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {/* 更新确认对话框 */}
        <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
          <DialogContent className="bg-green-50 border-none">
            <DialogHeader>
              <DialogTitle>确认更新数据源</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p>确定要更新数据源 "{selectedSource}" 吗？</p>
              <p className="text-sm text-muted-foreground mt-2">
                更新过程可能需要几分钟时间，期间该数据源将暂时不可用。
              </p>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setUpdateDialogOpen(false)}
                disabled={updating}
              >
                取消
              </Button>
              <Button
                onClick={() => selectedSource && handleUpdateSource(selectedSource)}
                disabled={updating}
              >
                {updating ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    更新中...
                  </>
                ) : (
                  '确认更新'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}

export default DataSourcesPage