import React, { useCallback } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/spinner'
import {
  TrendingUp,
  TrendingDown,
  CheckCircle,
  Globe as Public,
  Wifi as NetworkCheck,
  Users,
  Activity,
  Server,
  BarChart3,
  RefreshCw,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../hooks/useToast'
import { formatNumber } from '@/lib/utils'
import { cn } from '@/lib/utils'



const DashboardPage: React.FC = () => {
  const { showError } = useToast()

  // 使用 useCallback 来稳定 API 调用函数
  const getQueryStats = useCallback(() => apiService.getQueryStats(), [])
  const getHotIPs = useCallback(() => apiService.getHotIPs(), [])
  const getHotIPStats = useCallback(() => apiService.getHotIPStats(), [])
  const getBatchStats = useCallback(() => apiService.getBatchStats(), [])
  const getSystemStatus = useCallback(() => apiService.getSystemStatus(), [])
  const getMetrics = useCallback(() => apiService.getMetrics(), [])

  // 获取监控数据
  const {
    data: queryStats,
    loading: queryLoading,
    error: queryError,
    refetch: refetchQueryStats,
  } = useMonitoringData(getQueryStats, 30000)
  console.log('queryStats:', queryStats)
  const {
    data: hotIPs,
    loading: hotIPsLoading,
    error: hotIPsError,
    refetch: refetchHotIPs,
  } = useMonitoringData(getHotIPs, 60000)
  console.log('hotIPs:', hotIPs)
  const {
    data: hotIPStats,
    loading: hotIPStatsLoading,
    error: hotIPStatsError,
    refetch: refetchHotIPStats,
  } = useMonitoringData(getHotIPStats, 30000)

  const {
    data: batchStats,
    error: batchError,
  } = useMonitoringData(getBatchStats, 30000)
  console.log('batchStats:', batchStats)
  const {
    data: systemStatus,
    loading: systemStatusLoading,
    error: systemStatusError,
  } = useMonitoringData(getSystemStatus, 30000)
  console.log('systemStatus:', systemStatus)
  const {
    data: metrics,
    loading: metricsLoading,
    error: metricsError,
  } = useMonitoringData(getMetrics, 30000)
  console.log('metrics:', metrics)
  // 处理错误
  React.useEffect(() => {
    if (queryError) showError('获取查询统计失败: ' + queryError)
    if (hotIPsError) showError('获取热门IP失败: ' + hotIPsError)
    if (hotIPStatsError) showError('获取IP统计失败: ' + hotIPStatsError)
    if (batchError) showError('获取批量统计失败: ' + batchError)
    if (systemStatusError) showError('获取系统状态失败: ' + systemStatusError)
    if (metricsError) showError('获取监控指标失败: ' + metricsError)
  }, [
    queryError,
    hotIPsError,
    hotIPStatsError,
    batchError,
    systemStatusError,
    metricsError,
    showError,
  ])

  // 刷新所有数据
  const handleRefreshAll = () => {
    refetchQueryStats()
    refetchHotIPs()
    refetchHotIPStats()
    // 注意：useMonitoringData hook可能没有返回refetch函数，这里先注释
    // refetchSystemStatus?.()
    // refetchMetrics?.()
  }

  // 统计卡片数据
  const statsCards = [
    {
      title: '总查询次数',
      value: queryStats?.total_queries || 0,
      icon: <Activity className="h-5 w-5" />,
      trend: '+15.2%',
      trendUp: true,
      loading: queryLoading,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    },
    {
      title: '缓存命中率',
      value: `${(queryStats?.cache_hit_rate || 0).toFixed(1)}%`,
      icon: <TrendingUp className="h-5 w-5" />,
      trend: '+8.7%',
      trendUp: true,
      loading: queryLoading,
      color: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-950/20',
    },
    {
      title: '活跃IP数',
      value: hotIPStats?.hot_ips_count || hotIPs?.length || 0,
      icon: <Public className="h-5 w-5" />,
      trend: '+12.4%',
      trendUp: true,
      loading: hotIPStatsLoading,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50 dark:bg-purple-950/20',
    },
    {
      title: '系统状态',
      value: systemStatus?.status === 'healthy' ? '正常' : systemStatus?.status || '未知',
      icon: <CheckCircle className="h-5 w-5" />,
      trend: '+0.3%',
      trendUp: true,
      loading: systemStatusLoading,
      color: systemStatus?.status === 'healthy' ? 'text-green-500' : 'text-yellow-500',
      bgColor: systemStatus?.status === 'healthy' ? 'bg-green-50 dark:bg-green-950/20' : 'bg-yellow-50 dark:bg-yellow-950/20',
    },
  ]

  return (
    <div className="min-h-full bg-slate-50 -m-4 sm:-m-6 lg:-m-8 p-4 sm:p-6 lg:p-8">
      {/* 页面内容 */}
      <div className="max-w-7xl mx-auto">
        {/* 页面标题区域 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div>
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-600 mb-2">
                系统总览
              </h1>
              <p className="text-gray-600">实时监控系统运行状态和关键指标，提供全面的服务质量分析</p>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleRefreshAll}
                size="sm"
                className="text-black rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
                variant='ghost'
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 统计卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsCards.map((card, index) => (
            <div
              key={index}
              className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-pink-100 hover:border-pink-200 group relative"
            >
              {card.loading ? (
                <div className="flex items-center justify-center h-24">
                  <Spinner size="default" />
                </div>
              ) : (
                <>
                  {/* 卡片头部 */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                        <div className={cn("transition-colors", card.color)}>{card.icon}</div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600">{card.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={cn('text-xl font-bold', card.color)}>
                            {typeof card.value === 'number' ? formatNumber(card.value) : card.value}
                          </span>
                          <div className={cn(
                            "flex items-center text-xs px-2 py-1 rounded-full",
                            card.trendUp
                              ? "bg-green-100 text-green-700"
                              : "bg-red-100 text-red-700"
                          )}>
                            {card.trendUp ? (
                              <TrendingUp className="h-3 w-3 mr-1" />
                            ) : (
                              <TrendingDown className="h-3 w-3 mr-1" />
                            )}
                            {card.trend}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 装饰性背景 */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-primary/20 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
                </>
              )}
            </div>
          ))}
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* 查询统计图表 */}
          <div className="lg:col-span-2 bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-primary/10">
                <BarChart3 className="h-5 w-5 text-primary" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">查询统计趋势</h2>
            </div>

            {queryLoading ? (
              <div className="flex items-center justify-center h-48 bg-slate-50 rounded-xl border border-slate-200">
                <Spinner size="lg" />
              </div>
            ) : queryError ? (
              <Alert variant="destructive" className="bg-red-50 border-red-200">
                <AlertDescription>加载查询统计失败: {queryError}</AlertDescription>
              </Alert>
            ) : (
              <div className="h-48 flex items-center justify-center bg-slate-50 rounded-xl border border-slate-200">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-3 text-primary/60" />
                  <p className="text-sm text-gray-600 font-medium">查询统计图表</p>
                  <p className="text-xs text-gray-500">24小时实时数据</p>
                </div>
              </div>
            )}
          </div>

          {/* 热门IP */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-purple-100">
                <Users className="h-5 w-5 text-purple-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">热门IP地址</h2>
            </div>

            {hotIPsLoading ? (
              <div className="flex items-center justify-center h-48">
                <Spinner size="lg" />
              </div>
            ) : hotIPsError ? (
              <Alert variant="destructive" className="bg-red-50 border-red-200">
                <AlertDescription>加载热门IP失败: {hotIPsError}</AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {hotIPs?.slice(0, 5).map((ip, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-xl border border-slate-200 hover:border-slate-300 transition-colors">
                    <div className="flex items-center gap-3">
                      <Badge
                        variant="outline"
                        className="bg-pink-100 text-pink-700 border-pink-200 font-mono text-xs"
                      >
                        {index + 1}
                      </Badge>
                      <span className="font-mono text-gray-700 text-sm">{ip.ip}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-purple-600 text-sm">{ip.query_count}</div>
                      <div className="text-xs text-gray-500">查询次数</div>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">暂无热门IP数据</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 系统信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-success/10">
                <Server className="h-5 w-5 text-success" />
              </div>
              <h3 className="text-lg font-bold text-gray-800">系统信息</h3>
            </div>

            {metricsLoading ? (
              <div className="flex items-center justify-center h-32">
                <Spinner size="default" />
              </div>
            ) : metricsError ? (
              <Alert variant="destructive" className="bg-red-50 border-red-200">
                <AlertDescription>加载监控指标失败: {metricsError}</AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 px-3 bg-green-50 rounded-lg">
                  <span className="text-sm text-gray-600">运行时间</span>
                  <span className="text-sm font-semibold text-green-600">{metrics?.system?.uptime}</span>
                </div>
                <div className="flex justify-between items-center py-2 px-3 bg-blue-50 rounded-lg">
                  <span className="text-sm text-gray-600">内存使用</span>
                  <span className="text-sm font-semibold text-blue-600">{metrics?.system?.memory_usage?.toFixed(2)}M</span>
                </div>
                <div className="flex justify-between items-center py-2 px-3 bg-purple-50 rounded-lg">
                  <span className="text-sm text-gray-600">CPU使用率</span>
                  <span className="text-sm font-semibold text-purple-600">{metrics?.system?.cpu_usage}%</span>
                </div>
              </div>
            )}
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-warning/10">
                <NetworkCheck className="h-5 w-5 text-warning" />
              </div>
              <h3 className="text-lg font-bold text-gray-800">网络状态</h3>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 px-3 bg-orange-50 rounded-lg">
                <span className="text-sm text-gray-600">API响应时间</span>
                <span className="text-sm font-semibold text-warning">45ms</span>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-green-50 rounded-lg">
                <span className="text-sm text-gray-600">数据库连接</span>
                <Badge className="bg-green-100 text-green-700 border-green-200 rounded-full">正常</Badge>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-blue-50 rounded-lg">
                <span className="text-sm text-gray-600">Redis缓存</span>
                <Badge className="bg-green-100 text-green-700 border-green-200 rounded-full">正常</Badge>
              </div>
            </div>
          </div>

          {/* 数据源状态卡片 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-purple-100">
                <Activity className="h-5 w-5 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-800">数据源状态</h3>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 px-3 bg-purple-50 rounded-lg">
                <span className="text-sm text-gray-600">活跃数据源</span>
                <span className="text-sm font-semibold text-purple-600">8/10</span>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-pink-50 rounded-lg">
                <span className="text-sm text-gray-600">最后更新</span>
                <span className="text-sm font-semibold text-pink-600">2小时前</span>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-yellow-50 rounded-lg">
                <span className="text-sm text-gray-600">同步状态</span>
                <Badge className="bg-blue-100 text-blue-700 border-blue-200 rounded-full">同步中</Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage