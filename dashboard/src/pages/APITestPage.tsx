import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Spinner } from '@/components/ui/spinner'
import {
  Play,
  Copy,
  CheckCircle,
  AlertTriangle,
  Globe,
  Clock,
  Code,
  FileText,
  Settings,
  ExternalLink,
} from '@/components/ui/icons'
import { apiService } from '../services/api'
import { useToast } from '../hooks/useToast'
import { cn } from '@/lib/utils'

// 简化的Select组件
const SimpleSelect = ({
  value,
  onValueChange,
  options,
  placeholder,
}: {
  value: string
  onValueChange: (value: string) => void
  options: { value: string; label: string; description?: string }[]
  placeholder?: string
}) => (
  <select
    value={value}
    onChange={(e) => onValueChange(e.target.value)}
    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
  >
    {placeholder && <option value="">{placeholder}</option>}
    {options.map((option) => (
      <option key={option.value} value={option.value}>
        {option.label}
      </option>
    ))}
  </select>
)

// 简化的Tabs组件
const SimpleTabs = ({
  defaultValue,
  children,
}: {
  defaultValue: string
  children: React.ReactNode
}) => {
  const [activeTab, setActiveTab] = useState(defaultValue)

  return (
    <div className="w-full">
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === TabsList) {
          return React.cloneElement(child as any, { activeTab, setActiveTab })
        }
        if (React.isValidElement(child) && child.type === TabsContent) {
          return activeTab === (child.props as any).value ? child : null
        }
        return child
      })}
    </div>
  )
}

const TabsList = ({
  children,
  activeTab,
  setActiveTab,
}: {
  children: React.ReactNode
  activeTab?: string
  setActiveTab?: (tab: string) => void
}) => (
  <div className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground grid grid-cols-3 w-full">
    {React.Children.map(children, (child) => {
      if (React.isValidElement(child)) {
        return React.cloneElement(child as any, { activeTab, setActiveTab })
      }
      return child
    })}
  </div>
)

const TabsTrigger = ({
  value,
  children,
  activeTab,
  setActiveTab,
}: {
  value: string
  children: React.ReactNode
  activeTab?: string
  setActiveTab?: (tab: string) => void
}) => (
  <button
    className={cn(
      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
      activeTab === value ? 'bg-background text-foreground shadow-sm' : ''
    )}
    onClick={() => setActiveTab?.(value)}
  >
    {children}
  </button>
)

const TabsContent = ({ value, children }: { value: string; children: React.ReactNode }) => (
  <div className="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
    {children}
  </div>
)

const APITestPage: React.FC = () => {
  const { showError, showSuccess } = useToast()
  const [loading, setLoading] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [selectedEndpoint, setSelectedEndpoint] = useState('query')
  const [testData, setTestData] = useState({
    ip: '*******',
    ips: '*******,*******,***************',
    format: 'json',
  })

  // API端点配置
  const endpoints = [
    {
      id: 'query',
      name: '单IP查询',
      method: 'POST',
      path: '/api/v1/query',
      description: '查询单个IP地址的地理位置信息',
      params: ['ip'],
    },
    {
      id: 'batch',
      name: '批量查询',
      method: 'POST',
      path: '/api/v1/batch',
      description: '批量查询多个IP地址的地理位置信息',
      params: ['ips'],
    },
    {
      id: 'stats',
      name: '统计信息',
      method: 'GET',
      path: '/api/v1/stats',
      description: '获取系统统计信息',
      params: [],
    },
    {
      id: 'health',
      name: '健康检查',
      method: 'GET',
      path: '/health',
      description: '检查系统健康状态',
      params: [],
    },
  ]

  const currentEndpoint = endpoints.find((e) => e.id === selectedEndpoint)

  // 执行API测试
  const handleTest = async () => {
    if (!currentEndpoint) return

    setLoading(true)
    setTestResult(null)

    const startTime = Date.now()
    try {
      let result

      switch (selectedEndpoint) {
        case 'query':
          result = await apiService.queryIP(testData.ip)
          break
        case 'batch':
          result = await apiService.batchQueryIP({
            ips: testData.ips.split(',').map((ip) => ip.trim()),
          })
          break
        case 'stats':
          result = await apiService.getQueryStats()
          break
        case 'health':
          result = await fetch('/health').then((res) => res.json())
          break
        default:
          throw new Error('未知的端点')
      }

      const endTime = Date.now()
      const responseTime = endTime - startTime

      setTestResult({
        success: true,
        data: result,
        responseTime,
        timestamp: new Date().toISOString(),
      })

      showSuccess(`API测试成功，响应时间: ${responseTime}ms`)
    } catch (error: any) {
      const endTime = Date.now()
      const responseTime = endTime - startTime

      setTestResult({
        success: false,
        error: error.message || '请求失败',
        responseTime,
        timestamp: new Date().toISOString(),
      })

      showError(`API测试失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  // 复制结果到剪贴板
  const handleCopyResult = () => {
    if (testResult) {
      navigator.clipboard.writeText(JSON.stringify(testResult, null, 2))
      showSuccess('结果已复制到剪贴板')
    }
  }

  // 生成示例代码
  const generateExampleCode = (language: string) => {
    const baseUrl = window.location.origin
    const endpoint = currentEndpoint

    if (!endpoint) return ''

    switch (language) {
      case 'curl':
        if (endpoint.method === 'GET') {
          return `curl -X GET "${baseUrl}${endpoint.path}"`
        } else {
          const body =
            selectedEndpoint === 'query'
              ? `{"ip": "${testData.ip}"}`
              : `{"ips": [${testData.ips
                  .split(',')
                  .map((ip) => `"${ip.trim()}"`)
                  .join(', ')}]}`
          return `curl -X POST "${baseUrl}${endpoint.path}" \\
  -H "Content-Type: application/json" \\
  -d '${body}'`
        }
      case 'javascript':
        if (endpoint.method === 'GET') {
          return `fetch('${baseUrl}${endpoint.path}')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`
        } else {
          const body =
            selectedEndpoint === 'query'
              ? `{ip: "${testData.ip}"}`
              : `{ips: [${testData.ips
                  .split(',')
                  .map((ip) => `"${ip.trim()}"`)
                  .join(', ')}]}`
          return `fetch('${baseUrl}${endpoint.path}', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(${body})
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`
        }
      case 'python':
        if (endpoint.method === 'GET') {
          return `import requests

response = requests.get('${baseUrl}${endpoint.path}')
data = response.json()
print(data)`
        } else {
          const body =
            selectedEndpoint === 'query'
              ? `{"ip": "${testData.ip}"}`
              : `{"ips": [${testData.ips
                  .split(',')
                  .map((ip) => `"${ip.trim()}"`)
                  .join(', ')}]}`
          return `import requests
import json

url = '${baseUrl}${endpoint.path}'
data = ${body}

response = requests.post(url, json=data)
result = response.json()
print(result)`
        }
      default:
        return ''
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold text-gray-600">
          API测试
        </h1>
        <p className="text-gray-600">测试和调试API接口功能</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 测试配置 */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-primary/10">
              <Settings className="h-5 w-5 text-primary" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">测试配置</h2>
          </div>
          <div className="space-y-4">
            {/* 端点选择 */}
            <div className="space-y-2">
              <Label>API端点</Label>
              <SimpleSelect
                value={selectedEndpoint}
                onValueChange={setSelectedEndpoint}
                options={endpoints.map((e) => ({
                  value: e.id,
                  label: `${e.method} ${e.name}`,
                  description: e.description,
                }))}
              />
              {currentEndpoint && (
                <p className="text-sm text-muted-foreground">{currentEndpoint.description}</p>
              )}
            </div>

            {/* 参数配置 */}
            {currentEndpoint?.params.includes('ip') && (
              <div className="space-y-2">
                <Label htmlFor="ip">IP地址</Label>
                <Input
                  id="ip"
                  value={testData.ip}
                  onChange={(e) => setTestData({ ...testData, ip: e.target.value })}
                  placeholder="请输入IP地址，如: *******"
                />
              </div>
            )}

            {currentEndpoint?.params.includes('ips') && (
              <div className="space-y-2">
                <Label htmlFor="ips">IP地址列表</Label>
                <Input
                  id="ips"
                  value={testData.ips}
                  onChange={(e) => setTestData({ ...testData, ips: e.target.value })}
                  placeholder="请输入多个IP地址，用逗号分隔"
                />
                <p className="text-xs text-muted-foreground">
                  多个IP地址请用逗号分隔，如: *******,*******
                </p>
              </div>
            )}

            {/* 执行测试 */}
            <Button onClick={handleTest} disabled={loading} className="w-full" variant="outline">
              {loading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  测试中...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  执行测试
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 测试结果 */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-success/10">
              <FileText className="h-5 w-5 text-success" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">测试结果</h2>
            {testResult && (
              <Button variant="ghost" size="sm" onClick={handleCopyResult} className="ml-auto">
                <Copy className="h-3 w-3 mr-1" />
                复制
              </Button>
            )}
          </div>
          <div>
            {!testResult ? (
              <div className="flex items-center justify-center h-64 text-muted-foreground">
                <div className="text-center">
                  <Play className="h-12 w-12 mx-auto mb-2" />
                  <p>点击"执行测试"开始API测试</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* 结果状态 */}
                <div className="flex items-center gap-2">
                  {testResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  )}
                  <Badge variant={testResult.success ? 'success' : 'destructive'}>
                    {testResult.success ? '成功' : '失败'}
                  </Badge>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {testResult.responseTime}ms
                  </div>
                </div>

                {/* 结果内容 */}
                <div className="bg-muted/20 rounded-lg p-4">
                  <pre className="text-sm overflow-auto max-h-96 bg-gray-50 rounded-sm">
                    {JSON.stringify(
                      testResult.success ? testResult.data : { error: testResult.error },
                      null,
                      2
                    )}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 代码示例 */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-xl bg-purple-100">
            <Code className="h-5 w-5 text-purple-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-800">代码示例</h2>
        </div>
        <div>
          <SimpleTabs defaultValue="curl">
            <TabsList>
              <TabsTrigger value="curl">cURL</TabsTrigger>
              <TabsTrigger value="javascript">JavaScript</TabsTrigger>
              <TabsTrigger value="python">Python</TabsTrigger>
            </TabsList>
            <TabsContent value="curl">
              <div className="bg-gray-50 rounded-lg p-2">
                <pre className="text-sm overflow-auto">{generateExampleCode('curl')}</pre>
              </div>
            </TabsContent>
            <TabsContent value="javascript">
              <div className="bg-gray-50 rounded-lg p-2">
                <pre className="text-sm overflow-auto">{generateExampleCode('javascript')}</pre>
              </div>
            </TabsContent>
            <TabsContent value="python">
              <div className="bg-gray-50 rounded-lg p-2">
                <pre className="text-sm overflow-auto">{generateExampleCode('python')}</pre>
              </div>
            </TabsContent>
          </SimpleTabs>
        </div>
      </div>

      {/* API文档链接 */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-xl bg-warning/10">
            <Globe className="h-5 w-5 text-warning" />
          </div>
          <h2 className="text-xl font-bold text-gray-800 ">
            <a
              className="flex justify-center items-center"
              href="/api/docs"
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              API文档
            </a>
          </h2>
        </div>
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {endpoints.map((endpoint) => (
              <div key={endpoint.id} className="p-4 border-none rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="default">{endpoint.method}</Badge>
                  <code className="text-sm">{endpoint.path}</code>
                </div>
                <p className="text-sm text-muted-foreground">{endpoint.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default APITestPage