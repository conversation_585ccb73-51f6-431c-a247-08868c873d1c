import React, { useCallback, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'

import {
  Server,
  Cpu,
  HardDrive as Memory,
  HardDrive,
  Activity,
  Gauge,
  Search,
  RefreshCw,
  TrendingUp,
  TrendingDown,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../hooks/useToast'
import { formatBytes } from '@/lib/utils'
import { cn } from '@/lib/utils'

const SystemStatusPage: React.FC = () => {
  const { showError } = useToast()
  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // API调用函数
  const getSystemStatus = useCallback(() => apiService.getSystemStatus(), [])
  const getSystemMetrics = useCallback(() => apiService.getMetrics(), [])

  // 获取系统状态数据
  const {
    data: systemStatus,
    error: statusError,
    refetch: refetchStatus,
  } = useMonitoringData(getSystemStatus, 5000)
  console.log(`systemStatus:`, systemStatus)
  const {
    data: systemMetrics,
    error: metricsError,
    refetch: refetchMetrics,
  } = useMonitoringData(getSystemMetrics, 5000)
  console.log(`systemMetrics:`, systemMetrics)
  // 处理错误
  React.useEffect(() => {
    if (statusError) showError('获取系统状态失败: ' + statusError)
    if (metricsError) showError('获取系统指标失败: ' + metricsError)
  }, [statusError, metricsError, showError])

  // 刷新所有数据
  const handleRefreshAll = async () => {
    setRefreshing(true)
    await Promise.all([refetchStatus(), refetchMetrics()])
    await new Promise((resolve) => setTimeout(resolve, 200))
    setRefreshing(false)
    // showSuccess('数据已刷新')
  }

  // 使用真实的系统指标数据
  const metrics = systemMetrics?.system

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 60 * 60))
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
    const minutes = Math.floor((seconds % (60 * 60)) / 60)
    return `${days}天 ${hours}小时 ${minutes}分钟`
  }

  // 系统服务状态
  const services = [
    { name: 'Web服务器', status: 'running', port: 8082 },
    { name: 'PostgreSQL', status: 'running', port: 5432 },
    { name: 'Redis', status: 'running', port: 6379 },
    { name: '数据源更新', status: 'running', port: null },
  ]

  return (
    <div className="min-h-full bg-slate-50 -m-4 sm:-m-6 lg:-m-8 p-4 sm:p-6 lg:p-8">
      {/* 页面内容 */}
      <div className="max-w-7xl mx-auto">
        {/* 页面标题区域 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div>
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-600 mb-2">
                系统状态监控
              </h1>
              <p className="text-gray-600">实时监控系统运行状态和性能指标，确保服务稳定运行</p>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleRefreshAll}
                disabled={refreshing}
                size="sm"
                className="text-black rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
                variant='ghost'
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 系统概览卡片 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {/* CPU使用率 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-pink-100 hover:border-pink-200 group relative">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-blue-100 to-purple-100 group-hover:from-blue-200 group-hover:to-purple-200 transition-colors">
                  <Cpu className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-600">CPU使用率</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-2xl font-bold text-blue-600">
                      {(metrics?.cpu_usage || 0).toFixed(1)}%
                    </span>
                    <div
                      className={cn(
                        'flex items-center text-xs px-2 py-1 rounded-full',
                        (metrics?.cpu_usage || 0) < 70
                          ? 'bg-green-100 text-green-700'
                          : (metrics?.cpu_usage || 0) < 90
                            ? 'bg-yellow-100 text-yellow-700'
                            : 'bg-red-100 text-red-700'
                      )}
                    >
                      {(metrics?.cpu_usage || 0) < 70 ? (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      )}
                      {(metrics?.cpu_usage || 0) < 70
                        ? 'ok'
                        : (metrics?.cpu_usage || 0) < 90
                          ? 'warn'
                          : 'danger'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Progress value={metrics?.cpu_usage || 0} className="h-2 mb-2" />
            <div className="text-xs text-gray-500">Goroutines: {metrics?.goroutine_count || 0}</div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-primary/20 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
          </div>

          {/* 内存使用率 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-pink-100 hover:border-pink-200 group relative">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-green-100 to-blue-100 group-hover:from-green-200 group-hover:to-blue-200 transition-colors">
                  <Memory className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-600">内存使用率</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-2xl font-bold text-green-600">
                      {(metrics?.memory_usage || 0).toFixed(1)}%
                    </span>
                    <div
                      className={cn(
                        'flex items-center text-xs px-2 py-1 rounded-full',
                        (metrics?.memory_usage || 0) < 80
                          ? 'bg-green-100 text-green-700'
                          : (metrics?.memory_usage || 0) < 95
                            ? 'bg-yellow-100 text-yellow-700'
                            : 'bg-red-100 text-red-700'
                      )}
                    >
                      {(metrics?.memory_usage || 0) < 80 ? (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      )}
                      {(metrics?.memory_usage || 0) < 80
                        ? 'ok'
                        : (metrics?.memory_usage || 0) < 95
                          ? 'warn'
                          : 'danger'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Progress value={metrics?.memory_usage || 0} className="h-2 mb-2" />
            <div className="text-xs text-gray-500">
              堆内存: {formatBytes(metrics?.heap_in_use || 0)} /{' '}
              {formatBytes(metrics?.heap_size || 0)}
            </div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-success/20 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
          </div>

          {/* 磁盘使用率 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-pink-100 hover:border-pink-200 group relative">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-orange-100 to-red-100 group-hover:from-orange-200 group-hover:to-red-200 transition-colors">
                  <HardDrive className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-600">磁盘使用率</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-2xl font-bold text-orange-600">
                      {(metrics?.disk_usage || 0).toFixed(1)}%
                    </span>
                    <div
                      className={cn(
                        'flex items-center text-xs px-2 py-1 rounded-full',
                        (metrics?.disk_usage || 0) < 80
                          ? 'bg-green-100 text-green-700'
                          : (metrics?.disk_usage || 0) < 95
                            ? 'bg-yellow-100 text-yellow-700'
                            : 'bg-red-100 text-red-700'
                      )}
                    >
                      {(metrics?.disk_usage || 0) < 80 ? (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      )}
                      {(metrics?.disk_usage || 0) < 80
                        ? 'ok'
                        : (metrics?.disk_usage || 0) < 95
                          ? 'warn'
                          : 'danger'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Progress value={metrics?.disk_usage || 0} className="h-2 mb-2" />
            <div className="text-xs text-gray-500">磁盘使用率暂不可用</div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-200/20 to-red-200/20 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
          </div>

          {/* 系统运行时间 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-pink-100 hover:border-pink-200 group relative">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 transition-colors">
                  <Activity className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-600">运行时间</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-sm font-bold text-purple-600">
                      {formatUptime(metrics?.uptime / 1e9 || 0)}
                    </span>
                    <div className="flex items-center text-xs px-2 py-1 rounded-full bg-green-100 text-green-700">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      ok
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
          </div>
        </div>

        {/* 网络和服务状态 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 服务状态 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-pink-100 hover:border-pink-200">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-gradient-to-br from-green-100 to-blue-100">
                <Server className="h-6 w-6 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800">服务状态</h2>
            </div>

            <div className="space-y-4">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-100 hover:border-green-200 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={cn(
                        'w-3 h-3 rounded-full',
                        service.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                      )}
                    />
                    <span className="font-medium text-gray-700">{service.name}</span>
                    {service.port && (
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        :{service.port}
                      </span>
                    )}
                  </div>
                  <Badge
                    className={cn(
                      'rounded-full',
                      service.status === 'running'
                        ? 'bg-green-100 text-green-700 border-green-200'
                        : 'bg-red-100 text-red-700 border-red-200'
                    )}
                  >
                    {service.status === 'running' ? '运行中' : '已停止'}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* 性能指标 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-purple-100">
                <Gauge className="h-6 w-6 text-purple-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800">性能指标</h2>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 px-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                <span className="text-sm text-gray-600">API响应时间</span>
                <span className="font-semibold text-purple-600">
                  {Math.round(systemMetrics?.service?.avg_response_time || 0)}ms
                </span>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg">
                <span className="text-sm text-gray-600">数据库查询时间</span>
                <span className="font-semibold text-pink-600">
                  {Math.round(systemMetrics?.database?.avg_query_time || 0)}ms
                </span>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg">
                <span className="text-sm text-gray-600">缓存命中率</span>
                <span className="font-semibold text-orange-600">
                  {(systemMetrics?.cache?.hit_rate || 0).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center py-2 px-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                <span className="text-sm text-gray-600">并发连接数</span>
                <span className="font-semibold text-blue-600">
                  {systemMetrics?.service?.active_connections || 0}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SystemStatusPage