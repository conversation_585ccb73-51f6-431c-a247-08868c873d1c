import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Github,
  ExternalLink,
  Book,
  Code,
  Users,
  GitBranch,
  Package,
  Shield,
  Zap,
  Globe,
  Database,
  BarChart3,
  Settings
} from 'lucide-react'
import { Card } from '@/components/ui/card'

const AboutPage: React.FC = () => {
  const features = [
    {
      icon: <Database className="h-5 w-5" />,
      title: 'IP地理位置查询',
      description: '支持多数据源的高精度IP地理位置查询服务'
    },
    {
      icon: <Zap className="h-5 w-5" />,
      title: '高性能架构',
      description: 'Redis缓存 + PostgreSQL存储 + API回源的三层查询架构'
    },
    {
      icon: <BarChart3 className="h-5 w-5" />,
      title: '实时监控',
      description: '完整的系统监控、API统计和性能分析功能'
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: '安全认证',
      description: 'JWT + API Key双重身份验证机制'
    },
    {
      icon: <Globe className="h-5 w-5" />,
      title: '多数据源',
      description: '集成MaxMind、IP2Location、DB-IP等多个数据源'
    },
    {
      icon: <Settings className="h-5 w-5" />,
      title: '管理后台',
      description: '现代化的React管理界面，支持明暗主题切换'
    }
  ]

  const techStack = [
    { name: 'Go', version: '1.21+', type: 'backend' },
    { name: 'PostgreSQL', version: '15+', type: 'database' },
    { name: 'Redis', version: '7+', type: 'cache' },
    { name: 'React', version: '19', type: 'frontend' },
    { name: 'TypeScript', version: '5.8', type: 'frontend' },
    { name: 'Tailwind CSS', version: '4.1', type: 'frontend' },
    { name: 'shadcn/ui', version: 'latest', type: 'frontend' },
    { name: 'Vite', version: '7', type: 'frontend' }
  ]

  const apiEndpoints = [
    {
      method: 'GET',
      path: '/api/v1/ip/{ip}',
      description: 'IP地理位置查询'
    },
    {
      method: 'POST',
      path: '/api/v1/ip/batch',
      description: '批量IP查询'
    },
    {
      method: 'GET',
      path: '/api/v1/stats',
      description: 'API统计信息'
    },
    {
      method: 'GET',
      path: '/api/v1/health',
      description: '系统健康检查'
    }
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-600">关于 ipInsight</h1>
          <p className="text-gray-600">高性能IP地理位置查询服务</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="border-primary/20  hover:bg-primary/10" asChild>
            <a href="https://github.com/cosin2077/ipInsight" target="_blank" rel="noopener noreferrer">
              <Github className="h-4 w-4 mr-2" />
              GitHub
            </a>
          </Button>
          <Button variant="outline" size="sm" className="border-primary/20  hover:bg-primary/10" asChild>
            <a href="/api/docs" target="_blank" rel="noopener noreferrer">
              <Book className="h-4 w-4 mr-2" />
              API文档
            </a>
          </Button>
        </div>
      </div>

      {/* 项目概述 */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-xl bg-primary/10">
            <Package className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-800">项目概述</h2>
            <p className="text-sm text-gray-600 mt-1">
              ipInsight 是一个现代化的IP地理位置查询服务，提供高精度、高性能的IP地理位置信息查询功能
            </p>
          </div>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start gap-3 p-3 rounded-lg border-none bg-muted/50">
                <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-lg">
                  {feature.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{feature.title}</h4>
                  <p className="text-xs text-muted-foreground mt-1">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 技术栈 */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-success/10">
              <Code className="h-5 w-5 text-success" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">技术栈</h2>
              <p className="text-sm text-gray-600 mt-1">
                项目使用的主要技术和框架
              </p>
            </div>
          </div>
          <div>
            <div className="space-y-4">
              {['backend', 'database', 'cache', 'frontend'].map((category) => (
                <div key={category}>
                  <h4 className="font-medium text-sm mb-2 capitalize">
                    {category === 'backend' && '后端'}
                    {category === 'database' && '数据库'}
                    {category === 'cache' && '缓存'}
                    {category === 'frontend' && '前端'}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {techStack
                      .filter((tech) => tech.type === category)
                      .map((tech, index) => (
                        <Badge key={index} variant="success" className="text-xs">
                          {tech.name} {tech.version}
                        </Badge>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* API文档 */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-purple-100">
              <Book className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">API接口</h2>
              <p className="text-sm text-gray-600 mt-1">
                主要的API接口端点
              </p>
            </div>
          </div>
          <div>
            <div className="space-y-3">
              {apiEndpoints.map((endpoint, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg border-none bg-muted/50">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge
                        variant={endpoint.method === 'GET' ? 'default' : 'success'}
                        className="text-xs"
                      >
                        {endpoint.method}
                      </Badge>
                      <code className="text-xs font-mono">{endpoint.path}</code>
                    </div>
                    <p className="text-xs text-muted-foreground">{endpoint.description}</p>
                  </div>
                </div>
              ))}
              <div className="pt-2">
                <Button variant="ghost" size="sm" className="w-full" asChild>
                  <a href="/api/docs" target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    查看完整API文档
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 项目信息 */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-xl bg-warning/10">
            <GitBranch className="h-5 w-5 text-warning" />
          </div>
          <h2 className="text-xl font-bold text-gray-800">项目信息</h2>
        </div>
        <div>

          <div className="border-t border-slate-200 my-6"></div>

          <div className="space-y-4">
            <div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 高性能三层查询架构，支持亿级IP数据查询</li>
                <li>• 多数据源集成，提供高精度地理位置信息</li>
                <li>• 现代化管理界面，支持实时监控和统计分析</li>
                <li>• 完整的API文档和SDK支持</li>
                <li>• 容器化部署，支持Docker和Kubernetes</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">联系方式</h4>
              <div className="flex flex-wrap gap-2">
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://github.com/cosin2077/ipInsight" target="_blank" rel="noopener noreferrer">
                    <Github className="h-4 w-4 mr-2" />
                    GitHub仓库
                  </a>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://github.com/cosin2077/ipInsight/issues" target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    问题反馈
                  </a>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://github.com/cosin2077/ipInsight/wiki" target="_blank" rel="noopener noreferrer">
                    <Book className="h-4 w-4 mr-2" />
                    使用文档
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 致谢 */}
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-xl bg-pink-100">
            <Users className="h-5 w-5 text-pink-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-800">致谢</h2>
        </div>
        <div>
          <div className="text-sm text-muted-foreground space-y-2">
            <p>感谢以下开源项目和数据提供商的支持：</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <h5 className="font-medium text-foreground mb-2">数据源</h5>
                <ul className="space-y-1">
                  <li>• MaxMind GeoLite2 数据库</li>
                  <li>• IP2Location 数据库</li>
                  <li>• DB-IP 数据库</li>
                  <li>• 纯真IP数据库</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-foreground mb-2">开源项目</h5>
                <ul className="space-y-1">
                  <li>• React & TypeScript</li>
                  <li>• Tailwind CSS & shadcn/ui</li>
                  <li>• Go & Gin Framework</li>
                  <li>• PostgreSQL & Redis</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AboutPage