import React, { useCallback, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/spinner'
import {
  Activity,
  BarChart3,
  Clock,
  TrendingUp,
  Users,
  Api,
  CheckCircle,
  AlertTriangle,
  Timer,
  Heart,
  Search,
  RefreshCw,
  TrendingDown,
} from '@/components/ui/icons'
import { useMonitoringData } from '../hooks/useApi'
import { apiService } from '../services/api'
import { useToast } from '../hooks/useToast'
import { formatNumber, formatResponseTime } from '@/lib/utils'
import { cn } from '@/lib/utils'

const APIStatsPage: React.FC = () => {
  const { showError } = useToast()
  const [searchQuery, setSearchQuery] = useState('')
  const [likedCards, setLikedCards] = useState<Record<number, number>>({})

  // API调用函数 - 使用真实后端接口
  const getAPIStats = useCallback(() => apiService.getQueryStats(), [])
  const getMetrics = useCallback(() => apiService.getMetrics(), [])

  // 获取API统计数据
  const {
    data: apiStats,
    loading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useMonitoringData(getAPIStats, 30000)
  console.log(`apiStats:`, apiStats)
  // 获取监控指标数据（包含API端点统计）
  const {
    data: metrics,
    loading: metricsLoading,
    error: metricsError,
    refetch: refetchMetrics,
  } = useMonitoringData(getMetrics, 30000)
  console.log(`apiStats:`, apiStats)
  // 处理错误
  React.useEffect(() => {
    if (statsError) showError('获取API统计失败: ' + statsError)
    if (metricsError) showError('获取监控指标失败: ' + metricsError)
  }, [statsError, metricsError, showError])

  // 刷新所有数据
  const handleRefreshAll = () => {
    refetchStats()
    refetchMetrics()
    // showSuccess('数据已刷新')
  }

  // 从后端API获取的真实数据
  const endpointStats = metrics?.api?.endpoint_stats || {}
  const endpoints = Object.values(endpointStats).map((stat: any) => ({
    path: stat.path,
    method: stat.method,
    requests: stat.request_count,
    avg_time: Math.round(stat.avg_duration / 1000000), // 转换纳秒到毫秒
    success_rate:
      stat.request_count > 0
        ? (((stat.request_count - stat.error_count) / stat.request_count) * 100).toFixed(1)
        : 0,
    last_access: stat.last_access,
  }))

  // 统计卡片数据 - 基于真实API数据
  const totalRequests = metrics?.service?.total_requests || 0
  const successRequests = metrics?.service?.success_requests || 0
  const errorRequests = metrics?.service?.error_requests || 0
  const avgResponseTime = metrics?.service?.avg_response_time || 0
  const successRate = totalRequests > 0 ? ((successRequests / totalRequests) * 100).toFixed(1) : 0
  const errorRate = totalRequests > 0 ? ((errorRequests / totalRequests) * 100).toFixed(1) : 0

  const statsCards = [
    {
      title: '总请求数',
      value: totalRequests,
      icon: <Activity className="h-5 w-5" />,
      trend: '+15.2%',
      trendUp: true,
      loading: statsLoading || metricsLoading,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    },
    {
      title: '缓存命中数',
      value: apiStats?.cache_hits || 0,
      icon: <TrendingUp className="h-5 w-5" />,
      trend: '+8.7%',
      trendUp: true,
      loading: statsLoading,
      color: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-950/20',
    },
    {
      title: '成功率',
      value: `${successRate}%`,
      icon: <CheckCircle className="h-5 w-5" />,
      trend: '+0.3%',
      trendUp: true,
      loading: statsLoading || metricsLoading,
      color: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-950/20',
    },
    {
      title: '平均响应时间',
      value: `${Math.round(avgResponseTime)}ms`,
      icon: <Timer className="h-5 w-5" />,
      trend: '-5.1%',
      trendUp: false,
      loading: statsLoading || metricsLoading,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50 dark:bg-orange-950/20',
    },
    {
      title: '错误率',
      value: `${errorRate}%`,
      icon: <AlertTriangle className="h-5 w-5" />,
      trend: '-2.1%',
      trendUp: false,
      loading: statsLoading || metricsLoading,
      color: parseFloat(errorRate.toString()) > 1 ? 'text-red-500' : 'text-green-500',
      bgColor: parseFloat(errorRate.toString()) > 1 ? 'bg-red-50 dark:bg-red-950/20' : 'bg-green-50 dark:bg-green-950/20',
    },
    {
      title: '数据库查询',
      value: apiStats?.database_hits || 0,
      icon: <Users className="h-5 w-5" />,
      trend: '+12.4%',
      trendUp: true,
      loading: statsLoading,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50 dark:bg-purple-950/20',
    },
  ]

  // 获取响应时间颜色
  const getResponseTimeColor = (time: number) => {
    if (time <= 100) return 'text-green-500'
    if (time <= 500) return 'text-yellow-500'
    return 'text-red-500'
  }

  return (
    <div className="min-h-full bg-slate-50 -m-4 sm:-m-6 lg:-m-8 p-4 sm:p-6 lg:p-8">
      {/* 页面内容 */}
      <div className="max-w-7xl mx-auto">
        {/* 页面标题区域 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div>
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-600 mb-2">
                API统计中心
              </h1>
              <p className="text-gray-600">实时监控API接口调用统计和性能数据，提供全面的服务质量分析</p>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleRefreshAll}
                size="sm"
                className="text-black rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
                variant='ghost'
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 统计卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {statsCards.map((card, index) => (
            <div
              key={index}
              className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 group"
            >
              {card.loading ? (
                <div className="flex items-center justify-center h-32">
                  <Spinner size="default" />
                </div>
              ) : (
                <>
                  {/* 卡片头部 */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-3 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                        <div className={cn("transition-colors", card.color)}>{card.icon}</div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-600">{card.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={cn('text-2xl font-bold', card.color)}>
                            {typeof card.value === 'number' ? formatNumber(card.value) : card.value}
                          </span>
                          <div className={cn(
                            "flex items-center text-xs px-2 py-1 rounded-full",
                            card.trendUp
                              ? "bg-green-100 text-green-700"
                              : "bg-red-100 text-red-700"
                          )}>
                            {card.trendUp ? (
                              <TrendingUp className="h-3 w-3 mr-1" />
                            ) : (
                              <TrendingDown className="h-3 w-3 mr-1" />
                            )}
                            {card.trend}
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>

                  {/* 装饰性渐变背景 */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-pink-200/20 to-orange-200/20 rounded-full blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
                </>
              )}
            </div>
          ))}
        </div>

        {/* API端点统计卡片列表 */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-purple-100">
              <Api className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800">API端点统计</h2>
          </div>

          {metricsLoading ? (
            <div className="flex items-center justify-center h-64 bg-white/70 backdrop-blur-sm rounded-2xl">
              <Spinner size="lg" />
            </div>
          ) : metricsError ? (
            <Alert variant="destructive" className="bg-red-50 border-red-200">
              <AlertDescription>加载端点统计失败: {metricsError}</AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {endpoints.map((endpoint, index) => (
                <div
                  key={index}
                  className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-pink-200"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-4 lg:space-y-0">
                    {/* 端点信息 */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Badge
                          variant="outline"
                          className={cn(
                            "font-mono text-xs px-2 py-1",
                            endpoint.method === 'GET' ? 'bg-green-50 text-green-700 border-green-200' :
                            endpoint.method === 'POST' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                            endpoint.method === 'PUT' ? 'bg-orange-50 text-orange-700 border-orange-200' :
                            'bg-gray-50 text-gray-700 border-gray-200'
                          )}
                        >
                          {endpoint.method}
                        </Badge>
                        <code className="text-sm font-mono text-gray-700 bg-gray-100 px-2 py-1 rounded">
                          {endpoint.path}
                        </code>
                      </div>
                    </div>

                    {/* 统计数据 */}
                    <div className="flex flex-wrap items-center gap-6 text-sm">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {formatNumber(endpoint.requests)}
                        </div>
                        <div className="text-xs text-gray-500">请求数</div>
                      </div>

                      <div className="text-center">
                        <div className={cn(
                          "text-lg font-bold",
                          getResponseTimeColor(endpoint.avg_time)
                        )}>
                          {formatResponseTime(endpoint.avg_time)}
                        </div>
                        <div className="text-xs text-gray-500">响应时间</div>
                      </div>

                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">
                          {typeof endpoint.success_rate === 'number'
                            ? endpoint.success_rate.toFixed(1)
                            : endpoint.success_rate}%
                        </div>
                        <div className="text-xs text-gray-500">成功率</div>
                      </div>

                      <div className="text-center">
                        <Badge
                          className={cn(
                            "text-xs px-3 py-1 rounded-full",
                            parseFloat(endpoint.success_rate.toString()) >= 99
                              ? 'bg-green-100 text-green-700 border-green-200'
                              : parseFloat(endpoint.success_rate.toString()) >= 95
                                ? 'bg-yellow-100 text-yellow-700 border-yellow-200'
                                : 'bg-red-100 text-red-700 border-red-200'
                          )}
                        >
                          {parseFloat(endpoint.success_rate.toString()) >= 99
                            ? '优秀'
                            : parseFloat(endpoint.success_rate.toString()) >= 95
                              ? '良好'
                              : '需要关注'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 性能趋势图表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-slate-200">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-primary/10">
                <BarChart3 className="h-5 w-5 text-primary" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800">请求量趋势</h3>
            </div>
            <div className="h-64 flex items-center justify-center bg-slate-50 rounded-xl border border-slate-200">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-3 text-primary/60" />
                <p className="text-sm text-gray-600 font-medium">请求量趋势图表</p>
                <p className="text-xs text-gray-500">24小时实时数据</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-slate-200">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-xl bg-warning/10">
                <Clock className="h-5 w-5 text-warning" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800">响应时间趋势</h3>
            </div>
            <div className="h-64 flex items-center justify-center bg-orange-50 rounded-xl border border-orange-200">
              <div className="text-center">
                <Clock className="h-12 w-12 mx-auto mb-3 text-orange-400" />
                <p className="text-sm text-gray-600 font-medium">响应时间趋势图表</p>
                <p className="text-xs text-gray-500">24小时实时数据</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default APIStatsPage