import { format, formatDistanceToNow, fromUnixTime } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 格式化数字
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num?.toString()
}

// 格式化百分比
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`
}

// 格式化字节大小
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

// 格式化时间戳
export const formatTimestamp = (timestamp: number): string => {
  return format(fromUnixTime(timestamp), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
}

// 格式化相对时间
export const formatRelativeTime = (timestamp: number): string => {
  if (!timestamp) return '-'
  return formatDistanceToNow(fromUnixTime(timestamp), {
    addSuffix: true,
    locale: zhCN,
  })
}

// 格式化响应时间
export const formatResponseTime = (ms: number): string => {
  if (ms < 1000) {
    return `${ms.toFixed(0)}ms`
  }
  return `${(ms / 1000).toFixed(2)}s`
}

// 格式化IP地址（添加掩码等）
export const formatIP = (ip: string): string => {
  // 简单的IP格式化，可以根据需要扩展
  return ip
}

// 格式化状态文本
export const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    ok: '正常',
    healthy: '健康',
    warning: '警告',
    critical: '严重',
    error: '错误',
    active: '活跃',
    inactive: '非活跃',
    updating: '更新中',
  }

  return statusMap[status.toLowerCase()] || status
}

// 获取状态颜色
export const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    ok: '#4caf50',
    healthy: '#4caf50',
    warning: '#ff9800',
    critical: '#f44336',
    error: '#f44336',
    active: '#4caf50',
    inactive: '#9e9e9e',
    updating: '#2196f3',
  }

  return colorMap[status.toLowerCase()] || '#9e9e9e'
}

// 格式化国家代码
export const formatCountryCode = (code: string): string => {
  // 可以添加国家代码到国家名称的映射
  return code.toUpperCase()
}

// 格式化持续时间
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(0)}秒`
  }
  if (seconds < 3600) {
    return `${(seconds / 60).toFixed(0)}分钟`
  }
  if (seconds < 86400) {
    return `${(seconds / 3600).toFixed(1)}小时`
  }
  return `${(seconds / 86400).toFixed(1)}天`
}

// 格式化错误率
export const formatErrorRate = (errorCount: number, totalCount: number): string => {
  if (totalCount === 0) return '0%'
  const rate = (errorCount / totalCount) * 100
  return formatPercentage(rate)
}

// 格式化缓存命中率
export const formatCacheHitRate = (hits: number, total: number): string => {
  if (total === 0) return '0%'
  const rate = (hits / total) * 100
  return formatPercentage(rate)
}