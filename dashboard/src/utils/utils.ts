export const formatSource = (name: string) => {
  const include = name?.toLowerCase().includes.bind(name?.toLowerCase())
  if (include('dbip') || include('db-ip')) return 'dbip'
  if (include('ip2location')) return 'ip2location'
  if (include('ip2locate')) return 'ip2locate'
  if (include('iplocate')) return 'iplocate'
  if (include('maxmind')) return 'maxmind'
  if (include('qqwry')) return 'qqwry'
  if (include('ipapi')) return 'ipapi'
}
export const formatSources = (sources: string[]) => {
  return sources.map(formatSource).filter(Boolean) as string[];
};
