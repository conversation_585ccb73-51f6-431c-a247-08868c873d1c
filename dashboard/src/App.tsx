
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'
import { Toaster } from '@/components/ui/sonner'
import Layout from './components/Layout/Layout'
import LoginPage from './pages/LoginPage'
import DashboardPage from './pages/DashboardPage'
import SystemStatusPage from './pages/SystemStatusPage'
import APIStatsPage from './pages/APIStatsPage'
import DataSourcesPage from './pages/DataSourcesPage'
import APITestPage from './pages/APITestPage'
import AboutPage from './pages/AboutPage'
import ProtectedRoute from './components/ProtectedRoute'

function App() {
  return (
    <ThemeProvider defaultTheme="dark">
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Routes>
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      <Route path="/dashboard" element={<DashboardPage />} />
                      <Route path="/system" element={<SystemStatusPage />} />
                      <Route path="/api-stats" element={<APIStatsPage />} />
                      <Route path="/data-sources" element={<DataSourcesPage />} />
                      <Route path="/api-test" element={<APITestPage />} />
                      <Route path="/about" element={<AboutPage />} />
                    </Routes>
                  </Layout>
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
        <Toaster />
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
