// API响应类型定义

export interface ApiResponse<T> {
  timestamp: number;
  data?: T;
  error?: string;
  message?: string;
}

// 系统状态相关类型
export interface SystemStatus {
  status: string;
  timestamp: number;
  user?: string;
  auth_method?: string;
  version: string;
}

export interface HealthStatus {
  [serviceName: string]: HealthCheck;
}

export interface HealthCheck {
  service: string;
  status: string;
  message?: string;
  timestamp: string;
  duration: number;
}

// 查询统计类型
export interface QueryStats {
  cache_hits: number;
  database_hits: number;
  api_fallbacks: number;
  total_queries: number;
  errors: number;
  cache_hit_rate: number;
  db_hit_rate: number;
}

// 热门IP类型 - 匹配后端HotIPInfo结构
export interface HotIP {
  ip: string;
  query_count: number;
  last_access: string;
  first_access: string;
  score: number;
  cache_hit?: boolean;
  // 向后兼容字段
  count?: number;
  last_query?: number;
  country?: string;
  city?: string;
}

export interface HotIPStats {
  enabled: boolean;
  total_tracked_ips: number;
  hot_ips_count: number;
  last_update: string;
  config: {
    enabled: boolean;
    min_query_count: number;
    time_window: number;
    max_hot_ips: number;
    cache_ttl: number;
    update_interval: number;
  };
  // 向后兼容字段
  total_unique_ips?: number;
  top_countries?: CountryStats[];
  top_cities?: CityStats[];
  query_distribution?: QueryDistribution[];
}

export interface CountryStats {
  country: string;
  count: number;
  percentage: number;
}

export interface CityStats {
  city: string;
  country: string;
  count: number;
  percentage: number;
}

export interface QueryDistribution {
  hour: number;
  count: number;
}

// 批量统计类型 - 匹配后端数据库统计结构
export interface BatchStats {
  total_queries: number;
  success_queries: number;
  error_queries: number;
  avg_query_time: number;
  active_connections: number;
  pool_size: number;
  timestamp: string;
  // 向后兼容字段
  total_batches?: number;
  total_ips_processed?: number;
  avg_batch_size?: number;
  avg_processing_time?: number;
  success_rate?: number;
}

// 监控指标类型
export interface MonitoringMetrics {
  system: SystemMetrics;
  service: ServiceMetrics;
  database: DatabaseMetrics;
  cache: CacheMetrics;
  api: ApiMetrics;
  last_update: number;
}

export interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  goroutine_count: number;
  heap_size: number;
  heap_in_use: number;
  uptime: number;
  timestamp: string;
  // 向后兼容字段
  disk_usage?: number;
  goroutines?: number;
}

export interface ServiceMetrics {
  total_requests: number;
  success_requests: number;
  error_requests: number;
  avg_response_time: number;
  requests_per_second: number;
  active_connections: number;
  timestamp: string;
}

export interface DatabaseMetrics {
  total_queries: number;
  success_queries: number;
  error_queries: number;
  avg_query_time: number;
  active_connections: number;
  pool_size: number;
  timestamp: string;
  // 向后兼容字段
  total_connections?: number;
  idle_connections?: number;
  slow_queries?: number;
}

export interface CacheMetrics {
  total_requests: number;
  cache_hits: number;
  cache_misses: number;
  hit_rate: number;
  eviction_count: number;
  key_count: number;
  timestamp: string;
  // 向后兼容字段
  memory_usage?: number;
}

export interface ApiMetrics {
  endpoint_stats: Record<string, EndpointStat>;
  timestamp: string;
  // 向后兼容字段
  total_requests?: number;
  error_rate?: number;
  avg_response_time?: number;
}

export interface EndpointStat {
  path: string;
  method: string;
  request_count: number;
  error_count: number;
  avg_duration: number;
  last_access: string;
  // 向后兼容字段
  count?: number;
  avg_response_time?: number;
}

// 数据源相关类型
export interface DataSourceStatus {
  name: string;
  enabled: boolean;
  last_update: number;
  next_update: number;
  status: 'active' | 'inactive' | 'error' | 'updating';
  error?: string;
  records_count?: number;
  size?: number;
  records?: number;
}

export interface DataSourceUpdateRequest {
  sources?: string[];
  force?: boolean;
}

// 后端实际返回的数据源更新响应格式
export interface DataSourceUpdateResponse {
  force: boolean;
  status: 'success' | 'error' | 'partial';
  updated_sources: Record<string, number>; // 数据源名称 -> 处理记录数
  message?: string;
  errors?: Record<string, string>; // 数据源名称 -> 错误信息
}

// 前端使用的标准化数据源更新结果格式
export interface DataSourceUpdateResult {
  source: string;
  success: boolean;
  message: string;
  records_processed?: number;
  duration?: number;
  error?: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  expires_at: number;
}

// IP查询相关类型 - 新的嵌套结构
export interface IPQueryResponse {
  ip_range: {
    cidr: string;
    start_ip: string;
    end_ip: string;
    ip_version: string;
    netmask: string;
  };
  geolocation: {
    continent: {
      code: string;
      name: string;
    };
    country: {
      code: string;
      name: string;
      is_in_european_union: boolean | null;
      geoname_id: number | null;
    };
    region: {
      code: string;
      name: string;
      geoname_id: number | null;
    };
    subdivisions: any;
    city: string;
    postal_code: string;
    latitude: number;
    longitude: number;
    elevation: number | null;
    accuracy_radius: number | null;
    geoname_id: number | null;
  };
  network: {
    isp: string;
    asn: string;
    organization: string;
    domain: string;
    autonomous_system_id: number | null;
    connection_type: string;
    usage_type: string;
    mcc: string;
    mnc: string;
    carrier: string;
    hosting_provider: string;
    cloud_provider: string;
    is_business_ip: boolean | null;
    is_residential_ip: boolean | null;
    is_education_ip: boolean | null;
    is_government_ip: boolean | null;
    is_mobile_ip: boolean | null;
    network_speed: string;
  };
  timezone: {
    name: string;
    offset: string;
  };
  security: {
    is_proxy: boolean | null;
    proxy_type: string;
    proxy_level: string;
    is_vpn: boolean | null;
    vpn_provider: string;
    is_tor: boolean | null;
    is_anonymizer: boolean | null;
    is_anonymous: boolean | null;
    is_data_center: boolean | null;
    is_hosting: boolean | null;
    is_cloud_ip: boolean | null;
    threat_level: string;
    threat_score: number | null;
    threat_types: any;
    is_malicious: boolean | null;
    is_bot: boolean | null;
    is_spammer: boolean | null;
    is_attacker: boolean | null;
    is_scanner: boolean | null;
    last_seen: string | null;
    is_residential_proxy: boolean | null;
    is_mobile_proxy: boolean | null;
    reputation_score: number | null;
    risk_level: string;
    is_blacklisted: boolean | null;
    blacklist_sources: any;
  };
  metadata: {
    source: string;
    last_updated: string;
    confidence: number;
    source_version: string;
    accuracy: number | null;
  };
  extended: {
    currency: {
      code: string;
      name: string;
    };
    languages: any;
    calling_code: string;
    flag: string;
    population: number | null;
    weather_code: string;
    market_segment: string;
    custom_fields: any;
  };
}

// 保持向后兼容的扁平化接口
export interface IPInfo {
  ip: string;
  country?: string;
  country_code?: string;
  region?: string;
  city?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  isp?: string;
  organization?: string;
  asn?: number;
  postal_code?: string;
  is_proxy?: boolean;
  source: string;
  query_time: number;
}

export interface BatchQueryRequest {
  ips: string[];
}

export interface BatchQueryResponse {
  results: IPInfo[];
  total: number;
  success: number;
  failed: number;
  processing_time: number;
}

// 监控摘要类型
export interface MonitoringSummary {
  service_health: 'healthy' | 'warning' | 'critical';
  total_requests_24h: number;
  error_rate_24h: number;
  avg_response_time_24h: number;
  cache_hit_rate_24h: number;
  active_data_sources: number;
  last_data_update: number;
  system_load: {
    cpu: number;
    memory: number;
    disk: number;
  };
  alerts: Alert[];
}

export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  resolved?: boolean;
}
