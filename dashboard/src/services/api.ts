import axios from 'axios'
import type { AxiosInstance } from 'axios'
import type {
  SystemStatus,
  HealthStatus,
  QueryStats,
  HotIP,
  HotIPStats,
  BatchStats,
  MonitoringMetrics,
  MonitoringSummary,
  DataSourceUpdateRequest,
  DataSourceUpdateResult,
  DataSourceUpdateResponse,
  DataSourceStatus,
  LoginRequest,
  LoginResponse,
  IPQueryResponse,
  BatchQueryRequest,
  BatchQueryResponse,
} from '../types/api'
import { formatSources } from '../utils/utils'

class ApiService {
  private api: AxiosInstance
  private token: string | null = null
  private toastCallback?: (message: string, type: 'success' | 'error' | 'warning' | 'info') => void

  constructor() {
    this.api = axios.create({
      baseURL: '/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // 响应拦截器 - 处理错误
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearToken()
          this.showToast('登录已过期，请重新登录', 'warning')
          window.location.href = '/login'
        } else if (error.response?.status >= 500) {
          this.showToast('服务器错误，请稍后重试', 'error')
        } else if (error.response?.status >= 400) {
          const message = error.response?.data?.message || '请求失败'
          this.showToast(message, 'error')
        } else if (error.code === 'NETWORK_ERROR') {
          this.showToast('网络连接失败，请检查网络设置', 'error')
        } else if (error.code === 'ECONNABORTED') {
          this.showToast('请求超时，请稍后重试', 'warning')
        }
        return Promise.reject(error)
      }
    )

    // 从localStorage恢复token
    this.loadToken()
  }

  // 设置Toast回调函数
  setToastCallback(
    callback: (message: string, type: 'success' | 'error' | 'warning' | 'info') => void
  ) {
    this.toastCallback = callback
  }

  // 显示Toast消息
  private showToast(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
    if (this.toastCallback) {
      this.toastCallback(message, type)
    }
  }

  // Token管理
  setToken(token: string): void {
    this.token = token
    localStorage.setItem('auth_token', token)
  }

  clearToken(): void {
    this.token = null
    localStorage.removeItem('auth_token')
  }

  private loadToken(): void {
    const token = localStorage.getItem('auth_token')
    if (token) {
      this.token = token
    }
  }

  isAuthenticated(): boolean {
    return !!this.token
  }

  // 认证相关API
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post<LoginResponse>('/auth/login', credentials)
    const { token } = response.data
    this.setToken(token)
    return response.data
  }

  logout(): void {
    this.clearToken()
  }

  // 公开API（无需认证）
  async getHealth(): Promise<{ status: string }> {
    const response = await axios.get('/health')
    return response.data
  }

  async queryIP(ip: string): Promise<IPQueryResponse> {
    const response = await this.api.get<IPQueryResponse>(`/ip/${ip}`)
    return response.data
  }

  async batchQueryIP(request: BatchQueryRequest): Promise<BatchQueryResponse> {
    const response = await this.api.post<BatchQueryResponse>('/ip/batch', request)
    return response.data
  }

  // 管理API（需要认证）
  async getSystemStatus(): Promise<SystemStatus> {
    const response = await this.api.get<{
      timestamp: number
      status: SystemStatus
    }>('/admin/status')
    return response.data?.status || {
      status: 'unknown',
      timestamp: Date.now(),
      version: '1.0.0'
    }
  }

  async getHealthStatus(): Promise<HealthStatus> {
    const response = await this.api.get<{
      timestamp: number
      health: HealthStatus
    }>('/admin/health')
    return response.data?.health || {}
  }

  async getQueryStats(): Promise<QueryStats> {
    const response = await this.api.get<{
      timestamp: number
      stats: QueryStats
    }>('/admin/stats')
    return response.data?.stats || {
      cache_hits: 0,
      database_hits: 0,
      api_fallbacks: 0,
      total_queries: 0,
      errors: 0,
      cache_hit_rate: 0,
      db_hit_rate: 0
    }
  }
  async getHotIPs(): Promise<HotIP[]> {
    const response = await this.api.get<{
      timestamp: number
      hot_ips: HotIP[]
    }>('/admin/hot-ips')
    return response.data?.hot_ips || []
  }

  async getHotIPStats(): Promise<HotIPStats> {
    const response = await this.api.get<{
      timestamp: number
      stats: HotIPStats
    }>('/admin/hot-ip-stats')
    return response.data?.stats
  }

  async getBatchStats(): Promise<BatchStats> {
    const response = await this.api.get<{
      timestamp: number
      stats: BatchStats
    }>('/admin/batch-stats')
    return response.data?.stats
  }

  async getMetrics(): Promise<MonitoringMetrics> {
    const response = await this.api.get<{
      timestamp: number
      metrics: MonitoringMetrics
    }>('/admin/metrics')
    return response.data?.metrics
  }

  async getSummary(): Promise<MonitoringSummary> {
    const response = await this.api.get<{
      timestamp: number
      summary: MonitoringSummary
    }>('/admin/summary')
    return response.data?.summary || {
      service_health: 'healthy',
      total_requests_24h: 0,
      error_rate_24h: 0,
      avg_response_time_24h: 0,
      cache_hit_rate_24h: 0,
      active_data_sources: 0,
      last_data_update: 0,
      system_load: { cpu: 0, memory: 0, disk: 0 },
      alerts: []
    }
  }

  async getDataSources(): Promise<DataSourceStatus[]> {
    const response = await this.api.get<{
      timestamp: number
      datasources: DataSourceStatus[]
    }>('/admin/datasources')
    return response.data?.datasources || []
  }

  async fetchDataSource(sources: string[]): Promise<any> {
    const payload = { sources }
    const response = await this.api.post('/admin/fetch', payload)
    return response.data
  }

  // 数据源管理API
  async updateDataSources(request: DataSourceUpdateRequest): Promise<DataSourceUpdateResult[]> {
    request.sources = formatSources(request.sources)
    const response = await this.api.post<DataSourceUpdateResponse>('/admin/fetch', request)

    // 将后端响应转换为前端期望的格式
    return this.transformUpdateResponse(response.data, request.sources || [])
  }

  // 转换后端响应为前端标准格式
  private transformUpdateResponse(
    response: DataSourceUpdateResponse,
    requestedSources: string[]
  ): DataSourceUpdateResult[] {
    const results: DataSourceUpdateResult[] = []

    // 处理成功更新的数据源
    Object.entries(response.updated_sources || {}).forEach(([source, recordCount]) => {
      results.push({
        source,
        success: true,
        message: `成功更新 ${recordCount} 条记录`,
        records_processed: typeof recordCount === 'number' ? recordCount : 0,
      })
    })

    // 处理失败的数据源
    Object.entries(response.errors || {}).forEach(([source, error]) => {
      results.push({
        source,
        success: false,
        message: `更新失败: ${error}`,
        error: typeof error === 'string' ? error : String(error),
      })
    })

    // 处理请求的但没有在响应中的数据源（可能是跳过或其他状态）
    requestedSources.forEach((source) => {
      const hasResult = results.some((r) => r.source === source)
      if (!hasResult) {
        results.push({
          source,
          success: response.status === 'success',
          message: response.status === 'success' ? '更新完成' : '更新状态未知',
        })
      }
    })

    return results
  }

  async validateDataSources(): Promise<{
    message: string
    results: Record<string, unknown>
  }> {
    const response = await this.api.post<{
      message: string
      results: Record<string, unknown>
    }>('/admin/validate')
    return response.data
  }

  // Prometheus指标API
  async getPrometheusMetrics(): Promise<string> {
    const response = await this.api.get<string>('/metrics', {
      headers: {
        Accept: 'text/plain',
      },
    })
    return response.data
  }
}

// 创建单例实例
export const apiService = new ApiService()
export default apiService