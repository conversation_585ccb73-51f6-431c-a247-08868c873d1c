import { useState, useEffect, useCallback, useRef } from 'react'

interface UseApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

interface UseApiOptions {
  immediate?: boolean
  interval?: number // 自动刷新间隔（毫秒）
}

export function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = {}
): UseApiState<T> & { refetch: () => Promise<void> } {
  const { immediate = true, interval } = options
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: immediate,
    error: null,
  })

  // 使用 useRef 来存储最新的 apiCall，避免依赖变化导致的无限循环
  const apiCallRef = useRef(apiCall)
  apiCallRef.current = apiCall

  const fetchData = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      const data = await apiCallRef.current()
      setState({ data, loading: false, error: null })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '请求失败'
      setState({ data: null, loading: false, error: errorMessage })
    }
  }, []) // 移除 apiCall 依赖，使用 ref 来访问最新值

  useEffect(() => {
    if (immediate) {
      fetchData()
    }
  }, [fetchData, immediate])

  useEffect(() => {
    if (interval && interval > 0) {
      const timer = setInterval(fetchData, interval)
      return () => clearInterval(timer)
    }
  }, [fetchData, interval])

  return {
    ...state,
    refetch: fetchData,
  }
}

// 专门用于监控数据的hook，支持自动刷新
export function useMonitoringData<T>(
  apiCall: () => Promise<T>,
  refreshInterval: number = 30000 // 默认30秒刷新一次
): UseApiState<T> & { refetch: () => Promise<void> } {
  return useApi(apiCall, {
    immediate: true,
    interval: refreshInterval,
  })
}