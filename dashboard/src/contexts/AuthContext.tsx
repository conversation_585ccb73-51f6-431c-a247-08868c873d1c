import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import { apiService } from '../services/api'
import type { LoginRequest } from '../types/api'

interface AuthContextType {
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => void
  error: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 检查是否已经登录
    const checkAuth = async () => {
      try {
        if (apiService.isAuthenticated()) {
          // 验证token是否有效
          await apiService.getSystemStatus()
          setIsAuthenticated(true)
        }
      } catch (error) {
        // Token无效，清除
        apiService.clearToken()
        setIsAuthenticated(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true)
      setError(null)
      await apiService.login(credentials)
      setIsAuthenticated(true)
    } catch (error: any) {
      setError(error.message || '登录失败')
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    apiService.logout()
    setIsAuthenticated(false)
    setError(null)
  }

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    login,
    logout,
    error,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}