package cache

import (
	"context"
	"encoding/json"
	"sort"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type HotIPManager struct {
	cache  *Cache
	logger *zap.Logger

	// 热门IP统计
	stats struct {
		sync.RWMutex
		QueryCounts map[string]int64     `json:"query_counts"` // IP查询次数
		LastAccess  map[string]time.Time `json:"last_access"`  // 最后访问时间
		FirstAccess map[string]time.Time `json:"first_access"` // 首次访问时间
		HotIPs      []string             `json:"hot_ips"`      // 热门IP列表
		LastUpdate  time.Time            `json:"last_update"`  // 最后更新时间
	}

	// 配置
	config HotIPConfig
}

type HotIPConfig struct {
	Enabled              bool          `json:"enabled"`
	MinQueryCount        int64         `json:"min_query_count"`        // 最小查询次数阈值
	TimeWindow           time.Duration `json:"time_window"`            // 时间窗口
	MaxHotIPs            int           `json:"max_hot_ips"`            // 最大热门IP数量
	CacheTTL             time.Duration `json:"cache_ttl"`              // 热门IP缓存TTL
	UpdateInterval       time.Duration `json:"update_interval"`        // 更新间隔
	PrewarmEnabled       bool          `json:"prewarm_enabled"`        // 是否启用预热
	PrewarmBatchSize     int           `json:"prewarm_batch_size"`     // 预热批次大小
	StatsPersistInterval time.Duration `json:"stats_persist_interval"` // 统计持久化间隔
}

type HotIPInfo struct {
	IP          string    `json:"ip"`
	QueryCount  int64     `json:"query_count"`
	LastAccess  time.Time `json:"last_access"`
	FirstAccess time.Time `json:"first_access"`
	Score       float64   `json:"score"`     // 热度分数
	CacheHit    bool      `json:"cache_hit"` // 是否已缓存
}

func NewHotIPManager(cache *Cache, logger *zap.Logger) *HotIPManager {
	manager := &HotIPManager{
		cache:  cache,
		logger: logger,
		config: HotIPConfig{
			Enabled:              true,
			MinQueryCount:        5,
			TimeWindow:           24 * time.Hour,
			MaxHotIPs:            1000,
			CacheTTL:             6 * time.Hour,
			UpdateInterval:       5 * time.Minute,
			PrewarmEnabled:       true,
			PrewarmBatchSize:     50,
			StatsPersistInterval: 10 * time.Minute,
		},
	}

	// 初始化统计数据
	manager.stats.QueryCounts = make(map[string]int64)
	manager.stats.LastAccess = make(map[string]time.Time)
	manager.stats.FirstAccess = make(map[string]time.Time)
	manager.stats.HotIPs = make([]string, 0)
	manager.stats.LastUpdate = time.Now()

	// 启动后台任务
	if manager.config.Enabled {
		go manager.startBackgroundTasks()
	}

	return manager
}

// RecordAccess 记录IP访问
func (him *HotIPManager) RecordAccess(ip string) {
	if !him.config.Enabled {
		return
	}

	him.stats.Lock()
	defer him.stats.Unlock()

	now := time.Now()

	// 增加查询计数
	him.stats.QueryCounts[ip]++

	// 更新访问时间
	him.stats.LastAccess[ip] = now

	// 设置首次访问时间
	if _, exists := him.stats.FirstAccess[ip]; !exists {
		him.stats.FirstAccess[ip] = now
	}

	him.logger.Debug("Recorded IP access",
		zap.String("ip", ip),
		zap.Int64("count", him.stats.QueryCounts[ip]))
}

// IsHotIP 检查是否为热门IP
func (him *HotIPManager) IsHotIP(ip string) bool {
	if !him.config.Enabled {
		return false
	}

	him.stats.RLock()
	defer him.stats.RUnlock()

	for _, hotIP := range him.stats.HotIPs {
		if hotIP == ip {
			return true
		}
	}
	return false
}

// GetHotIPs 获取热门IP列表
func (him *HotIPManager) GetHotIPs() []HotIPInfo {
	if !him.config.Enabled {
		return nil
	}

	him.stats.RLock()
	defer him.stats.RUnlock()

	var hotIPs []HotIPInfo
	now := time.Now()

	for ip, count := range him.stats.QueryCounts {
		lastAccess := him.stats.LastAccess[ip]
		firstAccess := him.stats.FirstAccess[ip]

		// 检查是否在时间窗口内
		if now.Sub(lastAccess) > him.config.TimeWindow {
			continue
		}

		// 检查是否达到最小查询次数
		if count < him.config.MinQueryCount {
			continue
		}

		// 计算热度分数
		score := him.calculateHotScore(count, firstAccess, lastAccess, now)

		hotIPs = append(hotIPs, HotIPInfo{
			IP:          ip,
			QueryCount:  count,
			LastAccess:  lastAccess,
			FirstAccess: firstAccess,
			Score:       score,
		})
	}

	// 按分数排序
	sort.Slice(hotIPs, func(i, j int) bool {
		return hotIPs[i].Score > hotIPs[j].Score
	})

	// 限制数量
	if len(hotIPs) > him.config.MaxHotIPs {
		hotIPs = hotIPs[:him.config.MaxHotIPs]
	}

	return hotIPs
}

// calculateHotScore 计算热度分数
func (him *HotIPManager) calculateHotScore(count int64, firstAccess, lastAccess, now time.Time) float64 {
	// 基础分数：查询次数
	baseScore := float64(count)

	// 时间衰减因子：最近访问的权重更高
	timeSinceLastAccess := now.Sub(lastAccess).Hours()
	timeDecay := 1.0 / (1.0 + timeSinceLastAccess/24.0) // 24小时衰减

	// 频率因子：单位时间内的查询频率
	duration := lastAccess.Sub(firstAccess).Hours()
	if duration == 0 {
		duration = 1 // 避免除零
	}
	frequency := float64(count) / duration

	// 综合分数
	score := baseScore * timeDecay * (1.0 + frequency/10.0)

	return score
}

// UpdateHotIPs 更新热门IP列表
func (him *HotIPManager) UpdateHotIPs() {
	if !him.config.Enabled {
		return
	}

	hotIPs := him.GetHotIPs()

	him.stats.Lock()
	him.stats.HotIPs = make([]string, 0, len(hotIPs))
	for _, hotIP := range hotIPs {
		him.stats.HotIPs = append(him.stats.HotIPs, hotIP.IP)
	}
	him.stats.LastUpdate = time.Now()
	him.stats.Unlock()

	him.logger.Info("Updated hot IPs list",
		zap.Int("count", len(hotIPs)),
		zap.Time("last_update", him.stats.LastUpdate))

	// 如果启用预热，执行预热操作
	if him.config.PrewarmEnabled {
		go him.prewarmHotIPs(hotIPs)
	}
}

// prewarmHotIPs 预热热门IP
func (him *HotIPManager) prewarmHotIPs(hotIPs []HotIPInfo) {
	ctx := context.Background()

	for i := 0; i < len(hotIPs); i += him.config.PrewarmBatchSize {
		end := i + him.config.PrewarmBatchSize
		if end > len(hotIPs) {
			end = len(hotIPs)
		}

		batch := hotIPs[i:end]
		him.prewarmBatch(ctx, batch)

		// 避免过于频繁的预热操作
		time.Sleep(100 * time.Millisecond)
	}
}

// prewarmBatch 预热一批IP
func (him *HotIPManager) prewarmBatch(ctx context.Context, batch []HotIPInfo) {
	for _, hotIP := range batch {
		// 检查是否已经在缓存中
		if _, err := him.cache.Get(ctx, hotIP.IP); err == nil {
			continue // 已经在缓存中
		}

		// 这里应该调用数据库查询并缓存结果
		// 由于我们在cache包中，无法直接访问数据库
		// 实际实现时应该通过回调函数或接口来实现
		him.logger.Debug("IP needs prewarming", zap.String("ip", hotIP.IP))
	}
}

// CleanupExpiredStats 清理过期统计数据
func (him *HotIPManager) CleanupExpiredStats() {
	if !him.config.Enabled {
		return
	}

	him.stats.Lock()
	defer him.stats.Unlock()

	now := time.Now()
	expiredIPs := make([]string, 0)

	for ip, lastAccess := range him.stats.LastAccess {
		if now.Sub(lastAccess) > him.config.TimeWindow*2 { // 保留2倍时间窗口的数据
			expiredIPs = append(expiredIPs, ip)
		}
	}

	// 删除过期数据
	for _, ip := range expiredIPs {
		delete(him.stats.QueryCounts, ip)
		delete(him.stats.LastAccess, ip)
		delete(him.stats.FirstAccess, ip)
	}

	if len(expiredIPs) > 0 {
		him.logger.Info("Cleaned up expired IP stats", zap.Int("count", len(expiredIPs)))
	}
}

// GetStats 获取热门IP统计信息
func (him *HotIPManager) GetStats() map[string]interface{} {
	him.stats.RLock()
	defer him.stats.RUnlock()

	return map[string]interface{}{
		"enabled":           him.config.Enabled,
		"total_tracked_ips": len(him.stats.QueryCounts),
		"hot_ips_count":     len(him.stats.HotIPs),
		"last_update":       him.stats.LastUpdate,
		"config":            him.config,
	}
}

// startBackgroundTasks 启动后台任务
func (him *HotIPManager) startBackgroundTasks() {
	// 更新热门IP列表
	updateTicker := time.NewTicker(him.config.UpdateInterval)
	go func() {
		for range updateTicker.C {
			him.UpdateHotIPs()
		}
	}()

	// 清理过期统计数据
	cleanupTicker := time.NewTicker(him.config.TimeWindow / 4) // 每1/4时间窗口清理一次
	go func() {
		for range cleanupTicker.C {
			him.CleanupExpiredStats()
		}
	}()

	// 持久化统计数据
	persistTicker := time.NewTicker(him.config.StatsPersistInterval)
	go func() {
		for range persistTicker.C {
			him.persistStats()
		}
	}()

	him.logger.Info("Hot IP manager background tasks started")
}

// persistStats 持久化统计数据到Redis
func (him *HotIPManager) persistStats() {
	ctx := context.Background()

	him.stats.RLock()
	statsData := map[string]interface{}{
		"query_counts": him.stats.QueryCounts,
		"last_access":  him.stats.LastAccess,
		"first_access": him.stats.FirstAccess,
		"hot_ips":      him.stats.HotIPs,
		"last_update":  him.stats.LastUpdate,
	}
	him.stats.RUnlock()

	data, err := json.Marshal(statsData)
	if err != nil {
		him.logger.Error("Failed to marshal hot IP stats", zap.Error(err))
		return
	}

	key := "hot_ip_stats"
	if err := him.cache.client.Set(ctx, key, data, 24*time.Hour).Err(); err != nil {
		him.logger.Error("Failed to persist hot IP stats", zap.Error(err))
	} else {
		him.logger.Debug("Hot IP stats persisted successfully")
	}
}

// LoadStats 从Redis加载统计数据
func (him *HotIPManager) LoadStats() error {
	ctx := context.Background()
	key := "hot_ip_stats"

	data, err := him.cache.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			him.logger.Info("No persisted hot IP stats found, starting fresh")
			return nil
		}
		return err
	}

	var statsData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &statsData); err != nil {
		return err
	}

	him.stats.Lock()
	defer him.stats.Unlock()

	// 恢复统计数据
	if queryCounts, ok := statsData["query_counts"].(map[string]interface{}); ok {
		for ip, count := range queryCounts {
			if countFloat, ok := count.(float64); ok {
				him.stats.QueryCounts[ip] = int64(countFloat)
			}
		}
	}

	// 恢复其他数据...
	// 这里简化处理，实际应该完整恢复所有字段

	him.logger.Info("Hot IP stats loaded successfully",
		zap.Int("tracked_ips", len(him.stats.QueryCounts)))

	return nil
}
