package database

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// OptimizedDatabaseAdapter 优化数据库适配器
// 将OptimizedDatabase适配为DatabaseInterface接口
type OptimizedDatabaseAdapter struct {
	optimizedDB *OptimizedDatabase
	logger      *zap.Logger
	pool        *pgxpool.Pool
}

// NewOptimizedDatabaseAdapter 创建优化数据库适配器
func NewOptimizedDatabaseAdapter(dbConfig config.DatabaseConfig, logger *zap.Logger) (*OptimizedDatabaseAdapter, error) {
	// 创建连接池
	connStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		dbConfig.Host, dbConfig.Port, dbConfig.User, dbConfig.Password, dbConfig.DBName)

	pool, err := pgxpool.New(context.Background(), connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// 创建OptimizedDatabase实例
	optimizedDB := NewOptimizedDatabase(pool, logger)

	return &OptimizedDatabaseAdapter{
		optimizedDB: optimizedDB,
		logger:      logger,
		pool:        pool,
	}, nil
}

// GetPool 获取数据库连接池
func (a *OptimizedDatabaseAdapter) GetPool() *pgxpool.Pool {
	return a.pool
}

// Query 查询IP信息
func (a *OptimizedDatabaseAdapter) Query(ctx context.Context, ip string) (*model.IPInfo, error) {
	return a.optimizedDB.Query(ctx, ip)
}

// Insert 插入IP信息
func (a *OptimizedDatabaseAdapter) Insert(ctx context.Context, ipInfo model.IPInfo) error {
	return a.optimizedDB.Insert(ctx, ipInfo)
}

// BatchUpsertIPs 批量更新IP信息（优化版本）
func (a *OptimizedDatabaseAdapter) BatchUpsertIPs(ctx context.Context, ipInfos []model.IPInfo) (*BatchResult, error) {
	startTime := time.Now()

	// 根据数据量选择最佳插入策略，添加重试机制处理临时性数据库问题
	var err error
	maxRetries := 3
	for retry := 0; retry <= maxRetries; retry++ {
		if len(ipInfos) > 100 {
			// 大量数据使用快速批量插入
			err = a.optimizedDB.BatchInsertFast(ctx, ipInfos)
		} else {
			// 少量数据使用普通批量插入
			err = a.optimizedDB.BatchInsert(ctx, ipInfos, 100)
		}

		// 检查是否是临时性数据库恢复错误
		if err != nil && strings.Contains(err.Error(), "database system is in recovery mode") {
			if retry < maxRetries {
				waitTime := time.Duration(retry+1) * 5 * time.Second
				a.logger.Warn("Database is in recovery mode, retrying",
					zap.Int("retry", retry+1),
					zap.Int("max_retries", maxRetries),
					zap.Duration("wait_time", waitTime),
					zap.Error(err))

				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				case <-time.After(waitTime):
					continue
				}
			}
		} else {
			// 成功或其他类型的错误，退出重试循环
			break
		}
	}

	duration := time.Since(startTime)

	if err != nil {
		a.logger.Error("Batch upsert failed",
			zap.Int("total", len(ipInfos)),
			zap.Duration("duration", duration),
			zap.Error(err))

		return &BatchResult{
			TotalRecords:   len(ipInfos),
			SuccessRecords: 0,
			FailedRecords:  len(ipInfos),
			Errors:         []string{err.Error()},
			Duration:       duration,
		}, err
	}

	result := &BatchResult{
		TotalRecords:   len(ipInfos),
		SuccessRecords: len(ipInfos),
		FailedRecords:  0,
		Errors:         nil,
		Duration:       duration,
	}

	a.logger.Debug("Batch upsert completed successfully",
		zap.Int("total", len(ipInfos)),
		zap.Duration("duration", duration),
		zap.Float64("rate_per_sec", float64(len(ipInfos))/duration.Seconds()))

	return result, nil
}

func (a *OptimizedDatabaseAdapter) BatchInsert(ctx context.Context, ipInfos []model.IPInfo, batchSize int) error {
	return a.optimizedDB.BatchInsert(ctx, ipInfos, batchSize)
}

func (a *OptimizedDatabaseAdapter) Initialize(ctx context.Context) error {
	// 检查新表是否存在
	var exists bool
	err := a.pool.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT FROM information_schema.tables
			WHERE table_schema = 'public'
			AND table_name = 'ip_ranges_new'
		);
	`).Scan(&exists)

	if err != nil {
		return fmt.Errorf("failed to check table existence: %w", err)
	}

	if !exists {
		return fmt.Errorf("optimized database tables not found, please run schema deployment first")
	}

	return nil
}

func (a *OptimizedDatabaseAdapter) Close() {
	if a.pool != nil {
		a.pool.Close()
		a.logger.Info("Optimized database connection closed")
	}
}

func (a *OptimizedDatabaseAdapter) Ping(ctx context.Context) error {
	return a.pool.Ping(ctx)
}

func (a *OptimizedDatabaseAdapter) GetStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	var recordCount int64
	err := a.pool.QueryRow(ctx, "SELECT COUNT(*) FROM ip_ranges_new").Scan(&recordCount)
	if err == nil {
		stats["record_count"] = recordCount
	}

	// 获取表大小
	var tableSize string
	err = a.pool.QueryRow(ctx, "SELECT pg_size_pretty(pg_total_relation_size('ip_ranges_new'))").Scan(&tableSize)
	if err == nil {
		stats["table_size"] = tableSize
	}

	// 获取索引信息
	var indexCount int64
	err = a.pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM pg_indexes 
		WHERE tablename = 'ip_ranges_new'
	`).Scan(&indexCount)
	if err == nil {
		stats["index_count"] = indexCount
	}

	// 获取连接池状态
	poolStats := a.pool.Stat()
	stats["connection_pool"] = map[string]interface{}{
		"total_connections":    poolStats.TotalConns(),
		"acquired_connections": poolStats.AcquiredConns(),
		"idle_connections":     poolStats.IdleConns(),
	}

	stats["database_type"] = "optimized"
	stats["timestamp"] = time.Now().Unix()

	return stats
}

// DatabaseFactory 数据库工厂实现
type DatabaseFactoryImpl struct {
	logger *zap.Logger
}

// NewDatabaseFactory 创建数据库工厂
func NewDatabaseFactory(logger *zap.Logger) *DatabaseFactoryImpl {
	return &DatabaseFactoryImpl{
		logger: logger,
	}
}

// CreateDatabase 根据配置创建数据库实例
func (f *DatabaseFactoryImpl) CreateDatabase(dbConfig config.DatabaseConfig, config DatabaseConfig) (DatabaseInterface, error) {
	switch config.Type {
	case DatabaseTypeLegacy:
		f.logger.Warn("Legacy database type is no longer supported, using optimized database instead")
		return NewOptimizedDatabaseAdapter(dbConfig, f.logger)
	case DatabaseTypeOptimized:
		return NewOptimizedDatabaseAdapter(dbConfig, f.logger)
	default:
		return nil, fmt.Errorf("unsupported database type: %s", config.Type)
	}
}

// GetSupportedTypes 获取支持的数据库类型
func (f *DatabaseFactoryImpl) GetSupportedTypes() []DatabaseType {
	return []DatabaseType{
		DatabaseTypeOptimized, // 只支持优化数据库
	}
}
