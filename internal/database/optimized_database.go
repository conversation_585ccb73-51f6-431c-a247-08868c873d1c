package database

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"strings"

	"github.com/cosin2077/ipInsight/internal/model"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// OptimizedDatabase 优化的数据库访问层，支持规范化表结构
type OptimizedDatabase struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewOptimizedDatabase 创建优化的数据库实例
func NewOptimizedDatabase(pool *pgxpool.Pool, logger *zap.Logger) *OptimizedDatabase {
	return &OptimizedDatabase{
		pool:   pool,
		logger: logger,
	}
}

// Insert 插入IP信息到规范化表结构（优化版本）
func (od *OptimizedDatabase) Insert(ctx context.Context, ipInfo model.IPInfo) error {
	// 预构建JSONB字段，避免重复序列化
	geoExt := od.buildGeolocationExtended(ipInfo)
	netExt := od.buildNetworkExtended(ipInfo)
	secExt := od.buildSecurityExtended(ipInfo)
	extData := od.buildExtendedData(ipInfo)

	var ipRangeID int64
	err := od.pool.QueryRow(ctx, `
		INSERT INTO ip_ranges_new (
			ip_range, start_ip_int, end_ip_int, ip_version,
			country_code, country_name, city, isp, asn, source,
			geolocation_extended, network_extended, security_extended, extended_data
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
		)
		ON CONFLICT (ip_range) DO UPDATE SET
			country_code = EXCLUDED.country_code,
			country_name = EXCLUDED.country_name,
			city = EXCLUDED.city,
			isp = EXCLUDED.isp,
			asn = EXCLUDED.asn,
			source = EXCLUDED.source,
			updated_at = NOW()
		RETURNING id`,
		ipInfo.IPRange.CIDR,
		*od.ipToInt(ipInfo.IPRange.StartIP, ipInfo.IPRange.IPVersion),
		*od.ipToInt(ipInfo.IPRange.EndIP, ipInfo.IPRange.IPVersion),
		od.ipVersionToInt(ipInfo.IPRange.IPVersion),
		ipInfo.Geolocation.Country.Code,
		ipInfo.Geolocation.Country.Name,
		ipInfo.Geolocation.City,
		ipInfo.Network.ISP,
		ipInfo.Network.ASN,
		ipInfo.Metadata.Source,
		geoExt,
		netExt,
		secExt,
		extData,
	).Scan(&ipRangeID)

	if err != nil {
		return fmt.Errorf("failed to insert main record: %w", err)
	}

	return nil
}

// BatchInsert 真正的批量插入实现
func (od *OptimizedDatabase) BatchInsert(ctx context.Context, ipInfos []model.IPInfo, batchSize int) error {
	if len(ipInfos) == 0 {
		return nil
	}

	// 默认批次大小
	if batchSize <= 0 {
		batchSize = 1000
	}

	// 分批处理
	for i := 0; i < len(ipInfos); i += batchSize {
		end := i + batchSize
		if end > len(ipInfos) {
			end = len(ipInfos)
		}

		batch := ipInfos[i:end]
		if err := od.batchInsertChunk(ctx, batch); err != nil {
			return fmt.Errorf("failed to insert batch %d-%d: %w", i, end-1, err)
		}
	}

	return nil
}

// BatchInsertFast 使用VALUES批量插入的高性能实现
func (od *OptimizedDatabase) BatchInsertFast(ctx context.Context, ipInfos []model.IPInfo) error {
	if len(ipInfos) == 0 {
		return nil
	}

	// 使用VALUES语句进行批量插入
	return od.batchInsertWithValues(ctx, ipInfos)
}

// batchInsertWithValues 使用VALUES语句进行高效批量插入
func (od *OptimizedDatabase) batchInsertWithValues(ctx context.Context, ipInfos []model.IPInfo) error {
	const batchSize = 1000 // VALUES方式的最佳批次大小

	for i := 0; i < len(ipInfos); i += batchSize {
		end := i + batchSize
		if end > len(ipInfos) {
			end = len(ipInfos)
		}

		batch := ipInfos[i:end]
		if err := od.batchInsertValuesChunk(ctx, batch); err != nil {
			return fmt.Errorf("failed to insert values batch %d-%d: %w", i, end-1, err)
		}
	}

	return nil
}

// batchInsertValuesChunk 使用VALUES语句执行单个批次插入
func (od *OptimizedDatabase) batchInsertValuesChunk(ctx context.Context, ipInfos []model.IPInfo) error {
	if len(ipInfos) == 0 {
		return nil
	}

	// 去重处理：同一个批次中如果有重复的CIDR，只保留最后一个
	uniqueIPs := make(map[string]model.IPInfo)
	for _, ipInfo := range ipInfos {
		if ipInfo.IPRange.CIDR != "" {
			uniqueIPs[ipInfo.IPRange.CIDR] = ipInfo
		}
	}

	// 转换回切片
	deduplicatedIPs := make([]model.IPInfo, 0, len(uniqueIPs))
	for _, ipInfo := range uniqueIPs {
		deduplicatedIPs = append(deduplicatedIPs, ipInfo)
	}

	if len(deduplicatedIPs) == 0 {
		return nil
	}

	// 构建VALUES子句
	valueStrings := make([]string, 0, len(deduplicatedIPs))
	valueArgs := make([]interface{}, 0, len(deduplicatedIPs)*14)

	for i, ipInfo := range deduplicatedIPs {
		// 预计算JSONB字段
		geoExt := od.buildGeolocationExtended(ipInfo)
		netExt := od.buildNetworkExtended(ipInfo)
		secExt := od.buildSecurityExtended(ipInfo)
		extData := od.buildExtendedData(ipInfo)

		// 构建VALUES参数占位符
		baseIndex := i * 14
		valueStrings = append(valueStrings, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			baseIndex+1, baseIndex+2, baseIndex+3, baseIndex+4,
			baseIndex+5, baseIndex+6, baseIndex+7, baseIndex+8,
			baseIndex+9, baseIndex+10, baseIndex+11, baseIndex+12,
			baseIndex+13, baseIndex+14))

		// 添加参数
		startIPInt := od.ipToInt(ipInfo.IPRange.StartIP, ipInfo.IPRange.IPVersion)
		endIPInt := od.ipToInt(ipInfo.IPRange.EndIP, ipInfo.IPRange.IPVersion)

		valueArgs = append(valueArgs,
			ipInfo.IPRange.CIDR,
			*startIPInt, // 解引用指针
			*endIPInt,   // 解引用指针
			od.ipVersionToInt(ipInfo.IPRange.IPVersion),
			ipInfo.Geolocation.Country.Code,
			ipInfo.Geolocation.Country.Name,
			ipInfo.Geolocation.City,
			ipInfo.Network.ISP,
			ipInfo.Network.ASN,
			ipInfo.Metadata.Source,
			geoExt,
			netExt,
			secExt,
			extData,
		)
		// 修复：不能对interface{}类型直接使用len，需要断言为可取长度的类型
		// for _, a := range valueArgs {
		// 	switch v := a.(type) {
		// 	case string:
		// 		if len(v) > 300 {
		// 			fmt.Println(v)
		// 		}
		// 	case []byte:
		// 		if len(v) > 300 {
		// 			fmt.Println(v)
		// 		}
		// 		// 可以根据需要添加其他类型
		// 	}
		// }
	}

	// 构建完整的SQL语句
	query := fmt.Sprintf(`
		INSERT INTO ip_ranges_new (
			ip_range, start_ip_int, end_ip_int, ip_version,
			country_code, country_name, city, isp, asn, source,
			geolocation_extended, network_extended, security_extended, extended_data
		) VALUES %s
		ON CONFLICT (ip_range) DO UPDATE SET
			country_code = EXCLUDED.country_code,
			country_name = EXCLUDED.country_name,
			city = EXCLUDED.city,
			isp = EXCLUDED.isp,
			asn = EXCLUDED.asn,
			source = EXCLUDED.source,
			updated_at = NOW()
	`, strings.Join(valueStrings, ", "))

	// 执行批量插入
	_, err := od.pool.Exec(ctx, query, valueArgs...)
	if err != nil {
		return fmt.Errorf("failed to execute batch insert: %w", err)
	}

	od.logger.Debug("Successfully inserted values batch",
		zap.Int("original_count", len(ipInfos)),
		zap.Int("deduplicated_count", len(deduplicatedIPs)))

	return nil
}

// batchInsertChunk 执行单个批次的插入
func (od *OptimizedDatabase) batchInsertChunk(ctx context.Context, ipInfos []model.IPInfo) error {
	if len(ipInfos) == 0 {
		return nil
	}

	// 使用事务提升性能
	tx, err := od.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// 预处理语句
	stmt := `
		INSERT INTO ip_ranges_new (
			ip_range, start_ip_int, end_ip_int, ip_version,
			country_code, country_name, city, isp, asn, source,
			geolocation_extended, network_extended, security_extended, extended_data
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		ON CONFLICT (ip_range) DO UPDATE SET
			country_code = EXCLUDED.country_code,
			country_name = EXCLUDED.country_name,
			city = EXCLUDED.city,
			isp = EXCLUDED.isp,
			asn = EXCLUDED.asn,
			source = EXCLUDED.source,
			updated_at = NOW()`

	// 批量执行
	for _, ipInfo := range ipInfos {
		_, err := tx.Exec(ctx, stmt,
			ipInfo.IPRange.CIDR,
			*od.ipToInt(ipInfo.IPRange.StartIP, ipInfo.IPRange.IPVersion),
			*od.ipToInt(ipInfo.IPRange.EndIP, ipInfo.IPRange.IPVersion),
			od.ipVersionToInt(ipInfo.IPRange.IPVersion),
			ipInfo.Geolocation.Country.Code,
			ipInfo.Geolocation.Country.Name,
			ipInfo.Geolocation.City,
			ipInfo.Network.ISP,
			ipInfo.Network.ASN,
			ipInfo.Metadata.Source,
			od.buildGeolocationExtended(ipInfo),
			od.buildNetworkExtended(ipInfo),
			od.buildSecurityExtended(ipInfo),
			od.buildExtendedData(ipInfo),
		)
		if err != nil {
			return fmt.Errorf("failed to insert record %s: %w", ipInfo.IPRange.CIDR, err)
		}
	}

	// 提交事务
	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	od.logger.Debug("Successfully inserted batch",
		zap.Int("count", len(ipInfos)))

	return nil
}

// Query 查询IP信息（简化版本）
func (od *OptimizedDatabase) Query(ctx context.Context, ip string) (*model.IPInfo, error) {
	var ipInfo model.IPInfo
	var geolocationExtended, networkExtended, securityExtended, extendedData []byte
	var cidrText string

	err := od.pool.QueryRow(ctx, `
		SELECT
			ip_range::text, start_ip_int, end_ip_int, ip_version,
			country_code, country_name, city, isp, asn, source,
			geolocation_extended, network_extended, security_extended, extended_data
		FROM ip_ranges_new
		WHERE ip_range >> $1::inet
		ORDER BY masklen(ip_range) DESC
		LIMIT 1
	`, ip).Scan(
		&cidrText,
		&ipInfo.IPRange.StartIPInt,
		&ipInfo.IPRange.EndIPInt,
		&ipInfo.IPRange.IPVersion,
		&ipInfo.Geolocation.Country.Code,
		&ipInfo.Geolocation.Country.Name,
		&ipInfo.Geolocation.City,
		&ipInfo.Network.ISP,
		&ipInfo.Network.ASN,
		&ipInfo.Metadata.Source,
		&geolocationExtended,
		&networkExtended,
		&securityExtended,
		&extendedData,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to query IP: %w", err)
	}

	// 将CIDR文本赋值给结构体
	ipInfo.IPRange.CIDR = cidrText

	// 解析JSONB扩展字段
	if err := od.parseExtendedFields(&ipInfo, geolocationExtended, networkExtended, securityExtended, extendedData); err != nil {
		od.logger.Warn("Failed to parse extended fields", zap.Error(err))
	}

	return &ipInfo, nil
}

// 辅助方法
func (od *OptimizedDatabase) ipToInt(ipStr, version string) *int64 {
	if ipStr == "" {
		od.logger.Warn("Empty IP string provided, using default value 0")
		result := int64(0)
		return &result
	}

	if version == "IPv4" || version == "4" {
		ip := net.ParseIP(ipStr)
		if ip == nil {
			od.logger.Warn("Invalid IP address format, using default value 0",
				zap.String("ip", ipStr),
				zap.String("ip_hex", fmt.Sprintf("%x", []byte(ipStr))))
			result := int64(0)
			return &result
		}
		ip = ip.To4()
		if ip == nil {
			od.logger.Warn("Cannot convert to IPv4, using default value 0",
				zap.String("ip", ipStr))
			result := int64(0)
			return &result
		}
		result := int64(ip[0])<<24 + int64(ip[1])<<16 + int64(ip[2])<<8 + int64(ip[3])
		// 添加调试日志
		if result == 0 {
			od.logger.Debug("IP converted to 0",
				zap.String("ip", ipStr),
				zap.String("version", version),
				zap.Int64("result", result))
		}
		return &result
	}

	if version == "IPv6" || version == "6" {
		ip := net.ParseIP(ipStr)
		if ip == nil {
			od.logger.Warn("Invalid IPv6 address format, using default value 0",
				zap.String("ip", ipStr))
			result := int64(0)
			return &result
		}
		ip = ip.To16()
		if ip == nil {
			od.logger.Warn("Cannot convert to IPv6, using default value 0",
				zap.String("ip", ipStr))
			result := int64(0)
			return &result
		}
		// 对于 IPv6，我们取前8个字节作为 int64
		// 这不是完整的 IPv6 表示，但满足数据库约束
		result := int64(0)
		for i := 0; i < 8 && i < len(ip); i++ {
			result = (result << 8) | int64(ip[i])
		}
		return &result
	}

	// 未知版本，返回0
	od.logger.Warn("Unknown IP version, using default value 0",
		zap.String("version", version),
		zap.String("ip", ipStr))
	result := int64(0)
	return &result
}

func (od *OptimizedDatabase) ipVersionToInt(version string) int {
	if version == "IPv6" || version == "6" {
		return 6
	}
	return 4
}

func (od *OptimizedDatabase) parseTimeString(timeStr *string) interface{} {
	if timeStr == nil || *timeStr == "" {
		return nil
	}
	return *timeStr
}

func (od *OptimizedDatabase) buildGeolocationExtended(ipInfo model.IPInfo) []byte {
	if len(ipInfo.Geolocation.Subdivisions) == 0 && ipInfo.Geolocation.Elevation == nil {
		return nil
	}

	data := map[string]interface{}{}
	if len(ipInfo.Geolocation.Subdivisions) > 0 {
		data["subdivisions"] = ipInfo.Geolocation.Subdivisions
	}
	if ipInfo.Geolocation.Elevation != nil {
		data["elevation"] = ipInfo.Geolocation.Elevation
	}

	result, _ := json.Marshal(data)
	return result
}

func (od *OptimizedDatabase) buildNetworkExtended(ipInfo model.IPInfo) []byte {
	data := map[string]interface{}{}
	if ipInfo.Network.MCC != "" {
		data["mcc"] = ipInfo.Network.MCC
	}
	if ipInfo.Network.MNC != "" {
		data["mnc"] = ipInfo.Network.MNC
	}
	if ipInfo.Network.AutonomousSystemID != nil {
		data["autonomous_system_id"] = ipInfo.Network.AutonomousSystemID
	}

	if len(data) == 0 {
		return nil
	}

	result, _ := json.Marshal(data)
	return result
}

func (od *OptimizedDatabase) buildSecurityExtended(ipInfo model.IPInfo) []byte {
	data := map[string]interface{}{}
	if ipInfo.Security.IsAnonymous != nil {
		data["is_anonymous"] = ipInfo.Security.IsAnonymous
	}

	if len(data) == 0 {
		return nil
	}

	result, _ := json.Marshal(data)
	return result
}

func (od *OptimizedDatabase) buildExtendedData(ipInfo model.IPInfo) []byte {
	result, _ := json.Marshal(ipInfo.Extended)
	return result
}

// 关联表插入方法暂时移除，简化实现

func (od *OptimizedDatabase) parseExtendedFields(ipInfo *model.IPInfo, geoExt, netExt, secExt, extData []byte) error {
	// 解析地理位置扩展字段
	if len(geoExt) > 0 {
		var geoExtended map[string]interface{}
		if err := json.Unmarshal(geoExt, &geoExtended); err == nil {
			if subdivisions, ok := geoExtended["subdivisions"]; ok {
				// 处理subdivisions
				_ = subdivisions
			}
			if elevation, ok := geoExtended["elevation"]; ok {
				if elevFloat, ok := elevation.(float64); ok {
					ipInfo.Geolocation.Elevation = &elevFloat
				}
			}
		}
	}

	// 解析网络扩展字段
	if len(netExt) > 0 {
		var netExtended map[string]interface{}
		if err := json.Unmarshal(netExt, &netExtended); err == nil {
			if mcc, ok := netExtended["mcc"].(string); ok {
				ipInfo.Network.MCC = mcc
			}
			if mnc, ok := netExtended["mnc"].(string); ok {
				ipInfo.Network.MNC = mnc
			}
		}
	}

	// 解析安全扩展字段
	if len(secExt) > 0 {
		var secExtended map[string]interface{}
		if err := json.Unmarshal(secExt, &secExtended); err == nil {
			if isAnon, ok := secExtended["is_anonymous"].(bool); ok {
				ipInfo.Security.IsAnonymous = &isAnon
			}
		}
	}

	// 解析扩展数据
	if len(extData) > 0 {
		json.Unmarshal(extData, &ipInfo.Extended)
	}

	return nil
}
