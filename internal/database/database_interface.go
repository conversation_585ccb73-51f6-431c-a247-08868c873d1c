package database

import (
	"context"
	"time"

	"github.com/cosin2077/ipInsight/internal/model"
)

// BatchResult 批量操作结果
type BatchResult struct {
	TotalRecords   int           `json:"total_records"`
	SuccessRecords int           `json:"success_records"`
	FailedRecords  int           `json:"failed_records"`
	Errors         []string      `json:"errors,omitempty"`
	Duration       time.Duration `json:"duration"`
}

// DatabaseInterface 定义统一的数据库访问接口
// 新旧数据库实现都必须遵循此接口，确保替换过程的兼容性
type DatabaseInterface interface {
	// 基础查询操作
	Query(ctx context.Context, ip string) (*model.IPInfo, error)
	Insert(ctx context.Context, ipInfo model.IPInfo) error

	// 批量操作
	BatchUpsertIPs(ctx context.Context, ipInfos []model.IPInfo) (*BatchResult, error)
	BatchInsert(ctx context.Context, ipInfos []model.IPInfo, batchSize int) error // 新增高性能批量插入

	// 系统操作
	Initialize(ctx context.Context) error
	Close()
	Ping(ctx context.Context) error

	// 统计信息
	GetStats(ctx context.Context) map[string]interface{}
}

// DatabaseType 数据库类型枚举
type DatabaseType string

const (
	DatabaseTypeLegacy    DatabaseType = "legacy"    // 旧的JSONB架构
	DatabaseTypeOptimized DatabaseType = "optimized" // 新的规范化架构
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type DatabaseType `yaml:"type" json:"type"`
}

// DatabaseFactory 数据库工厂接口
type DatabaseFactory interface {
	CreateDatabase(config DatabaseConfig) (DatabaseInterface, error)
	GetSupportedTypes() []DatabaseType
}
