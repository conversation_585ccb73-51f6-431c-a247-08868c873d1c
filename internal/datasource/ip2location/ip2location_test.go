package ip2location

import (
	"context"
	"log"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/cosin2077/ipInsight/internal/utils"
)

func TestIP2LocationParse(t *testing.T) {
	logger, _ := utils.GetLogger()
	defer logger.Sync()

	logger.Info("staring to LoadConfig ...")
	cfg, err := config.LoadConfig(logger)

	if err != nil {
		log.Fatal(err)
	}
	// 创建测试数据目录
	// dataDir := "./testdata"
	// os.MkdirAll(dataDir, 0755)
	// defer os.RemoveAll(dataDir)

	// // 模拟 IP2LOCATION-LITE-DB3.CSV
	// csvContent := `16777216,16777471,"AU","Australia","Queensland","Brisbane","-27.4705","153.0260"`
	// csvFile := filepath.Join(dataDir, "IP2LOCATION-LITE-DB3.CSV")
	// os.WriteFile(csvFile, []byte(csvContent), 0644)

	// 需要提供数据库适配器参数，但测试中暂时使用nil
	ds := NewIP2Location(cfg.Datasources.IP2Location, logger, nil)
	ctx := context.Background()
	ds.Fetch(ctx)
	// ds.Update(context.Background(), []model.IPInfo{})
}
