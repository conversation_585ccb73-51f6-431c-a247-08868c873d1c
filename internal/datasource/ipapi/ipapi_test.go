package ipapi

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestIPAPIParse(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	dataDir := "./testdata"
	os.MkdirAll(dataDir, 0755)
	defer os.RemoveAll(dataDir)

	csvContent := `16777216,16777471,"AU","Australia","Brisbane","-27.4705","153.0260"`
	csvFile := filepath.Join(dataDir, "ipapi.csv")
	os.WriteFile(csvFile, []byte(csvContent), 0644)

	ipapi := &IPAPI{
		config:  config.DatasourceConfig{URL: []string{}, Schedule: ""},
		logger:  logger,
		dataDir: dataDir,
	}

	ipInfos, err := ipapi.Parse(context.Background())
	assert.NoError(t, err)
	assert.Len(t, ipInfos, 1)

	ipInfo := ipInfos[0]
	assert.Equal(t, "*******", ipInfo.IPRange.StartIP)
	assert.Equal(t, "*********", ipInfo.IPRange.EndIP)
	assert.Equal(t, "AU", ipInfo.Geolocation.Country.Code)
	assert.Equal(t, "Australia", ipInfo.Geolocation.Country.Name)
	assert.Equal(t, "Brisbane", ipInfo.Geolocation.City)
	assert.Equal(t, -27.4705, ipInfo.Geolocation.Latitude)
	assert.Equal(t, 153.0260, ipInfo.Geolocation.Longitude)
	assert.Equal(t, "ipapi", ipInfo.Metadata.Source)
}
