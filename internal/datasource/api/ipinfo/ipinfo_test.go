package ipinfo

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/cosin2077/ipInsight/internal/config"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

func TestIPInfoQueryIP(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	// 模拟 ipinfo.io API
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte(`{
            "ip": "*******",
            "city": "Brisbane",
            "region": "Queensland",
            "country": "AU",
            "loc": "-27.4705,153.0260",
            "org": "Cloudflare, Inc.",
            "timezone": "Australia/Brisbane"
        }`))
	}))
	defer server.Close()

	// 初始化 IPInfo
	ipInfo := &IPInfo{
		config: config.APIConfig{
			URL:       []string{server.URL},
			APIKey:    "test",
			RateLimit: 1000,
		},
		logger:      logger,
		client:      server.Client(),
		rateLimiter: rate.NewLimiter(rate.Inf, 1000), // 无限制
	}

	// 测试 QueryIP
	ipInfoResult, err := ipInfo.QueryIP(context.Background(), "*******")
	assert.NoError(t, err)
	assert.NotNil(t, ipInfoResult)
	assert.Equal(t, "*******/32", ipInfoResult.IPRange.CIDR)
	assert.Equal(t, "AU", ipInfoResult.Geolocation.Country.Code)
	assert.Equal(t, "Queensland", ipInfoResult.Geolocation.Region.Name)
	assert.Equal(t, "Brisbane", ipInfoResult.Geolocation.City)
	assert.Equal(t, -27.4705, ipInfoResult.Geolocation.Latitude)
	assert.Equal(t, 153.0260, ipInfoResult.Geolocation.Longitude)
	assert.Equal(t, "Cloudflare, Inc.", ipInfoResult.Network.Organization)
	assert.Equal(t, "Australia/Brisbane", ipInfoResult.Timezone.Name)
	assert.Equal(t, "ipinfo", ipInfoResult.Metadata.Source)
}
