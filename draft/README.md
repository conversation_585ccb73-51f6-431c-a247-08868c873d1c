# ipInsight Dashboard - 前端管理看板

## 项目概述

ipInsight Dashboard 是 ipInsight IP地理位置查询服务的现代化 React 管理界面，提供系统监控、API统计、数据源管理等功能。采用 React 19 + TypeScript + Tailwind CSS 构建，具备响应式设计和暗色主题支持。

## 技术栈

### 核心技术
- **React** 19.1.0 - 前端框架
- **TypeScript** 5.8 - 类型安全
- **Vite** 7.0 - 构建工具
- **React Router** 7.6 - 路由管理

### UI 框架与样式
- **Tailwind CSS** 4.1 - CSS 框架
- **shadcn/ui** - 现代化组件库
- **Radix UI** - 无障碍组件基础
- **Lucide React** - 图标库
- **Class Variance Authority** - 样式变体管理

### 状态管理与工具
- **Axios** 1.10 - HTTP 客户端
- **React Context** - 状态管理
- **date-fns** - 日期处理
- **Recharts** - 图表组件
- **Sonner** - 通知组件

## 应用结构

### 布局结构

应用采用经典的管理后台布局，包含以下主要部分：

#### 1. 顶部导航栏 (Header)
- **位置**: 固定在页面顶部，支持粘性定位
- **左侧区域**: 
  - 移动端菜单按钮（仅在移动端显示）
  - 桌面端侧边栏折叠/展开按钮
- **右侧区域**:
  - 系统状态指示器（显示"系统正常"或"系统异常"状态）
  - 网络状态按钮
  - 用户头像下拉菜单（包含用户资料和退出登录选项）

#### 2. 左侧边栏 (Sidebar)
- **Logo区域**: 显示 ipInsight 品牌标识和图标
- **导航菜单**: 主要功能导航，支持折叠模式
- **底部信息**: 版本号、版权信息（仅在展开模式显示）
- **响应式支持**: 移动端为滑出式抽屉，桌面端为固定侧边栏

#### 3. 主内容区域 (Main Content)
- **容器**: 最大宽度 7xl，居中布局
- **内边距**: 响应式内边距设计
- **背景**: 浅灰色背景，提供良好的视觉层次

## 路由结构

### 主要路由配置

| 路径 | 组件 | 描述 |
|------|------|------|
| `/` | Navigate to `/dashboard` | 根路径重定向到总览页 |
| `/login` | LoginPage | 用户登录页面 |
| `/dashboard` | DashboardPage | 系统总览仪表板 |
| `/system` | SystemStatusPage | 系统状态监控 |
| `/api-stats` | APIStatsPage | API统计中心 |
| `/data-sources` | DataSourcesPage | 数据源管理 |
| `/api-test` | APITestPage | API接口测试 |
| `/about` | AboutPage | 项目信息介绍 |

### 路由保护
- 除 `/login` 外，所有路由都受 `ProtectedRoute` 组件保护
- 未登录用户将被重定向到登录页面
- 使用 JWT 进行身份验证

## 页面详细说明

### 1. 登录页面 (`/login`)
- **布局**: 居中卡片式登录表单
- **功能**: 
  - 用户名/密码登录
  - 表单验证和错误提示
  - 加载状态指示
  - 默认凭据提示
- **样式**: 现代化卡片设计，带有品牌标识

### 2. 系统总览 (`/dashboard`)
- **统计卡片区域**: 
  - 总查询次数（显示趋势）
  - 缓存命中率（性能指标）
  - 活跃IP数（用户活跃度）
  - 系统状态（健康状况）
- **图表区域**:
  - 查询统计趋势图表（占用 2/3 宽度）
  - 热门IP地址列表（显示查询次数排名）
- **系统信息卡片**:
  - 系统信息（运行时间、内存使用、CPU使用率）
  - 网络状态（API响应时间、数据库连接、Redis缓存）
  - 数据源状态（活跃数据源、最后更新、同步状态）

### 3. 系统状态监控 (`/system`)
- **系统概览卡片**:
  - CPU使用率（带进度条和状态指示）
  - 内存使用率（堆内存详细信息）
  - 磁盘使用率（存储空间监控）
  - 系统运行时间（格式化显示）
- **服务状态表格**:
  - Web服务器状态和端口
  - PostgreSQL数据库状态
  - Redis缓存状态
  - 数据源更新服务状态
- **性能指标面板**:
  - API响应时间
  - 数据库查询时间
  - 缓存命中率
  - 并发连接数

### 4. API统计中心 (`/api-stats`)
- **统计卡片网格**:
  - 总请求数、缓存命中数、成功率
  - 平均响应时间、错误率、数据库查询数
- **API端点统计列表**:
  - 每个端点的详细统计（请求数、响应时间、成功率）
  - HTTP方法和路径显示
  - 性能状态标识（优秀/良好/需要关注）
- **性能趋势图表**:
  - 请求量趋势图表
  - 响应时间趋势图表

### 5. 数据源管理 (`/data-sources`)
- **数据源概览**:
  - 总数据源数、活跃数据源数
  - 异常数据源数、总数据大小
- **数据源状态表格**:
  - 数据源名称、类型、状态
  - 数据大小、记录数、最后更新时间
  - 手动更新操作按钮
- **更新进度显示**:
  - 正在更新的数据源进度条
- **更新确认对话框**:
  - 数据源更新确认和进度提示

### 6. API接口测试 (`/api-test`)
- **测试配置面板**:
  - API端点选择（单IP查询、批量查询、统计信息、健康检查）
  - 参数配置（IP地址、IP地址列表）
  - 测试执行按钮
- **测试结果显示**:
  - 请求成功/失败状态
  - 响应时间显示
  - JSON格式化结果
  - 结果复制功能
- **代码示例**:
  - cURL、JavaScript、Python 示例代码
  - 动态生成基于当前配置
- **API文档链接**:
  - 所有可用端点列表
  - 外部API文档链接

### 7. 关于页面 (`/about`)
- **项目概述**:
  - 核心功能特性介绍
  - 架构亮点说明
- **技术栈展示**:
  - 后端、数据库、缓存、前端技术分类展示
- **API接口列表**:
  - 主要端点和描述
- **项目信息**:
  - 项目特点和优势
  - GitHub仓库链接
  - 问题反馈和文档链接
- **致谢信息**:
  - 数据源提供商
  - 开源项目贡献

## 导航菜单结构

### 左侧边栏菜单项

| 图标 | 名称 | 路径 | 描述 |
|------|------|------|------|
| 📊 | 总览 | `/dashboard` | 系统总体状况和关键指标 |
| 💻 | 系统状态 | `/system` | 服务器性能和系统监控 |
| 📈 | API统计 | `/api-stats` | 接口调用统计和性能分析 |
| 💾 | 数据源管理 | `/data-sources` | IP数据库管理和更新 |
| 🔧 | API测试 | `/api-test` | 在线接口测试工具 |
| ℹ️ | 关于 | `/about` | 项目信息和技术说明 |

### 菜单交互特性
- **活跃状态指示**: 当前页面的菜单项高亮显示
- **折叠模式**: 支持图标模式和完整文本模式
- **工具提示**: 折叠模式下显示菜单项名称
- **响应式导航**: 移动端自动切换为抽屉式菜单

## 主题和样式

### 设计系统
- **颜色方案**: 支持明暗主题切换
- **组件样式**: 基于 shadcn/ui 设计系统
- **响应式设计**: 移动优先的响应式布局
- **动画效果**: 流畅的过渡动画和悬停效果

### 视觉特点
- **毛玻璃效果**: 卡片组件使用 backdrop-blur
- **渐变背景**: 装饰性渐变元素
- **阴影层次**: 多层次阴影提供深度感
- **圆角设计**: 现代化的圆角卡片布局

## 状态管理

### Context 提供者
- **AuthContext**: 用户认证状态管理
- **ThemeContext**: 主题切换状态管理

### 自定义 Hooks
- **useApi**: API数据获取和错误处理
- **useMediaQuery**: 响应式断点检测
- **useToast**: 全局通知消息管理

## 开发和构建

### 开发命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint

# 预览构建结果
npm run preview
```

### 环境要求
- Node.js 18.0.0+
- pnpm 8.0.0+（推荐包管理器）

## 特色功能

### 1. 实时数据更新
- 自动定时刷新系统状态
- 实时监控数据源同步状态
- 动态更新API统计信息

### 2. 响应式设计
- 移动端优化的触摸交互
- 自适应不同屏幕尺寸
- 优雅的布局降级策略

### 3. 用户体验优化
- 加载状态指示器
- 错误处理和用户反馈
- 快捷操作和批量功能

### 4. 数据可视化
- 统计图表和趋势分析
- 进度条和状态指示器
- 色彩编码的状态展示

---

*该文档描述了 ipInsight Dashboard 的完整页面结构和功能特性，为开发者和用户提供全面的界面导航指南。*